/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.crm.service;

import static com.hazelcast.com.fasterxml.jackson.core.io.NumberInput.parseInt;
import static com.stpl.tech.kettle.crm.core.CustomerServiceConstants.API_VERSION;
import static com.stpl.tech.kettle.crm.core.CustomerServiceConstants.CUSTOMER_SERVICES_ROOT_CONTEXT;
import static com.stpl.tech.kettle.crm.core.CustomerServiceConstants.SEPARATOR;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.MediaType;

import com.stpl.tech.kettle.data.model.CancelOrderInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.multipart.MultipartFile;
import com.stpl.tech.kettle.clevertap.domain.model.CleverTapPushResponse;
import com.stpl.tech.kettle.clevertap.service.CleverTapDataPushService;
import com.stpl.tech.kettle.clevertap.util.CleverTapConstants;
import com.stpl.tech.kettle.clm.service.ClevertapAttributesService;
import com.stpl.tech.kettle.core.LoyaltyEventType;
import com.stpl.tech.kettle.core.cache.UnitSessionCache;
import com.stpl.tech.kettle.core.cache.UnitSessionDetail;
import com.stpl.tech.kettle.core.cache.UnitTerminalDetail;
import com.stpl.tech.kettle.core.data.vo.CustomerFeedbackData;
import com.stpl.tech.kettle.core.data.vo.CustomerNpsData;
import com.stpl.tech.kettle.core.data.vo.CustomerTransactionData;
import com.stpl.tech.kettle.core.data.vo.CustomerVisitInfo;
import com.stpl.tech.kettle.core.exception.CardValidationException;
import com.stpl.tech.kettle.crm.model.CustomerLoginData;
import com.stpl.tech.kettle.crm.model.EmployeeLoginData;
import com.stpl.tech.kettle.customer.service.AuthorizationService;
import com.stpl.tech.kettle.customer.service.CustomerService;
import com.stpl.tech.kettle.customer.service.LoyaltyService;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.data.model.CustomerInfo;
import com.stpl.tech.kettle.data.model.LoyaltyScore;
import com.stpl.tech.kettle.datalake.service.CustomerReportService;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.ObjectFactory;
import com.stpl.tech.master.core.exception.AuthenticationFailureException;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.external.notification.service.NotificationService;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.util.AppConstants;

@Controller
@RequestMapping(value = API_VERSION + SEPARATOR + CUSTOMER_SERVICES_ROOT_CONTEXT)
public class CustomerResources extends CustomerManagementResources {

    @Autowired
    protected CustomerService customerService;

    @Autowired
    private NotificationService notificationService;

    @Autowired
    protected AuthorizationService authorizatonDao;

    @Autowired
    protected LoyaltyService loyaltyService;

    @Autowired
    protected CleverTapDataPushService cleverTapDataPushService;
    @Autowired
    private CustomerReportService customerReportService;

    @Autowired
    protected EnvironmentProperties enviournment;

    @Autowired
    private ApplicationContext applicationContext;
    
    @Autowired
    private ClevertapAttributesService clevertapAttributesService;

    private static final ObjectFactory objectFactory = new ObjectFactory();

    private static final Logger LOG = LoggerFactory.getLogger(CustomerResources.class);

    /**
     * Method handling HTTP GET requests. The returned object will be sent to the
     * client as "text/plain" media type.
     *
     * @return String that will be returned as a text/plain response.
     * @throws DataUpdationException
     */
    @RequestMapping(method = RequestMethod.POST, value = "lookup", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public UnitSessionDetail search(@RequestBody final CustomerLoginData userSession) throws DataUpdationException {
        LOG.info(String.format("Got request for a customer with contact number ## %s from unit %d ",
            userSession.getContactNumber(), userSession.getUnit()));
        // validate phone number here
        boolean sentOtp = false;
        UnitSessionDetail detail = UnitSessionCache.getInstance()
            .get(new UnitTerminalDetail(userSession.getUnit(), userSession.getTerminalId()));
        Customer customer = customerService.getCustomer(userSession.getContactNumber());
        if (customer == null) {
            customer = customerService.addCustomer(getCustomer(detail.getUnitId(), userSession.getContactNumber()));
            sentOtp = sendCustomerAuthorizationOTP(enviournment.getSendOTPLastFourDigits(),
                userSession.getContactNumber());
            detail.setNewCustomer(true);
            cleverTapDataPushService.pushUserToCleverTap(customer.getId());
        } else {
            if (customer.isInternal()) {
                throw new DataUpdationException(
                    "Customer With Contact Number " + userSession.getContactNumber() + " is an Internal Employee");
            }
            if (!customer.isContactNumberVerified()) {
                sentOtp = sendCustomerAuthorizationOTP(enviournment.getSendOTPLastFourDigits(),
                    userSession.getContactNumber());
            }
            detail.setNewCustomer(false);
        }
        detail.setCustomer(customer);
        detail.setSentOtp(sentOtp);
        return detail;
    }

    @RequestMapping(method = RequestMethod.POST, value = "generate/otp", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Boolean generateOtp(@RequestBody final CustomerLoginData userSession) throws DataUpdationException {
        LOG.info(String.format("Got generate otp request for a customer with contact number ## %s from unit %d",
            userSession.getContactNumber(), userSession.getUnit()));
        return sendRedemptionOTP(userSession);
    }

    @RequestMapping(method = RequestMethod.POST, value = "generate/complimentary/order/otp", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Boolean generateComplimentaryOrderOtp(@RequestBody final EmployeeLoginData userSession) throws DataUpdationException {
        LOG.info(String.format("Got generate  complimentary order otp request for a customer with contact number ## %s from unit %d",
                userSession.getAmContactNumber(), userSession.getUnit()));
        return sendComplimentaryOrderRedemptionOTP(userSession);
    }

    @RequestMapping(method = RequestMethod.POST, value = "generate/servicecharge/order/otp", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Boolean generateServiceChargeOtp(@RequestBody final EmployeeLoginData userSession) throws DataUpdationException {
        LOG.info(String.format("Got generate  servicecharge order otp request for a customer with contact number ## %s from unit %d",
                userSession.getAmContactNumber(), userSession.getUnit()));
        return sendServiceChargeOTP(userSession);
    }

    @RequestMapping(method = RequestMethod.POST, value = "generate/cancel/order/otp", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Boolean generateCancelOrderOtp(@RequestBody final CancelOrderInfo cancelOrderInfo) throws DataUpdationException {
        LOG.info(String.format("Got generate cancel order otp request for a customer with contact number ## %s from unit %d",
                cancelOrderInfo.getContactNumber(), cancelOrderInfo.getUnit()));
        return sendCancelOrderOTP(cancelOrderInfo);
    }

    @RequestMapping(method = RequestMethod.POST, value = "verify/signup", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Boolean verifyContactNumber(@RequestBody final CustomerLoginData userSession) throws DataUpdationException {
        LOG.info(String.format(
            "Got verify otp request for a customer to verify signup with contact number ## %s from unit %d and otp %s",
            userSession.getContactNumber(), userSession.getUnit(), userSession.getOtpPin()));
        boolean result = verifyOTPInCurrentSession(userSession.getOtpPin(), userSession.getContactNumber());
        if (result) {
            customerService.verifyContactNumber(userSession.getContactNumber());
        }
        return result;
    }

    @RequestMapping(method = RequestMethod.POST, value = "verify/otp", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Boolean verifyOTP(@RequestBody final CustomerLoginData userSession) throws DataUpdationException {
        LOG.info(
            String.format("Got verify otp request for a customer with contact number ## %s from unit %d and otp %s",
                userSession.getContactNumber(), userSession.getUnit(), userSession.getOtpPin()));
        return verifyOTPInCurrentSession(userSession.getOtpPin(), userSession.getContactNumber());
    }

    private final Customer getCustomer(int unitId, String contactNumber) {
        Customer customer = objectFactory.createCustomer();
        customer.setContactNumber(contactNumber);
        customer.setCountryCode(AppConstants.DEFAULT_COUNTRY_CODE);
        customer.setRegistrationUnitId(unitId);
        customer.setAcquisitionSource(UnitCategory.CAFE.name());
        customer.setAcquisitionToken("CHAAYOS_LOYALTY");
        return customer;
    }

    @RequestMapping(method = RequestMethod.POST, value = "update", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public UnitSessionDetail update(@RequestBody final UnitSessionDetail userSession) throws DataNotFoundException ,DataUpdationException{
        LOG.info(String.format(
            "Got request for updating customer information with contact number ## %s from unit %d with emailId %s",
            userSession.getCustomer().getContactNumber(), userSession.getUnitId(),
            userSession.getCustomer().getEmailId()));
        customerService.updateBasicCustomerInfo(userSession.getCustomer());
        UnitSessionDetail detail = UnitSessionCache.getInstance()
            .get(new UnitTerminalDetail(userSession.getUnitId(), userSession.getTerminalId()));
        Customer updatedCustomer = customerService.getCustomer(userSession.getCustomer().getId());
        detail.setCustomer(updatedCustomer);

        // Updating customer data on clevertap
        cleverTapDataPushService.pushUserToCleverTap(updatedCustomer.getId());

        detail.setRedeemedProductId(userSession.getRedeemedProductId());
        return detail;
    }

    /**
     * This service is for directly updating Customer Loyalty points without any
     * order. TODO create UI to access this service via Administrator console Order
     * productId is sent as NULL
     *
     * @param userSession
     * @return
     * @throws DataNotFoundException
     */
    @RequestMapping(method = RequestMethod.POST, value = "update/score", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public UnitSessionDetail updateScore(@RequestBody final UnitSessionDetail userSession)
        throws DataNotFoundException {
        LOG.info(String.format(
            "Got request for updating customer loyalty score with contact number ## %s from unit %d with emailId %s",
            userSession.getCustomer().getContactNumber(), userSession.getUnitId(),
            userSession.getCustomer().getEmailId()));
        UnitSessionDetail detail = UnitSessionCache.getInstance()
            .get(new UnitTerminalDetail(userSession.getUnitId(), userSession.getTerminalId()));
        detail.setPointsRedeemedSuccessfully(loyaltyService.createEvent(userSession.getCustomer().getId(),
            LoyaltyEventType.REGULAR_REDEMPTION_VERIFICATION, userSession.getLoyaltyPoints(), null, false, false));
        detail.setLoyaltyPoints(userSession.getLoyaltyPoints());
        detail.setRedeemedProductId(userSession.getRedeemedProductId());
        detail.setCustomer(customerService.getCustomer(userSession.getCustomer().getId()));
        return detail;
    }

    @RequestMapping(method = RequestMethod.GET, value = "profile/loyalty", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public LoyaltyScore getLoyaltyScore(@RequestParam int customerId) {
        return loyaltyService.getScore(customerId);
    }

    @Override
    public AuthorizationService getAuthorizationService() {
        return authorizatonDao;
    }

    @RequestMapping(method = RequestMethod.POST, value = "profile/lookup", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public CustomerTransactionData getCustomerTransactionInfo(@RequestBody final String contactNumber)
            throws AuthenticationFailureException, DataNotFoundException, CardValidationException {
        LOG.debug(String.format("Got request for Customer Transaction Info. using contact number ## %s", contactNumber));
        return customerService.getCustomerTransactionInfo(contactNumber);
    }

    @RequestMapping(method = RequestMethod.POST, value = "profile/view", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Customer viewCustomer(@RequestBody final String contactNumber)
        throws AuthenticationFailureException, DataNotFoundException {
        LOG.debug(String.format("Got request view customer using contact number ## %s", contactNumber));
        return customerService.viewCustomer(contactNumber);
    }

    @RequestMapping(method = RequestMethod.POST, value = "profile/blacklist", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean blacklistCustomer(@RequestBody final int customerId)
        throws AuthenticationFailureException, DataNotFoundException {
        LOG.info(String.format("Got request blacklist customer using customer id ## %d", customerId));
        return customerService.blacklistCustomer(customerId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "profile/blacklist/remove", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean whitelistCustomer(@RequestBody final int customerId)
        throws AuthenticationFailureException, DataNotFoundException {
        LOG.info(String.format("Got request blacklist customer using customer id ## %d", customerId));
        return customerService.whitelistCustomer(customerId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "logout", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Boolean logoutCustomer(@RequestBody final CustomerLoginData customerLoginData)
        throws AuthenticationFailureException, DataNotFoundException {
        LOG.info(String.format("Logging out customer, at Unit: " + customerLoginData.getUnit() + " and terminal: "
            + customerLoginData.getTerminalId()));
        UnitSessionCache.getInstance()
            .generateToken(new UnitTerminalDetail(customerLoginData.getUnit(), customerLoginData.getTerminalId()));
        return true;
    }

    @RequestMapping(method = RequestMethod.POST, value = "lookup/address", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Customer lookupCustomerByDeliveryAddress(@RequestBody final Integer deliveryAddressId)
        throws AuthenticationFailureException, DataNotFoundException {
        return customerService.getCustomerByDeliveryAddress(deliveryAddressId);
    }

    @RequestMapping(method = RequestMethod.GET, value = "nps-detail-require/{customerId}", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public CustomerNpsData getNpsForCustomerDetail(@PathVariable Integer customerId) throws DataNotFoundException {
        LOG.debug(" Getting NPS Feedback Data for customer {}", customerId);
        return customerReportService.getNpsForCustomerDetail(customerId);
    }

    @RequestMapping(method = RequestMethod.GET, value = "feedback-detail-require/{customerId}", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public CustomerFeedbackData getFeedbackForCustomerDetail(@PathVariable Integer customerId) throws DataNotFoundException {
        LOG.debug(" Getting Order Feedback Data for customer {}", customerId);
        return customerReportService.getFeedbackForCustomerDetail(customerId);
    }

    @RequestMapping(method = RequestMethod.GET, value = "customer-visit-detail", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public CustomerVisitInfo customerVisit(@RequestParam final Integer customerId, @RequestParam final Integer unitId)
        throws DataNotFoundException {
        LOG.debug("Getting customer visit info for customerId " + customerId + "unit id is " + unitId);
        CustomerVisitInfo  customerVisitInfo= customerService.customerVisit(customerId, unitId);
        return  customerService.feedbackDetail(customerVisitInfo, customerId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "update-customer-appId", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean updateCustomerAppId(@RequestParam String customerId, @RequestParam String appId) {
        LOG.info("getting request to update customer app id in kettle from app for customer :{} and app id is: {}", customerId, appId);
        if (customerId != null) {
            return customerService.updateCustomerAppId(appId, Integer.parseInt(customerId));
        }
        return false;
    }

    @Override
    public EnvironmentProperties getEnvironmentProperties() {
        return enviournment;
    }

    @Override
    public CustomerService getCustomerService() {
        return customerService;
    }

    @Override
    public NotificationService getNotificationService() {
        return notificationService;
    }

    @RequestMapping(method = RequestMethod.GET, value = "customer-recommendation/{customerId}", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<Integer> recommendedProduct(@PathVariable Integer customerId) throws DataNotFoundException {
        if (enviournment.getRecommendationProduct()) {
            LOG.debug(" finding  Recommended Products  for Customer ID {}", customerId);
            return customerReportService.customerRecommendedProduct(customerId);
        }
        List<Integer> recom = new ArrayList<>();
        recom.add(10);
        return recom;
    }

    @RequestMapping(method = RequestMethod.GET, value = "expire-signup-offer", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public  void expireSignupOffer() throws DataNotFoundException {
        customerService.expireSignupOffer(enviournment.getExpireSignupOfferDays());
    }


    @RequestMapping(method = RequestMethod.GET, value = "get-customer-card-info", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public com.stpl.tech.kettle.customer.CustomerCardInfo getCustomerCardInfo(@RequestParam Integer customerId)throws DataNotFoundException,CardValidationException {
        return customerService.getCustomerCardInfo(customerId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "upload-customer", consumes = MediaType.MULTIPART_FORM_DATA)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Map<String,Integer> uploadCustomerSheet(HttpServletRequest request,
                                              final MultipartFile file,@RequestParam String acquisitionSource) throws Exception {
        LOG.info("Request to upload customer sheet");
        Map<String,List<String>> response = customerService.uploadCustomerSheet(file,acquisitionSource);
        List<String> customerIDs = response.get("NewCustomerAdded");
        List<Integer> custIds = new ArrayList<>();
        for(String ids : customerIDs){
            custIds.add(Integer.valueOf(ids));
            if(custIds.size() == 990){
                pushToClevertapAsync(custIds);
                custIds.clear();
            }
        }
        pushToClevertapAsync(custIds);
        Map<String,Integer> responseMap = new HashMap<>();
        responseMap.put("NewCustomersAdded",custIds.size());
        responseMap.put("CustomersOptInUpdated",response.get("CustomersUpdated").size());
        responseMap.put("ValidCustomers",response.get("ValidCustomers").size());
        return responseMap;
    }
    
	@RequestMapping(method = RequestMethod.POST, value = "profile/sub", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public boolean subscribeCustomer(@RequestBody final int customerId, @RequestParam final String mode)
			throws AuthenticationFailureException, DataNotFoundException {
		LOG.info(String.format("Got request unsubscribe customer using customer id ## %d", customerId));
		CustomerInfo customer = customerService.subscribeCustomer(customerId, mode);
		if (customer != null) {
			cleverTapDataPushService.pushUserToCleverTap(customer.getCustomerId());
			long time = System.currentTimeMillis();
			clevertapAttributesService.updateCommunicationAttributes(customer.getOptWhatsapp(),
					customer.getSmsSubscriber(), customer.getCustomerId());
			LOG.info("Updating clm customer one view took {}",System.currentTimeMillis()-time);
			return true;
		}
		return false;
	}

	@RequestMapping(method = RequestMethod.POST, value = "profile/unsub", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public boolean unsubscribeCustomer(@RequestBody final int customerId, @RequestParam final String mode)
			throws AuthenticationFailureException, DataNotFoundException {
		LOG.info(String.format("Got request subscribe customer using customer id ## %d", customerId));
		CustomerInfo customer = customerService.unsubscribeCustomer(customerId, mode);
		if (customer != null) {
			cleverTapDataPushService.pushUserToCleverTap(customer.getCustomerId());
			long time = System.currentTimeMillis();
			clevertapAttributesService.updateCommunicationAttributes(customer.getOptWhatsapp(),
					customer.getSmsSubscriber(), customer.getCustomerId());
			LOG.info("Updating clm customer one view took {}",System.currentTimeMillis()-time);
			return true;
		}
		return false;
	}

    private void pushToClevertapAsync(List<Integer> custIds){
        ThreadPoolTaskExecutor executor = (ThreadPoolTaskExecutor) applicationContext
                .getBean("taskExecutor");
        executor.execute(() -> {
            LOG.info("Starting bulk push for uploaded customer lead in separate thread");
            try {
                CleverTapPushResponse clevertapResponse = cleverTapDataPushService.pushUsersToCleverTap(custIds, CleverTapConstants.BULK);
                cleverTapDataPushService.persistProfileTrack(clevertapResponse.getProfiles());
            } catch (DataNotFoundException e) {
                LOG.error("Data not found during bulk push of customer leads to clevertap",e);
            }
        });
    }
}
