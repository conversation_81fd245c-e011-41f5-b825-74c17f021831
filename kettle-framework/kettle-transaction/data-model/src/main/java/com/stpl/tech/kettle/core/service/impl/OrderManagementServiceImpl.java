/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 *
 */
package com.stpl.tech.kettle.core.service.impl;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;
import javax.jms.JMSException;
import javax.jms.MessageProducer;
import javax.jms.Session;

import com.stpl.tech.kettle.clevertap.domain.model.ClevertapChargedEventData;
import com.stpl.tech.kettle.clevertap.domain.model.EventUploadRequest;
import com.stpl.tech.kettle.clevertap.domain.model.ProfileUploadRequest;
import com.stpl.tech.kettle.core.data.vo.DelayReason;
import com.stpl.tech.kettle.core.data.vo.EmailData;
import com.stpl.tech.kettle.core.notification.ErrorNotification;
import com.stpl.tech.kettle.core.service.OrderSearchService;
import com.stpl.tech.kettle.core.notification.*;
import com.stpl.tech.kettle.data.dao.OrderSearchDao;
import com.stpl.tech.kettle.data.model.GamifiedOfferResponse;
import com.stpl.tech.kettle.data.model.OrderItemStatus;
import com.stpl.tech.kettle.data.model.OrderRefundDetail;
import com.stpl.tech.kettle.domain.model.OrderItemCancellationRequest;
import com.stpl.tech.master.core.external.notification.FireStoreNotificationType;
import com.stpl.tech.kettle.service.notification.OrderPushNotification;
import com.stpl.tech.master.core.external.notification.service.FirebaseNotificationService;
import com.stpl.tech.master.domain.model.*;
import com.stpl.tech.kettle.domain.model.OrderRefund;
import com.stpl.tech.master.recipe.model.IngredientVariantDetail;
import com.stpl.tech.master.recipe.model.RecipeDetail;
import com.stpl.tech.util.*;
import com.stpl.tech.kettle.customer.dao.CustomerInfoDao;
import com.stpl.tech.kettle.customer.dao.WalletSuggestionEventsDao;
import com.stpl.tech.kettle.customer.service.LoyaltyService;
import com.stpl.tech.kettle.data.dao.KettleReadDao;
import com.stpl.tech.kettle.data.model.LoyaltyEvents;
import com.stpl.tech.kettle.data.model.OrderFeedbackQuestionResponse;
import com.stpl.tech.kettle.data.model.WalletSuggestionEvents;
import com.stpl.tech.kettle.domain.model.OrderFeedbackQuestionType;
import com.stpl.tech.kettle.domain.model.OrderPaymentDetailData;
import com.stpl.tech.kettle.domain.model.PartnerOrderRiderStatesDetailData;
import com.stpl.tech.kettle.domain.model.WalletEventData;
import com.stpl.tech.master.core.external.refLookup.dao.RefLookupDao;
import com.stpl.tech.master.data.model.FeedbackQuestionsDetail;
import com.stpl.tech.master.data.model.FeedbackQuestionsUnitMapping;
import com.stpl.tech.master.data.model.OfferDetailData;
import com.stpl.tech.master.data.model.OfferMetadata;
import com.stpl.tech.master.data.repository.FeedBackQuestionDetailDao;
import com.stpl.tech.master.data.repository.FeedBackQuestionsUnitMappingDao;
import com.stpl.tech.master.domain.model.ListData;
import com.stpl.tech.master.domain.model.OfferMetaDataType;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.kettle.data.dao.impl.PartnerOrderRiderStatesDetailDao;
import com.stpl.tech.kettle.data.model.PartnerOrderRiderStatesDetail;
import com.stpl.tech.kettle.channelpartner.core.queue.model.OrderDeliveryStatusUpdate;
import com.stpl.tech.kettle.domain.model.PartnerOrderRiderStates;
import com.stpl.tech.util.endpoint.Endpoints;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.amazon.sqs.javamessaging.SQSSession;
import com.amazonaws.regions.Regions;
import com.google.common.base.Stopwatch;
import com.google.gson.Gson;
import com.stpl.tech.analytics.model.PartnerOrderRiderData;
import com.stpl.tech.kettle.clevertap.converter.CleverTapConverter;
import com.stpl.tech.kettle.clevertap.data.model.CleverTapProfilePushTrack;
import com.stpl.tech.kettle.clevertap.data.model.EventPushTrack;
import com.stpl.tech.kettle.clevertap.domain.model.CleverTapPushResponse;
import com.stpl.tech.kettle.clevertap.publisher.CleverTapEventPublisher;
import com.stpl.tech.kettle.clevertap.service.CleverTapDataPushService;
import com.stpl.tech.kettle.clevertap.util.CleverTapConstants;
import com.stpl.tech.kettle.clevertap.util.CleverTapEvents;
import com.stpl.tech.kettle.clm.service.SpecialOfferService;
import com.stpl.tech.kettle.commission.MonthlyAOVDetail;
import com.stpl.tech.kettle.commission.PartnerAOVRequest;
import com.stpl.tech.kettle.core.CampaignStrategy;
import com.stpl.tech.kettle.core.CustomerRepeatType;
import com.stpl.tech.kettle.core.EmailStatus;
import com.stpl.tech.kettle.core.FeedbackEventType;
import com.stpl.tech.kettle.core.FeedbackRatingType;
import com.stpl.tech.kettle.core.FeedbackSource;
import com.stpl.tech.kettle.core.OrderEmailEntryType;
import com.stpl.tech.kettle.core.TransactionConstants;
import com.stpl.tech.kettle.core.TransactionUtils;
import com.stpl.tech.kettle.core.cache.CampaignCache;
import com.stpl.tech.kettle.core.cache.MappingCache;
import com.stpl.tech.kettle.core.cache.OrderInfoCache;
import com.stpl.tech.kettle.core.cache.PartnerOrderConsiderationCache;
import com.stpl.tech.kettle.core.cache.UnitSessionCache;
import com.stpl.tech.kettle.core.cache.UnitSessionDetail;
import com.stpl.tech.kettle.core.cache.UnitTerminalDetail;
import com.stpl.tech.kettle.core.data.vo.CreateOrderResult;
import com.stpl.tech.kettle.core.data.vo.FeedbackTokenInfo;
import com.stpl.tech.kettle.core.data.vo.OrderFetchStrategy;
import com.stpl.tech.kettle.core.data.vo.PartnerDataConsiderRequest;
import com.stpl.tech.kettle.core.data.vo.PartnerDataWithOrderConsideration;
import com.stpl.tech.kettle.core.data.vo.SubscriptionProduct;
import com.stpl.tech.kettle.core.exception.CardValidationException;
import com.stpl.tech.kettle.core.notification.sms.CustomerSMSNotificationType;
import com.stpl.tech.kettle.core.service.CustomerOfferManagementService;
import com.stpl.tech.kettle.core.service.OrderManagementService;
import com.stpl.tech.kettle.core.service.OrderNotificationService;
import com.stpl.tech.kettle.customer.dao.SubscriptionPlanDao;
import com.stpl.tech.kettle.customer.service.CashBackService;
import com.stpl.tech.kettle.customer.service.CustomerService;
import com.stpl.tech.kettle.customer.service.FeedbackManagementService;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.data.converter.DataConverter;
import com.stpl.tech.kettle.data.dao.DeliveryDao;
import com.stpl.tech.kettle.data.dao.OrderManagementDao;
import com.stpl.tech.kettle.data.model.CashPacketData;
import com.stpl.tech.kettle.data.model.CustomerAdditionalDetail;
import com.stpl.tech.kettle.data.model.CustomerBrandMapping;
import com.stpl.tech.kettle.data.model.CustomerCampaignOfferDetail;
import com.stpl.tech.kettle.data.model.CustomerInfo;
import com.stpl.tech.kettle.data.model.CustomerMappingTypes;
import com.stpl.tech.kettle.data.model.DeliveryDetail;
import com.stpl.tech.kettle.data.model.FeedbackDetail;
import com.stpl.tech.kettle.data.model.FeedbackEvent;
import com.stpl.tech.kettle.data.model.FeedbackOrderItem;
import com.stpl.tech.kettle.data.model.ImageUrls;
import com.stpl.tech.kettle.data.model.LoyaltyScore;
import com.stpl.tech.kettle.data.model.MenuProductCogsDrilldown;
import com.stpl.tech.kettle.data.model.MenuProductCostData;
import com.stpl.tech.kettle.data.model.OrderComplaint;
import com.stpl.tech.kettle.data.model.OrderDetail;
import com.stpl.tech.kettle.data.model.OrderDetailForFeedback;
import com.stpl.tech.kettle.data.model.OrderInvoiceDetail;
import com.stpl.tech.kettle.data.model.OrderItemAddon;
import com.stpl.tech.kettle.data.model.OrderItemComplaint;
import com.stpl.tech.kettle.data.model.OrderStatusEvent;
import com.stpl.tech.kettle.data.model.PartnerOrderRiderDetailData;
import com.stpl.tech.kettle.data.model.RefundOptions;
import com.stpl.tech.kettle.data.model.SpecialOfferDetail;
import com.stpl.tech.kettle.data.model.SpecialOfferRequest;
import com.stpl.tech.kettle.data.model.SpecialOfferResponse;
import com.stpl.tech.kettle.data.model.SpecialOfferType;
import com.stpl.tech.kettle.data.model.SubscriptionPlan;
import com.stpl.tech.kettle.data.model.SubscriptionPlanEvent;
import com.stpl.tech.kettle.data.util.KettleUtils;
import com.stpl.tech.kettle.delivery.model.DeliveryReason;
import com.stpl.tech.kettle.delivery.model.DeliveryStatus;
import com.stpl.tech.kettle.domain.model.CreateNextOfferRequest;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.CustomerDineInView;
import com.stpl.tech.kettle.domain.model.CustomerTransactionViewEvent;
import com.stpl.tech.kettle.domain.model.DayCloseEstimateData;
import com.stpl.tech.kettle.domain.model.DiscountDetail;
import com.stpl.tech.kettle.domain.model.ExternalPartnerDetail;
import com.stpl.tech.kettle.domain.model.InAppUrlResponse;
import com.stpl.tech.kettle.domain.model.MyOfferResponse;
import com.stpl.tech.kettle.domain.model.MyOfferResponseStatus;
import com.stpl.tech.kettle.domain.model.NextOffer;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderComaplaintZomatoRequest;
import com.stpl.tech.kettle.domain.model.OrderComplaintResponse;
import com.stpl.tech.kettle.domain.model.OrderFeedbackMetadata;
import com.stpl.tech.kettle.domain.model.OrderInAppUrlResponse;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.domain.model.OrderItemConsumable;
import com.stpl.tech.kettle.domain.model.OrderItemRequest;
import com.stpl.tech.kettle.domain.model.OrderNotification;
import com.stpl.tech.kettle.domain.model.OrderStatus;
import com.stpl.tech.kettle.domain.model.OrderStatusDomain;
import com.stpl.tech.kettle.domain.model.OrderStatusUpdate;
import com.stpl.tech.kettle.domain.model.PartnerOrderStateUpdate;
import com.stpl.tech.kettle.domain.model.PartnerOrderStates;
import com.stpl.tech.kettle.domain.model.RefundOptionsRequest;
import com.stpl.tech.kettle.domain.model.RequestInvoiceDetail;
import com.stpl.tech.kettle.domain.model.SignupOfferStatus;
import com.stpl.tech.kettle.domain.model.SubscriptionOfferInfoDetail;
import com.stpl.tech.kettle.domain.model.SubscriptionViewData;
import com.stpl.tech.kettle.domain.model.TestCampaignNotificationRequest;
import com.stpl.tech.kettle.domain.model.TransitionStatus;
import com.stpl.tech.kettle.facebook.service.FacebookDataPushService;
import com.stpl.tech.master.core.WebServiceHelper;
import com.stpl.tech.master.core.data.vo.NotificationPayload;
import com.stpl.tech.master.core.exception.AuthenticationFailureException;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.cache.RecipeCache;
import com.stpl.tech.master.core.external.inventory.service.SQSNotificationService;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import com.stpl.tech.master.core.external.notification.service.NotificationService;
import com.stpl.tech.master.core.external.notification.service.SMSClientProviderService;
import com.stpl.tech.master.core.external.offer.service.OfferManagementExternalService;
import com.stpl.tech.master.core.notification.sms.SMSWebServiceClient;
import com.stpl.tech.master.core.notification.sms.SolsInfiniWebServiceClient;
import com.stpl.tech.master.data.model.CampaignDetailData;
import com.stpl.tech.master.data.model.CouponDetailData;
import com.stpl.tech.master.data.model.DeliveryCouponDetailData;
import com.stpl.tech.master.data.model.ProductImageMapping;
import com.stpl.tech.master.notification.SQSNotification;
import com.stpl.tech.spring.model.FileDetail;
import com.stpl.tech.spring.service.FileArchiveService;

/**
 * <AUTHOR>
 */
@Service
public class OrderManagementServiceImpl implements OrderManagementService {

    private static final Logger LOG = LoggerFactory.getLogger(OrderManagementServiceImpl.class);

    private static final String DATE_FORMAT = "yyyy-MM-dd";

    private MessageProducer producer;
    private SQSSession session;

    @Autowired
    private OrderNotificationService orderNotificationService;

    @Autowired
    private OrderManagementDao dao;
    @Autowired
    private OrderSearchDao orderSearchDao;
    @Autowired
    private EnvironmentProperties props;
    @Autowired
    private OrderInfoCache ordersCache;
    @Autowired
    private SimpMessagingTemplate template;
    @Autowired
    private NotificationService notificationService;
    @Autowired
    private MasterDataCache masterDataCache;
    @Autowired
    private SMSClientProviderService providerService;
    @Autowired
    private DeliveryDao deliveryDao;
    @Autowired
    private RecipeCache recipeCache;
    @Autowired
    private PartnerOrderConsiderationCache partnerOrderConsiderationCache;
    @Autowired
    private CampaignCache campaignCache;
    @Autowired
    private SubscriptionPlanDao subscriptionPlanDao;

    @Autowired
    private SQSNotificationService sqsNotificationService;

    @Autowired
    private FileArchiveService fileArchiveService;

    @Autowired
    private CashBackService cashBackService;

    @Autowired
    private FeedbackManagementService feedbackManagementService;

    @Autowired
    private CustomerOfferManagementService offerService;

    @Autowired
    private CleverTapDataPushService cleverTapDataPushService;
    @Autowired
    private FacebookDataPushService facebookDataPushService;
    @Autowired
    private OfferManagementExternalService offerExternalService;

    @Autowired
    private CustomerService customerService;

    @Autowired
    private MappingCache mappingCache;

    @Autowired
    private SpecialOfferService specialOfferService;

    @Autowired
	private EnvironmentProperties properties;

    @Autowired
	private CleverTapConverter cleverTapConverter;

    @Autowired
    private CleverTapEventPublisher cleverTapEventPublisher;

    @Autowired
    private FirebaseNotificationService firebaseNotificationService;

    @Autowired
    private PartnerOrderRiderStatesDetailDao partnerOrderRiderStatesDetailDao;

    @Autowired
    private FirebaseNotificationService fireBaseService;

    @Autowired
    private KettleReadDao kettleReadDao;

    @Autowired
    private RefLookupDao refLookupDao;

    @Autowired
    private WalletSuggestionEventsDao walletSuggestionEventsDao;

    @Autowired
    private CustomerInfoDao customerDao;
    @Autowired
    private FeedBackQuestionsUnitMappingDao feedBackQuestionsUnitMappingDao;

    @Autowired
    private FeedBackQuestionDetailDao feedBackQuestionDetailDao;

    @Autowired
    private LoyaltyService loyaltyService;

    @Autowired
    private OrderSearchService orderSearchService;


    @Autowired
    private EnvironmentProperties environmentProperties;

    @PostConstruct
    public void setQueueSessions() throws JMSException {
		LOG.info("POST-CONSTRUCT OrderManagementServiceImpl - STARTED");
        Regions region = AppUtils.getRegion(props.getEnvironmentType());
        session = SQSNotification.getInstance().getSession(region, Session.AUTO_ACKNOWLEDGE);
        producer = SQSNotification.getInstance().getProducer(session, props.getEnvironmentType().name(),
            "_ORDER_STATUS_EVENTS");
    }

    /*
     * (non-Javadoc)
     *
     * @see
     * com.stpl.tech.kettle.core.service.OrderManagementService#createOrder(com.
     * stpl.tech.kettle.domain.model.Order)
     */
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public CreateOrderResult createOrder(Order order) throws DataUpdationException {
        CreateOrderResult result = null;
        if (TransactionUtils.isCODOrder(order.getSource())) {
            result = createCODOrder(order);
        } else {
            UnitSessionDetail session = UnitSessionCache.getInstance()
                .get(new UnitTerminalDetail(order.getUnitId(), order.getTerminalId()));
            order.setGenerateOrderId(session.getGeneratedOrderId());
            if (order.getCustomerId() == null) {
                order.setCustomerId(
                    session.getCustomer() == null ? props.getDummyCustomerId() : session.getCustomer().getId());
            }
            result = dao.createOrder(order);
            if(order.isSkipLoyaltyProducts()){
                removeSecondFreeChai(order.getCustomerId(), SignupOfferStatus.EXPIRED.name());
            }
            if (order.getEnquiryItems().size() > 0) {
                dao.addOrderEnquiryItems(order, result.getOrderId());
            }
            UnitSessionCache.getInstance()
                .generateToken(new UnitTerminalDetail(session.getUnitId(), session.getTerminalId()));
        }
        return result;
    }


    @Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public SubscriptionPlan createSubscription(SubscriptionProduct subscriptionProduct, Customer customer, Integer campaignId, String source, Integer lagDays)
			throws DataUpdationException{
		SubscriptionPlan plan = dao.createSubscription(subscriptionProduct, customer, campaignId, source, lagDays);
        if(Objects.nonNull(plan)){
            Brand brand = masterDataCache.getBrandMetaData().get(AppConstants.CHAAYOS_BRAND_ID);
            sendSubscriptionPurchaseNotification(plan,brand,customer,subscriptionProduct.getProductId(),AppConstants.BAZAAR_UNIT_ID,-1);
        }
        return plan;
	}

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public SpecialOfferResponse getSpecialOffer(SpecialOfferRequest request) throws AuthenticationFailureException, DataUpdationException, JMSException, IOException {
        Customer customer = customerService.getCustomer(request.getContactNumber());
        CampaignDetailResponse response = campaignCache.getCampaignByToken(request.getCampaignToken(),AppConstants.ACTIVE);
        if(Objects.isNull(response.getCampaignId())){
            LOG.info("SPECIAL_OFFER ::: No campaign found for token ::: {}",request.getCampaignToken());
            return new SpecialOfferResponse(SpecialOfferType.NO_OFFER.name());
        }
        if((Objects.isNull(customer.getEmailId()) && Objects.nonNull(request.getEmail()) && !masterDataCache.getAllUnitsEmailId().contains(customer.getEmailId())) || (Objects.isNull(customer.getOptWhatsapp()) && Objects.nonNull(request.getWhatsappOpt()))){
            CustomerInfo info = customerService.getCustomerInfoObject(customer.getContactNumber());
            info.setEmailId(request.getEmail());
            LOG.info("SPECIAL_OFFER  ::: adding customer email ::: {}",request.getEmail());
            if(Objects.isNull(customer.getOptWhatsapp()) && Objects.nonNull(request.getWhatsappOpt())){
                info.setOptWhatsapp(AppUtils.setStatus(request.getWhatsappOpt()));
                LOG.info("SPECIAL_OFFER ::: setting whatsapp status ::: {}",AppUtils.setStatus((request.getWhatsappOpt())));
            }
            dao.update(info);
        }
        CampaignDetail masterCampaignDetail = campaignCache.getCampaign(response.getCampaignId());
        Map<Integer,SpecialOfferDetail> activeOffer = dao.getActiveOffer(request.getContactNumber(), response.getCampaignId(), "CRM_APP");
        if(Objects.nonNull(activeOffer) && (activeOffer.containsKey(response.getCampaignId()) || Objects.nonNull(getPosOffer(activeOffer)))){
            LOG.info("SPECIAL_OFFER ::: active offer found for customer with contact number : {} and utm medium : {}", request.getContactNumber(),request.getUtmMedium());
                if(activeOffer.containsKey(response.getCampaignId())){
                    return getOfferResponse(activeOffer.get(response.getCampaignId()),true);
        }else{
                    return getOfferResponse(getPosOffer(activeOffer),true);
                }
        }else{
            List<String> offerStrings = null;
            if(!AppUtils.isProd(props.getEnvironmentType())){
                offerStrings = getOfferString(customer.getId(), AppConstants.CHAAYOS_BRAND_ID);
            }
			else {
				boolean hasSubscription = customer != null && customer.getSubscriptionInfoDetail() != null
						&& customer.getSubscriptionInfoDetail().isHasSubscription();
				if (Objects.nonNull(activeOffer)) {
					SpecialOfferDetail prevOffer = (SpecialOfferDetail) activeOffer.values().toArray()[0];
					offerStrings = specialOfferService.getOfferString(AppConstants.CHAAYOS_BRAND_ID, customer.getId(),
							request.getUtmSource(), prevOffer.getUtmSource(), request.getUtmMedium(),
							prevOffer.getUtmMedium(), hasSubscription);
				} else {
					offerStrings = specialOfferService.getOfferString(AppConstants.CHAAYOS_BRAND_ID, customer.getId(),
							request.getUtmSource(), null, request.getUtmMedium(), null, hasSubscription);
				}
			}
            String[] offerDetail;
            if("APP".equals(request.getUtmSource()) && "CRM_APP".equals(request.getUtmMedium()) && offerStrings.size() > 1){
                offerDetail = offerStrings.get(1).split("#");
                LOG.info("SPECIAL_OFFER  ::: creating offer for offer string ::: {}",offerStrings.get(1));
            }else{
                offerDetail = offerStrings.get(0).split("#");
                LOG.info("SPECIAL_OFFER  ::: creating offer for offer string ::: {}",offerStrings.get(0));
            }
            if(SpecialOfferType.MEMBERSHIP.name().equals(offerDetail[0])){
                LOG.info("SPECIAL_OFFER :::: Creating MEMBERSHIP offer");
                SubscriptionPlan subscriptionDetail = subscriptionPlanDao.getActiveSubscription(customer.getId());
                if(Objects.nonNull(subscriptionDetail)){
                    addSpecialOfferData(SpecialOfferType.NO_OFFER,null,masterCampaignDetail,customer,request);
                    return new SpecialOfferResponse(SpecialOfferType.NO_OFFER.name());
                }
                SubscriptionProduct product = getSubscriptionProduct(offerDetail[1],new BigDecimal(offerDetail[2]), Integer.valueOf(offerDetail[3]));
                SubscriptionPlan plan = createSubscription(product, customer, masterCampaignDetail.getCampaignId(), request.getUtmSource(), Integer.valueOf(offerDetail[4]));
                if(Objects.nonNull(plan)){
                    LOG.info("SPECIAL_OFFER  ::: Subscription plan found with subscription plan id :: {} and plan code :: {}",plan.getSubscriptionPlanId(), plan.getSubscriptionPlanCode());
                    SpecialOfferDetail specialOfferDetail = addSpecialOfferData(SpecialOfferType.MEMBERSHIP,plan, masterCampaignDetail,customer,request);
                    return getOfferResponse(specialOfferDetail,false);
                }
                LOG.info("SPECIAL_OFFER  ::: No offer created ");
                addSpecialOfferData(SpecialOfferType.NO_OFFER,null,masterCampaignDetail,customer,request);
                return new SpecialOfferResponse(SpecialOfferType.NO_OFFER.name());
            }else if(SpecialOfferType.CHAAYOS_CASH.name().equals(offerDetail[0])){
                LOG.info("SPECIAL_OFFER :::: Creating CHAAYOS_CASH  offer");
                CashPacketData data = cashBackService.allotCashAsFreeGift(new BigDecimal(offerDetail[1]),customer.getContactNumber(),Integer.valueOf(offerDetail[2]),Integer.valueOf(offerDetail[3]),customer);
                if(Objects.nonNull(data)){
                    LOG.info("SPECIAL_OFFER ::: chaayos cash offer create with cash packet id ::: {}", data.getCashPacketId());
                    SpecialOfferDetail specialOfferDetail = addSpecialOfferData(SpecialOfferType.CHAAYOS_CASH,data, masterCampaignDetail,customer,request);
                    return getOfferResponse(specialOfferDetail,false);
                }
                LOG.info("SPECIAL_OFFER  ::: No offer created ");
                addSpecialOfferData(SpecialOfferType.NO_OFFER,null,masterCampaignDetail,customer,request);
                return new SpecialOfferResponse(SpecialOfferType.NO_OFFER.name());
            }else if(SpecialOfferType.NBO.name().equals(offerDetail[0])){
                LOG.info("SPECIAL_OFFER :::: Creating NBO offer");
                CampaignDetail campaignDetail = campaignCache.getCampaign(Integer.parseInt(offerDetail[1]));
                CreateNextOfferRequest offerRequest = new CreateNextOfferRequest(customer.getId(), customer.getContactNumber(), AppConstants.CHAAYOS_BRAND_ID, campaignDetail.getCampaignId(),request.getUtmSource(),request.getUtmMedium());
                NextOffer offerResponse = createGeneralOffer(offerRequest,customer, campaignDetail,Integer.valueOf(offerDetail[2])).getValue();
                if(Objects.nonNull(offerResponse)){
                    LOG.info("SPECIAL_OFFER ::: Created NBO offer with id ::: {}", offerResponse.getCustomerCampaignOfferDetailId());
                    SpecialOfferDetail specialOfferDetail = addSpecialOfferData(SpecialOfferType.NBO,offerResponse, masterCampaignDetail,customer,request);
                    return getOfferResponse(specialOfferDetail,false);
                }
                LOG.info("SPECIAL_OFFER  ::: No offer created ");
                addSpecialOfferData(SpecialOfferType.NO_OFFER,null,masterCampaignDetail,customer,request);
                return new SpecialOfferResponse(SpecialOfferType.NO_OFFER.name());
            }else if(SpecialOfferType.DNBO.name().equals(offerDetail[0])){
                LOG.info("SPECIAL_OFFER :::: Creating DNBO offer");
                CampaignDetail campaignDetail = campaignCache.getCampaign(Integer.parseInt(offerDetail[1]));
                CreateNextOfferRequest offerRequest = new CreateNextOfferRequest(customer.getId(), customer.getContactNumber(), AppConstants.CHAAYOS_BRAND_ID, campaignDetail.getCampaignId(),request.getUtmSource(),request.getUtmMedium());
                NextOffer offerResponse = createDeliveryGeneralOffer(offerRequest,customer, campaignDetail,Integer.valueOf(offerDetail[2])).getValue();
                if(Objects.nonNull(offerResponse)){
                    LOG.info("SPECIAL_OFFER ::: Created DNBO offer with id ::: {}", offerResponse.getCustomerCampaignOfferDetailId());
                    SpecialOfferDetail specialOfferDetail = addSpecialOfferData(SpecialOfferType.DNBO,offerResponse, masterCampaignDetail,customer,request);
                    return getOfferResponse(specialOfferDetail,false);
                }
                LOG.info("SPECIAL_OFFER  ::: No offer created ");
                addSpecialOfferData(SpecialOfferType.NO_OFFER,null,masterCampaignDetail,customer,request);
                return new SpecialOfferResponse(SpecialOfferType.NO_OFFER.name());
            }
        }
        LOG.info("SPECIAL_OFFER  ::: No offer created ");
        addSpecialOfferData(SpecialOfferType.NO_OFFER,null,null,null,null);
        return new SpecialOfferResponse(SpecialOfferType.NO_OFFER.name());
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean isSpecialOfferExist(String contact, String token) {
        CampaignDetailResponse response = campaignCache.getCampaignByToken(token, AppConstants.ACTIVE);
        if(Objects.isNull(response)){
            LOG.info("No Active found for this token : {}",token);
            return false;
        }
        return dao.isSpecialOfferExist(contact, response.getCampaignId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean isGamifiedOfferExist(String contact, String token, String source) {
        CampaignDetailResponse response = campaignCache.getCampaignByToken(token, AppConstants.ACTIVE);
        if(Objects.isNull(response)){
            LOG.info("No Active found for this token : {}",token);
            return false;
        }
        return dao.isGamifiedOfferExist(contact, response.getCampaignId(), source);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void revertSubscriptionSaving(int id, BigDecimal savings){
        SubscriptionPlan subscriptionPlan = subscriptionPlanDao.getActiveSubscription(id);
        if(Objects.nonNull(subscriptionPlan) && Objects.nonNull(subscriptionPlan.getOverAllSaving()) && BigDecimal.ZERO.compareTo(subscriptionPlan.getOverAllSaving())<0){
            subscriptionPlan.setOverAllSaving(AppUtils.subtract(subscriptionPlan.getOverAllSaving(),savings));
//            subscriptionPlan.setEventType(SubscriptionEventType.SUBSCRIPTION_CANCELLED.name());
            subscriptionPlanDao.update(subscriptionPlan);
            revertSubscriptionEventSaving(subscriptionPlan,savings);
        }
    }


    private void revertSubscriptionEventSaving(SubscriptionPlan plan, BigDecimal savings){
        SubscriptionPlanEvent subscriptionPlanEvent = subscriptionPlanDao.getActiveSubscriptionEvent(plan);
        if(Objects.nonNull(subscriptionPlanEvent.getSubscriptionSavings()) &&
                BigDecimal.ZERO.compareTo(subscriptionPlanEvent.getSubscriptionSavings())<0){
            subscriptionPlanEvent.setSubscriptionSavings(AppUtils.subtract(subscriptionPlanEvent.getSubscriptionSavings(),savings));
            subscriptionPlanDao.update(subscriptionPlanEvent);
        }

    }



    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void resetOverallFrequency(Order orderDetail) {
        dao.resetOverallFrequency(orderDetail);
    }


    private SubscriptionProduct getSubscriptionProduct(String planCode,BigDecimal price,Integer validityInDays){
        Product product = masterDataCache.getSubscriptionSkuCodeDetail(planCode).getValue();
        SubscriptionProduct subscriptionProduct = new SubscriptionProduct();
        subscriptionProduct.setProductId(product.getId());
        subscriptionProduct.setProductName(product.getName());
        subscriptionProduct.setSubscriptionCode(planCode);
        subscriptionProduct.setDimensionCode("NONE");
        subscriptionProduct.setPrice(price);
        subscriptionProduct.setValidityInDays(validityInDays);
        return subscriptionProduct;
    }

    private List<String> getOfferString(Integer customerId, Integer brandId){
        return dao.getOfferStrings(customerId,brandId);
    }

    private SpecialOfferDetail getPosOffer(Map<Integer,SpecialOfferDetail> map){
        for(Map.Entry<Integer,SpecialOfferDetail> entry : map.entrySet()){
            if("CRM_APP".equals(entry.getValue().getUtmMedium())){
                return entry.getValue();
            }
        }
        return null;
    }

    private SpecialOfferDetail addSpecialOfferData(SpecialOfferType type,Object data,CampaignDetail campaignDetail, Customer customer, SpecialOfferRequest request){
        try {
            SpecialOfferDetail detail = new SpecialOfferDetail();
            if(SpecialOfferType.MEMBERSHIP.equals(type)){
                SubscriptionPlan plan = (SubscriptionPlan) data;
                CouponDetail couponDetail = masterDataCache.getSubscriptionSkuCodeDetail(plan.getSubscriptionPlanCode()).getKey();
                detail.setCouponCode(plan.getSubscriptionPlanCode());
                detail.setOfferText(couponDetail.getOffer().getText());
                detail.setStartDate(AppUtils.getDateString(plan.getPlanStartDate(), AppUtils.DATE_FORMAT_STRING));
                detail.setEndDate(AppUtils.getDateString(plan.getPlanEndDate(), AppUtils.DATE_FORMAT_STRING));
                detail.setSubscriptionPlanId(plan.getSubscriptionPlanId());
            detail.setTnc(couponDetail.getOffer().getTermsAndConditions());
            }else if(SpecialOfferType.NBO.equals(type) || SpecialOfferType.DNBO.equals(type)){
                NextOffer nextOffer = (NextOffer) data;
                detail.setCustomerCampaignOfferDetailId(nextOffer.getCustomerCampaignOfferDetailId());
                detail.setMaxUsage(nextOffer.getMaxUsage());
                detail.setStartDate(nextOffer.getValidityFrom());
                detail.setEndDate(nextOffer.getValidityTill());
                detail.setCouponCode(nextOffer.getOfferCode());
                detail.setOfferText(nextOffer.getText());
            detail.setTnc(nextOffer.getTnc());
            }else if(SpecialOfferType.CHAAYOS_CASH.equals(type)){
                CashPacketData packetData = (CashPacketData) data;
                detail.setCashAmount(packetData.getInitialAmount());
                detail.setCouponCode("CHAAYOS_CASH");
                detail.setStartDate(AppUtils.getDateString(packetData.getActivationTime(), AppUtils.DATE_FORMAT_STRING));
                detail.setEndDate(AppUtils.getDateString(packetData.getExpirationDate(), AppUtils.DATE_FORMAT_STRING));
                detail.setCashPacketId(packetData.getCashPacketId());
                detail.setTnc(campaignCache.getCouponDetail("CHAAYOS_CASH").getOffer().getTermsAndConditions());
            }
            detail.setOfferType(type.name());
            detail.setRecordStatus(AppConstants.ACTIVE);
            detail.setCampaignStrategy(campaignDetail.getCampaignStrategy());
            detail.setCampaignId(campaignDetail.getCampaignId());
            detail.setContactNumber(customer.getContactNumber());
            detail.setCustomerId(customer.getId());
            detail.setUtmMedium(request.getUtmMedium());
            detail.setUtmSource(request.getUtmSource());
            detail.setRecordTime(AppUtils.getCurrentTimestamp());
            detail.setFlow(request.getFlow());
            dao.add(detail);
            return detail;
        }catch (Exception e){
            LOG.error("Error while adding offer ",e);
        }
        return null;
    }

    private SpecialOfferResponse getOfferResponse( SpecialOfferDetail offerDetail, Boolean isExistingOffer){
        if(Objects.isNull(offerDetail)){
            return new SpecialOfferResponse(SpecialOfferType.NO_OFFER.name());
        }
        SpecialOfferResponse response = new SpecialOfferResponse();
        if(SpecialOfferType.NBO.name().equals(offerDetail.getOfferType()) || SpecialOfferType.DNBO.name().equals(offerDetail.getOfferType())){
            response.setMaxUsage(offerDetail.getMaxUsage());
        }else if(SpecialOfferType.CHAAYOS_CASH.name().equals(offerDetail.getOfferType())){
            response.setChaayosCash(offerDetail.getCashAmount());
        }
        response.setOfferCode(offerDetail.getCouponCode());
        response.setText(offerDetail.getOfferText());
        response.setValidityFrom(offerDetail.getStartDate());
        response.setValidityTill(offerDetail.getEndDate());
        response.setExistingOffer(isExistingOffer);
        response.setOfferType(offerDetail.getOfferType());
        response.setOfferTnCString(offerDetail.getTnc());
        return response;
    }

    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean createOrderEnquiry(Order order) {
        return dao.addOrderEnquiryItems(order, null);
    }

    /*
     * (non-Javadoc)
     *
     * @see
     * com.stpl.tech.kettle.core.service.OrderManagementService#createOrder(com.
     * stpl.tech.kettle.domain.model.Order)
     */
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public CreateOrderResult createCODOrder(Order order) throws DataUpdationException {
        order.setGenerateOrderId(AppUtils.generateRandomOrderId());
        return dao.createOrder(order);
    }

    /*
     * (non-Javadoc)
     *
     * @see
     * com.stpl.tech.kettle.core.service.OrderManagementService#updateOrder(com.
     * stpl.tech.kettle.domain.model.Order)
     */
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updateOrder(Order order) throws DataUpdationException {
        return dao.updateOrder(order);
    }

    /*
     * (non-Javadoc)
     *
     * @see
     * com.stpl.tech.kettle.core.service.OrderManagementService#deleteOrder(int,
     * java.lang.String)
     */
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean deleteOrder(int unitId, String generatedOrderId, int cancelledBy, int cancelApprovedBy,
                               String reason, Integer reasonId, String bookWastage)
        throws DataUpdationException, DataNotFoundException, CardValidationException {
        OrderStatusEvent ose = dao.deleteOrder(unitId, generatedOrderId, cancelledBy, cancelApprovedBy, reason,
            reasonId, bookWastage);
        publishOrderStatusEvent(ose);
        return TransitionStatus.SUCCESS.name().equals(ose.getTransitionStatus());
    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.kettle.core.service.OrderManagementService#
     * generateOrderEmailEvent(int, int, java.lang.String, boolean)
     *
     * @Transactional(rollbackFor=Exception.class, value =
     * "TransactionDataSourceTM", readOnly = false, propagation =
     * Propagation.REQUIRED) public void generateOrderEmailEvent(int orderId, int
     * retryCount, String emailId, boolean isSystemGenerated, boolean
     * isEmailVerified) { dao.generateOrderEmailEvent(orderId, retryCount, emailId,
     * isSystemGenerated, isEmailVerified); }
     *
     *
     * (non-Javadoc)
     *
     * @see com.stpl.tech.kettle.core.service.OrderManagementService#
     * generateOrderEmailEvent(int, int, int, boolean)
     *
     * @Transactional(rollbackFor=Exception.class, value =
     * "TransactionDataSourceTM", readOnly = false, propagation =
     * Propagation.REQUIRED) public void generateOrderEmailEvent(int orderId, int
     * retryCount, int customerId, boolean isSystemGenerated) throws
     * DataNotFoundException { dao.generateOrderEmailEvent(orderId, retryCount,
     * customerId, isSystemGenerated);
     *
     * }
     */

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.kettle.core.service.OrderManagementService#updateStatus(
     * int, com.stpl.tech.kettle.core.EmailStatus, java.lang.String)
     */
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updateStatus(int orderEmailId, EmailStatus status, String errorMessage) {
        return dao.updateStatus(orderEmailId, status, errorMessage);
    }

    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void addReprintRequest(int orderId, int generatedBy, int approvedBy, String reason) {
        dao.addReprintRequest(orderId, generatedBy, approvedBy, reason);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void generateOrderEmailEvent(OrderEmailEntryType type, int orderId, int retryCount, String emailId,
                                        boolean isSystemGenerated, boolean isEmailVerified, Date currentTimestamp) {
        dao.generateOrderEmailEvent(type, orderId, retryCount, emailId, isSystemGenerated, isEmailVerified,
            currentTimestamp, null);
    }

    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void generateOrderEmailEvent(OrderEmailEntryType type, int orderId, int retryCount, Customer customer,
                                        boolean isSystemGenerated) {
        dao.generateOrderEmailEvent(type, orderId, retryCount, customer, isSystemGenerated,
            AppUtils.getCurrentTimestamp());
    }

    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public OrderStatusEvent updateOrderStatus(Integer orderId, OrderStatus orderStatus, int approvedBy, int generatedBy,
                                              int unitId, String reason, Boolean refund, Integer reasonId, String bookWastage) {
        return dao.updateOrderStatus(orderId, orderStatus, approvedBy, generatedBy, unitId, reason, refund, reasonId,
            bookWastage);
    }

    @Override
    public NewOrderNotification fetchNewOrders(UnitOrder unitOrder) throws DataNotFoundException {
        NewOrderNotification orderNotification = new NewOrderNotification();
        Set<Integer> currentOrders = new HashSet<>();
        if (unitOrder.getOrders() != null) {
            unitOrder.getOrders().forEach((o) -> {
                currentOrders.add(o.getOrderId());
            });
        }
        List<OrderInfo> orders = ordersCache.getOrders(unitOrder.getUnitId());
        if (orders != null && !orders.isEmpty()) {
            orders.forEach(o -> {
                if (!currentOrders.contains(o.getOrder().getOrderId())
                    || OrderStatus.CANCELLED_REQUESTED.equals(o.getOrder().getStatus())) {
                    orderNotification.getOrders().add(o);
                }
            });
        }
        return orderNotification;
    }

    @Override
    @Deprecated
    public NewOrderNotification fetchNewOrders(UnitOrderOld unitOrder) throws DataNotFoundException {
        NewOrderNotification orderNotification = new NewOrderNotification();
        List<OrderInfo> orders = ordersCache.getOrders(unitOrder.getUnitId());
        if (orders != null && !orders.isEmpty()) {
            orders.forEach(o -> {
                if (!unitOrder.getOrders().contains(o.getOrder().getOrderId())
                    || OrderStatus.CANCELLED_REQUESTED.equals(o.getOrder().getStatus())) {
                    orderNotification.getOrders().add(o);
                }
            });
        }
        return orderNotification;
    }

    /*
     * (non-Javadoc)
     *
     * @see
     * com.stpl.tech.kettle.core.service.OrderManagementService#fetchNewStatuses
     * (com.stpl.tech.kettle.core.notification.UnitOrder)
     */
    @Override
    public Map<Integer, OrderStatus> fetchNewStatuses(UnitOrder unitOrder) {
        Map<Integer, OrderStatus> newStatuses = new HashMap<>();
        if (unitOrder.getOrders() != null && unitOrder.getOrders().size() > 0) {
            for (StatusData current : unitOrder.getOrders()) {
                try {
                    OrderInfo info = ordersCache.getOrderById(current.getOrderId(), current.getSource(), false);
                    if (info != null && !info.getOrder().getStatus().name().equals(current.getStatus().name())) {
                        newStatuses.put(info.getOrder().getOrderId(), info.getOrder().getStatus());
                    } else if (info == null) {

						/*
						 * LOG.info("Order Not available in Cache {}, {}, {}", current.getOrderId(),
						 * current.getSource(), current.getStatus());
						 */
                        OrderStatusEvent event = dao.getLastOrderStatusEvent(current.getOrderId());
                        if (event != null) {
                            newStatuses.put(current.getOrderId(), OrderStatus.valueOf(event.getToStatus()));
                        } else {
                            OrderDetail order = dao.find(OrderDetail.class, current.getOrderId());
                            if (OrderStatus.SETTLED.equals(OrderStatus.valueOf(order.getOrderStatus()))
                                && TransactionUtils.isCODOrder(order.getOrderSource())) {
                                newStatuses.put(current.getOrderId(), OrderStatus.DELIVERED);
                            } else {
                                newStatuses.put(current.getOrderId(), OrderStatus.valueOf(order.getOrderStatus()));
                            }
                        }
                    }
                } catch (TemplateRenderingException | DataNotFoundException e) {
                    LOG.error("Error while fetching Order " + current.getOrderId());
                }
            }
        }
        return newStatuses;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updateOrderStatusInCache(Integer orderId, OrderStatus orderStatus, Integer userId, Integer unitId,
                                            UnitCategory orderSource, String reason, Boolean refund, Integer reasonId, String bookWastage) {
        Stopwatch watch = Stopwatch.createUnstarted();
        watch.start();
        boolean updated = false;
        if(OrderStatus.SETTLED.value().equalsIgnoreCase(orderStatus.value())) {
            OrderInfo orderInfo = ordersCache.getOrder(orderId);
            if (Objects.nonNull(orderInfo)) {
                for (OrderItem oi : orderInfo.getOrder().getOrders()) {
                    if (AppConstants.YES.equalsIgnoreCase(oi.getIsHoldOn())) {
                        LOG.info("Status of Order Can be updated to settled as item on Hold");
                        return false;
                    }
                    if (Objects.nonNull(oi.getComposition()) && !CollectionUtils.isEmpty(oi.getComposition().getMenuProducts())) {
                        for (OrderItem mi : oi.getComposition().getMenuProducts()) {
                            if (AppConstants.YES.equalsIgnoreCase(mi.getIsHoldOn())) {
                                LOG.info("Status of Order Can be updated to settled as item on Hold");
                                return false;
                            }
                        }
                    }
                }
            }
        }
        OrderStatusEvent event = updateOrderStatus(orderId, orderStatus, userId, userId, unitId,
            reason != null ? reason : "", refund, reasonId, bookWastage);
        System.out.println("######### , STEP 1, - , Update Order Status ----------,"
            + watch.stop().elapsed(TimeUnit.MILLISECONDS));

        if (orderId != null && event != null && TransitionStatus.SUCCESS.name().equals(event.getTransitionStatus())) {
            try {

                watch.start();
                ordersCache.updateStatus(unitId, orderId, orderStatus, orderSource);
                System.out.println("######### , STEP 2, - , Update Order Status in Cache----------,"
                    + watch.stop().elapsed(TimeUnit.MILLISECONDS));
                watch.start();
                OrderInfo info = ordersCache.getOrderById(orderId, orderSource);
                System.out.println("######### , STEP 3, - , Get Order From cache----------,"
                    + watch.stop().elapsed(TimeUnit.MILLISECONDS));
                watch.start();
                if (info.getOrder().getChannelPartner() == AppConstants.CHANNEL_PARTNER_WEB_APP) {
                    // publish to SQS for web ordering
                    publishOrderStatusEvent(event);
                }
                System.out.println("######### , STEP 4, - , Publish Order Status To queue----------,"
                    + watch.stop().elapsed(TimeUnit.MILLISECONDS));
                watch.start();
                if (info != null
                    && info.getDeliveryDetails()
                    .getDeliveryPartnerId() == AppConstants.DELIVERY_PARTNER_CHAAYOS_DELIVERY
                    && orderStatus.equals(OrderStatus.SETTLED)) {
                    try {
                        StatusData data = new StatusData(info.getOrder().getOrderId(),
                            UnitCategory.valueOf(info.getOrder().getSource()), orderStatus);
                        publishOrderInfo(info, data, reasonId);
                        sendSDPDetailsMSgToCustomer(info);
                        updateDeliveryDetail(info.getOrder().getGenerateOrderId(), DeliveryStatus.OUT_FOR_DELIVERY,
                            null);
                        LOG.info(
                            "Settled Order status for delivery partner chaayos : Skipping the removal from cache. Published to channel");

                    } catch (Exception e) {
                        LOG.error("Some exception while publishing in order channel ::::::::: ", e);
                    }
                    System.out.println("######### , STEP 5a, - , Public Web Socket----------,"
                        + watch.stop().elapsed(TimeUnit.MILLISECONDS));
                } else if (orderStatus.equals(OrderStatus.CANCELLED) || orderStatus.equals(OrderStatus.SETTLED)
                    || orderStatus.equals(OrderStatus.CLOSED) || orderStatus.equals(OrderStatus.DELIVERED)) {
                    if (info.getDeliveryDetails()
                        .getDeliveryPartnerId() == AppConstants.DELIVERY_PARTNER_CHAAYOS_DELIVERY
                        && (orderStatus.equals(OrderStatus.DELIVERED)
                        || orderStatus.equals(OrderStatus.CANCELLED))) {
                        if (ordersCache.checkRiderPoolForOrder(info.getDeliveryDetails().getDeliveryBoyPhoneNum(),
                            orderId)) {
                            ordersCache.removeAllotedNoToRider(info.getDeliveryDetails().getDeliveryBoyPhoneNum(),
                                orderId);
                            if (orderStatus.equals(OrderStatus.DELIVERED)) {
                                StatusData data = new StatusData(info.getOrder().getOrderId(),
                                    UnitCategory.valueOf(info.getOrder().getSource()), orderStatus);
                                publishOrderInfo(info, data, reasonId);
                                updateDeliveryDetail(info.getOrder().getGenerateOrderId(), DeliveryStatus.DELIVERED,
                                    reasonId);
                                if (reasonId != null && DeliveryReason.MissedCallResponse.getReasonId() == reasonId) {
                                    sendDeliveryConfirmationMsgToCustomer(info);
                                }
                            }
                        }
                    }

                    ordersCache.removeFromCache(unitId, orderId);
                    System.out.println("######### , STEP 5b, - , Remove From cache----------,"
                        + watch.stop().elapsed(TimeUnit.MILLISECONDS));
                }
            } catch (Exception e) {
                LOG.error("Some exception while updating in order cache ::::::::: ", e);
            }
            updated = true;
        } else {
            try {
                LOG.info("GOT FAILURE TRANSACTION EVENT FOR ORDER ID : {}", orderId);
                try {
                    if (Objects.nonNull(event)) {
                        LOG.info("Returned Success Event for OrderId : {} is : {}", orderId, event.toString());
                    } else {
                        LOG.info("Returned Event IS NULL FOR ORDER ID : {}", orderId);
                    }
                    LOG.info("OrderSource for Order Id : {} is : {}", orderId, orderSource);
                    LOG.info("Status CHeck for OrderId : {} is : {}", OrderStatus.SETTLED.name(), TransitionStatus.FAILURE.name());

                    if (orderId != null && event != null && UnitCategory.COD.equals(orderSource) && OrderStatus.SETTLED.name().equalsIgnoreCase(event.getFromStatus())
                            && OrderStatus.SETTLED.name().equalsIgnoreCase(event.getToStatus()) && TransitionStatus.FAILURE.name().equals(event.getTransitionStatus())) {
                        LOG.info("FOR ORDER ID :  {} found SUCCESS EVENT returning TRUE to assembly REMOVING FROM CACHE TOO", orderId);
                        ordersCache.removeFromCache(unitId, orderId);
                        return false;
                    }
                } catch (Exception e) {
                    LOG.error("Exception Occurred While Verifying FAILURE TRANSACTION EVENT for Order Id : {}", orderId , e);
                }
                OrderInfo info = ordersCache.getOrderById(orderId, orderSource);
                OrderStatus status = info.getOrder().getStatus();
                if (status.equals(OrderStatus.CANCELLED) || status.equals(OrderStatus.SETTLED)
                    || status.equals(OrderStatus.CLOSED) || status.equals(OrderStatus.DELIVERED)) {
                    if (info.getDeliveryDetails()
                        .getDeliveryPartnerId() == AppConstants.DELIVERY_PARTNER_CHAAYOS_DELIVERY
                        && orderStatus.equals(OrderStatus.SETTLED)) {
                        // if COD order in SETTLED status don't remove from
                        // cache
                    } else {
                        if (info.getDeliveryDetails()
                            .getDeliveryPartnerId() == AppConstants.DELIVERY_PARTNER_CHAAYOS_DELIVERY
                            && (status.equals(OrderStatus.DELIVERED) || status.equals(OrderStatus.CANCELLED))) {
                            if (ordersCache.checkRiderPoolForOrder(info.getDeliveryDetails().getDeliveryBoyPhoneNum(),
                                orderId)) {
                                ordersCache.removeAllotedNoToRider(info.getDeliveryDetails().getDeliveryBoyPhoneNum(),
                                    orderId);
                            }
                        }
                        ordersCache.removeFromCache(info.getUnit().getId(), info.getOrder().getOrderId());
                    }
                }
            } catch (Exception e) {
                LOG.error("Exception during getting order from cache", e);
            }
        }
        return updated;
    }

    private void publishOrderInfo(OrderInfo info, StatusData data, Integer reasonId) {
        if (reasonId != null && (reasonId == DeliveryReason.MissedCallResponse.getReasonId()
            || reasonId == DeliveryReason.PosReset.getReasonId())) {
            info.getDeliveryDetails().setByAssemblyScreen(false);
        }
        String webSocketChannel = TransactionConstants.WEB_SOCKET_CHANNEL + info.getUnit().getId()
            + TransactionConstants.WEB_SOCKET_CHANNEL_ORDERS;
        template.convertAndSend(webSocketChannel, info);
    }

    private void publishOrderStatusEvent(OrderStatusEvent ose) {
        if (props.publishOrders() && ose != null) {
            try {
                producer.send(session.createTextMessage(JSONSerializer.toJSON(ose)));
            } catch (JMSException e) {
                LOG.error("Error while adding order to the message queue", e);
            }
        }

    }

    @Override
    public void postOrderCancellationUpdateToDineInServer(int orderId, Integer customerId, Integer partnerId) {
        publishCafeOrderStatus(orderId, customerId, partnerId, OrderStatus.CANCELLED);
    }

    @Override
    public void postUpdateActions(int orderId, Integer customerId, Integer partnerId, boolean isDelivery,
                                  OrderStatus status) {
        if (isDelivery) {
            switch (status) {
                case READY_TO_DISPATCH:
                    publishPartnerOrderStatus(orderId, PartnerOrderStates.PREPARED, true);
                    //TODO Removed Mission Chai Garam
                    //updateMissionGaramChaiCache(orderId);
                    break;
                case SETTLED:
                    publishPartnerOrderStatus(orderId, PartnerOrderStates.PICKEDUP, true);
                    break;
                case DELIVERED:
                    publishPartnerOrderStatus(orderId, PartnerOrderStates.DELIVERED, false);
                    break;
                default:
                    break;
            }
            if (partnerId == null || partnerId != AppConstants.BAZAAR_PARTNER_ID) {
                return;
            }
        }
        publishCafeOrderStatus(orderId, customerId, partnerId, status);
    }

    void publishPartnerOrderStatus(int orderId, PartnerOrderStates partnerOrderState, boolean sendSDPDetail) {

        try {
            // SQS publish for order food PREPARED call
            PartnerOrderStateUpdate partnerOrderStateUpdate = new PartnerOrderStateUpdate();
            partnerOrderStateUpdate.setOrderId(String.valueOf(orderId));
            partnerOrderStateUpdate.setState(partnerOrderState);
            if (sendSDPDetail) {
                List<DeliveryDetail> deliveryDetails = deliveryDao.getDeliveryDetail(orderId);
                if (deliveryDetails != null && !deliveryDetails.isEmpty()) {
                    partnerOrderStateUpdate
                        .setData(new Gson().toJson(new IdCodeName(0, deliveryDetails.get(0).getDeliveryBoyName(),
                            deliveryDetails.get(0).getDeliveryBoyPhoneNum())));
                }
            }
            sqsNotificationService.publishToSQS(props.getEnvironmentType().name(), partnerOrderStateUpdate,
                "_PARTNER_ORDER_STATUS");
            LOG.info("Publishing " + partnerOrderState.name() + " order status update event"
                + new Gson().toJson(partnerOrderStateUpdate));
        } catch (JMSException ex) {
            LOG.error("Error publishing " + partnerOrderState.name() + " order status update::::::::", ex);
        }
    }

    private void updateMissionGaramChaiCache(int orderId) {
        if (props.isMissionGaramChaiActive()) {
            try {
                Thread t = new Thread() {
                    @Override
                    public void run() {
                        try {
                            OrderDetail order = dao.find(OrderDetail.class, orderId);
                            if (props.getMissionGaramChaiChannelpartnerIds().contains(order.getChannelPartnerId()) && props.getMissionGaramChaiBrandIds().contains(order.getBrandId())) {
                                dao.getOrderStatusEventTime(order.getOrderId(), OrderStatus.READY_TO_DISPATCH);
                                PartnerDataConsiderRequest request = dao.getChannelPartnerOrderDetail(orderId);
                                PartnerDataWithOrderConsideration partner = setMissionGaramChai(request);
                                if (!partnerOrderConsiderationCache.getMissionGaramChai().containsKey(order.getUnitId())) {
                                    partnerOrderConsiderationCache.getMissionGaramChai().put(order.getUnitId(), new HashMap<>());
                                }
                                partner = setCountsForMissionGaramChai(partner, request, props.getMissionGaramChaiThresholdMPR(),
                                    props.getMissionGaramChaiMaximumOrderValue(), partnerOrderConsiderationCache.getMissionGaramChai().get(request.getUnitId()).containsKey(request.getPartnerId()) ? partnerOrderConsiderationCache.getMissionGaramChai().get(request.getUnitId()).get(request.getPartnerId()) : partner);
                                partnerOrderConsiderationCache.getMissionGaramChai().get(request.getUnitId()).put(request.getPartnerId(), partner);
                                LOG.info("added orderId {} in mission chai cache", orderId);
                            }
                        } catch (Exception e) {
                            LOG.error("Error updating cache for mission garam chai:" + e.getMessage());
                        }
                    }
                };
                t.start();
            } catch (Exception e) {
                LOG.error("Error  while running mission garam chai cache thread:" + e.getMessage());
            }
        }
    }


    public void publishCafeOrderStatus(int orderId, Integer customerId, Integer partnerId, OrderStatus status) {
        try {
            OrderStatusUpdate data = new OrderStatusUpdate();
            data.setOrderId(orderId);
            data.setStatus(status);
            data.setCustomerId(customerId);
            data.setPartnerId(partnerId);
            data.setDate(AppUtils.getCurrentTimestamp());
            sqsNotificationService.publishToSQS(props.getEnvironmentType().name(), data, "_APP_ORDER_STATUS",
                Regions.fromName(props.getAppOrderStatusQueueRegion()));
            LOG.info("Publishing App Order Status " + status.name() + " order status update event"
                + new Gson().toJson(data));
        } catch (JMSException ex) {
            LOG.error("Error publishing App Order Status " + status.name() + " order status update::::::::", ex);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean getPaymentStatus(String cartId) {
        return dao.getPaymentStatus(cartId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean changeSettlementMode(int unitId, int orderId, int editedBy, List<Pair<Integer, Integer>> settlements,
                                        UnitCategory orderSource) throws DataUpdationException, TemplateRenderingException, DataNotFoundException {
        boolean flag = dao.changeSettlementMode(unitId, orderId, editedBy, settlements);
        if (flag) {
            try {
                ordersCache.removeFromCache(unitId, orderId);
                ordersCache.getOrderById(orderId, orderSource);
            } catch (DataNotFoundException e) {
                LOG.error("Could not find order with id :::::", orderId);
                ordersCache.getOrderById(orderId, orderSource);
            }
        }
        return flag;
    }

    /*
     * (non-Javadoc)
     *
     * @see
     * com.stpl.tech.kettle.core.service.OrderManagementService#addCost(com.stpl
     * .tech.kettle.domain.model.Order, java.util.Collection)
     */
    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public int addCost(Order o, Collection<Consumable> values) {
        return dao.addCost(o, values);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void setWastageSumoId(int orderId, int sumoId) {
        dao.setWastageSumoId(orderId, sumoId);
    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.kettle.core.service.OrderManagementService#
     * remainingBillCount(int, int, int) Return no of bills remaining in a bill book
     * fro that unit
     */
    @Override
    public int remainingBillCount(int unitId, int startNo, int endNo) {
        return dao.remainingBillCount(unitId, startNo, endNo);
    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.kettle.core.service.OrderManagementService#
     * validateBillBookNo(int, int) Return true if given billBookNo is not already
     * used
     */
    @Override
    public int validateBillBookNo(int unitId, int billBookNo) {
        return dao.validateBillBookNo(unitId, billBookNo);
    }

    /**
     * Integrated with Sarv sms api
     *
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updateOrderStatusOnSDPCallback(String riderContactNo, String allotedNo, String reason,
                                                  int reasonId) {
        boolean result = false;
        int orderId = ordersCache.getOrderIdOfRider(riderContactNo, allotedNo);
        if (ordersCache.checkRiderPoolForOrder(riderContactNo, orderId)) {
            OrderDetail orderDetail = dao.find(OrderDetail.class, orderId);
            if (!orderDetail.getOrderStatus().equals(OrderStatus.SETTLED.value())) {
                sdpCallbackFailure(riderContactNo, allotedNo, result, orderId);
                LOG.info("Order status :" + orderDetail.getOrderStatus() + " can not be processed for order id : "
                    + orderId + " and rider contact no : " + riderContactNo + " mapping cache : "
                    + ordersCache.getSDPOrders(riderContactNo));
                return result;
            }
            if (orderDetail.getOrderStatus().equals(OrderStatus.SETTLED.value())
                && (!orderDetail.getOrderStatus().equals(OrderStatus.DELIVERED.value())
                || !orderDetail.getOrderStatus().equals(OrderStatus.CANCELLED.value()))) {

                result = updateOrderStatusInCache(orderDetail.getOrderId(), OrderStatus.DELIVERED,
                    orderDetail.getEmpId(), orderDetail.getUnitId(),
                    UnitCategory.fromValue(orderDetail.getOrderSource()), reason, false, reasonId, null);
                if (result) {
                    LOG.info("Order updated successfully with status :" + result + " orderId :" + orderId + " rider : "
                        + riderContactNo);
                    sdpCallbackFailure(riderContactNo, allotedNo, result, orderId);
                    ordersCache.removeAllotedNoToRider(riderContactNo, orderId);
                } else {
                    sdpCallbackFailure(riderContactNo, allotedNo, result, orderId);
                    LOG.info("Order updation failed with  status :" + result + " orderId :" + orderId
                        + " rider contact no : " + riderContactNo + " mapping cache : "
                        + ordersCache.getSDPOrders(riderContactNo));
                }
            }
        } else {
            sdpCallbackFailure(riderContactNo, allotedNo, result, orderId);
            LOG.info("OrderId not available in cache for alloted no : " + allotedNo + " and rider contact no : "
                + riderContactNo + " mapping cache : " + ordersCache.getSDPOrders(riderContactNo));
        }
        return result;
    }

    private boolean sendSDPDetailsMSgToCustomer(OrderInfo info) {
        try {
            String message = CustomerSMSNotificationType.DELIVERY_ALLOTED_MSG_CUSTOMER.getMessage(info);
            boolean status = notificationService
                .sendNotification(CustomerSMSNotificationType.DELIVERY_ALLOTED_MSG_CUSTOMER.name(), message,
                    info.getCustomer().getContactNumber(),
                    providerService.getSMSClient(CustomerSMSNotificationType.DELIVERY_ALLOTED_MSG_CUSTOMER
                        .getTemplate().getSMSType(), ApplicationName.KETTLE_SERVICE),
                    props.getAllotedNoToCustomerSMS(),null);
            return status;
        } catch (IOException | JMSException e) {
            LOG.error(
                "Error while sending alloted sdp no message to  customer " + info.getCustomer().getContactNumber(),
                e);
        }
        return false;
    }

    private boolean sendFreeChaiDeliveryMSgToCustomer(String contactNumber) {

        /*try {
            System.out.println(props.getFreeChaiDeliveryConfirmationOfCustomerSMS());
            String message = CustomerSMSNotificationType.FREE_CHAI_DELIVERY.getMessage(contactNumber);
            boolean status = notificationService
                .sendNotification(CustomerSMSNotificationType.FREE_CHAI_DELIVERY.name(), message,
                    contactNumber,
                    providerService.getSMSClient(CustomerSMSNotificationType.FREE_CHAI_DELIVERY
                        .getTemplate().getSMSType(), ApplicationName.KETTLE_SERVICE),
                    props.getFreeChaiDeliveryConfirmationOfCustomerSMS(),null);
            return status;
        } catch (IOException | JMSException e) {
            LOG.error(
                "Error while sending free chai message to  customer " + contactNumber,
                e);
        }*/
        return false;
    }

    private boolean sendDeliveryConfirmationMsgToCustomer(OrderInfo info) {

        try {
            String message = CustomerSMSNotificationType.DELIVERY_CONFIRMATION_MSG_CUSTOMER
                .getMessage(info.getOrder().getGenerateOrderId());
            LOG.info(" DELIVERY_CONFIRMATION_MSG_CUSTOMER  :  " + message);
            boolean status = notificationService
                .sendNotification(CustomerSMSNotificationType.DELIVERY_CONFIRMATION_MSG_CUSTOMER.name(), message,
                    info.getCustomer().getContactNumber(),
                    providerService.getSMSClient(CustomerSMSNotificationType.DELIVERY_CONFIRMATION_MSG_CUSTOMER
                        .getTemplate().getSMSType(), ApplicationName.KETTLE_SERVICE),
                    props.getDeliveryConfirmationOfCustomerSMS(),null);
            return status;
        } catch (IOException | JMSException e) {
            LOG.error("Error while sending delivery confirmation message to  customer "
                + info.getCustomer().getContactNumber(), e);
        }
        return false;
    }

    @Override
    public Map<String, String> getSDPOrderDetails(String riderContactNo)
        throws TemplateRenderingException, DataNotFoundException {
        HashMap<String, Integer> allotedNoMap = ordersCache.getSDPOrders(riderContactNo);
        Map<String, String> hmap = new HashMap<>();
        if (allotedNoMap != null && allotedNoMap.size() > 0) {
            for (Map.Entry<String, Integer> entry : allotedNoMap.entrySet()) {
                Order order = ordersCache.getOrderById(entry.getValue(), UnitCategory.COD).getOrder();
                hmap.put(order.getGenerateOrderId(), order.getStatus().name());
            }
            return hmap;
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean resetSDPOrderDetails(String riderContactNo)
        throws TemplateRenderingException, DataNotFoundException {
        HashMap<String, Integer> allotedNoMap = ordersCache.getSDPOrders(riderContactNo);
        boolean result = false;
        if (allotedNoMap != null && allotedNoMap.size() > 0) {
            HashMap<String, Integer> clonedMap = new HashMap<>();
            for (String contact : allotedNoMap.keySet()) {
                clonedMap.put(contact, allotedNoMap.get(contact));
            }
            for (String contact : clonedMap.keySet()) {
                int orderId = clonedMap.get(contact);
                OrderInfo info = ordersCache.getOrderById(orderId, UnitCategory.COD);
                if (info == null || info.getOrder().getStatus().name().equals(OrderStatus.DELIVERED.name())) {
                    LOG.info("Order rmoved from sdp cache by manual reset" + orderId);
                    result = ordersCache.removeAllotedNoToRider(contact, orderId);
                } else if (info.getOrder().getStatus().name().equals(OrderStatus.SETTLED.name())) {
                    result = updateOrderStatusOnSDPCallback(riderContactNo, info.getDeliveryDetails().getAllotedNo(),
                        DeliveryReason.PosReset.getReason(), DeliveryReason.PosReset.getReasonId());
                }
            }
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean outOfDeliveryNotification(int orderId) throws TemplateRenderingException, DataNotFoundException {
        if (dao.outOfDeliveryOrder(orderId)) {
            OrderInfo orderInfo = ordersCache.getOrderById(orderId, UnitCategory.COD);
            orderInfo.getOrder().setOod(true);
            ordersCache.addToCache(orderInfo);
            PaymentMode paymentType = masterDataCache
                .getPaymentMode(orderInfo.getOrder().getSettlements().get(0).getMode());
            String paymentMode = paymentType.getCategory().name().equals(PaymentCategory.ONLINE.name()) ? "ONLINE"
                : paymentType.getName();
            StringBuilder sb = new StringBuilder();
            sb.append(String.format(
                "*Order Not Acknowledged Out of Delivery Area !* \n Please contact customer regarding Order Placement\n"));
            StringBuilder sbAddress = new StringBuilder();
            StringBuilder sbProductDetails = new StringBuilder();
            for (OrderItem item : orderInfo.getOrder().getOrders()) {
                addItem(sbProductDetails, item);
            }
            for (Address a : orderInfo.getCustomer().getAddresses()) {
                if (a.getId() == orderInfo.getOrder().getDeliveryAddress()) {
                    sbAddress.append(
                        String.format("%s, %s, %s, %s, %s", a.getLine1(), a.getLine2() != null ? a.getLine2() : "",
                            a.getLandmark() != null ? a.getLandmark() : "", a.getCity(), a.getState()));
                }
            }
            DiscountDetail discountDetail = orderInfo.getOrder().getTransactionDetail().getDiscountDetail();

            String message = String.format(sb.toString()
                    + "Name : %s \nContact %s \nAddress %s \nPayment Mode %s \n Order Id  %s \n Channel Partner %s \n Amount %s \n Product Details %s \n Discount/CouponCode %s %s",
                orderInfo.getCustomer().getFirstName(), orderInfo.getCustomer().getContactNumber(),
                sbAddress.toString(), paymentMode, orderInfo.getOrder().getGenerateOrderId(),
                orderInfo.getChannelPartner().getName(),
                orderInfo.getOrder().getTransactionDetail().getPaidAmount().toString(), sbProductDetails.toString(),
                discountDetail.getTotalDiscount() != null ? discountDetail.getTotalDiscount().toString() : "-",
                discountDetail.getDiscountCode() != null ? "/" + discountDetail.getDiscountCode().toString()
                    : "/" + "-");

            String unitDetails = String.format("\n *Unit name*  %s", orderInfo.getUnit().getName());

            message = message + unitDetails;

            SlackNotificationService.getInstance().sendNotification(props.getEnvironmentType(),
                ApplicationName.KETTLE_SERVICE.name(), SlackNotification.ORDER_FAILURE_INFO, message);

            return true;
        }
        return false;
    }

    private void addItem(StringBuilder sb, OrderItem item) {
        sb.append(String.format("\n%s %s   %d", item.getProductName(),
            "none".equals(item.getDimension().toLowerCase()) ? "" : item.getDimension(), item.getQuantity()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<OrderStatusDomain> getOrderTransitionDetail(int orderId) {
        List<OrderStatusEvent> list = dao.getOrderTransitionDetail(orderId);
        List<OrderStatusDomain> resultList = new ArrayList<>();
        for (OrderStatusEvent event : list) {
            resultList.add(DataConverter.convert(event));
        }
        return resultList;
    }

    private boolean updateDeliveryDetail(String generatedOrderId, DeliveryStatus status, Integer reasonId) {
        List<DeliveryDetail> deliveryDetails = deliveryDao.getDeliveryDetail(generatedOrderId);
        if (deliveryDetails != null && deliveryDetails.size() > 0) {
            deliveryDao.markAdditionalDetailsAsCancelled(deliveryDetails);
            DeliveryDetail deliveryDetail = deliveryDetails.get(0);
            deliveryDetail.setDeliveryStatus(status.name());
            if (status == DeliveryStatus.DELIVERED) {
                if (reasonId != null && reasonId == DeliveryReason.MissedCallResponse.getReasonId()) {
                    deliveryDetail.setDeliverySource(DeliveryReason.MissedCallResponse.name());
                } else if (reasonId != null && reasonId == DeliveryReason.PosReset.getReasonId()) {
                    deliveryDetail.setDeliverySource(DeliveryReason.PosReset.name());
                } else {
                    deliveryDetail.setDeliverySource(DeliveryReason.AssemblyScreen.name());
                }
            }
            deliveryDetail.setStatusUpdateTime(AppUtils.getCurrentTimestamp());
            deliveryDao.update(deliveryDetail);
            return true;
        }
        return false;
    }

    private void sdpCallbackFailure(String riderContactNo, String allotedNo, boolean result, int orderId) {
        StringBuilder sb = new StringBuilder();
        OrderDetail orderDetail = null;
        String unitName = "-";
        if (orderId == 0) {
            sb.append("This rider does not have any order for this missed call no at this moment.");
        } else {
            orderDetail = dao.find(OrderDetail.class, orderId);
        }
        if (result) {
            sb.append("Order updated successfully with result :" + result + " orderId :" + orderId);
        } else {
            sb.append("Order updation failed with  result :" + result + " orderId :" + orderId);
        }

        if (orderDetail != null) {
            unitName = masterDataCache.getUnit(orderDetail.getUnitId()).getName();
        }

        String message = String.format(
            "Rider Contact No : %s \n Missed Call No %s \n Assigned Order %s \n Order Status %s \n Unit name %s \n Remarks %s",
            riderContactNo, allotedNo, ordersCache.getSDPOrders(riderContactNo),
            orderDetail != null ? orderDetail.getOrderStatus() : "-", unitName, sb.toString());

        SlackNotificationService.getInstance().sendNotification(props.getEnvironmentType(),
            ApplicationName.KETTLE_SERVICE.name(), SlackNotification.ORDER_DELIVERY_LOG, message);

        String logMessage = String.format(
            "Rider Contact No : %s , Missed Call No %s , Assigned Order %s , Order Status %s , Unit name %s , Remarks %s",
            riderContactNo, allotedNo, ordersCache.getSDPOrders(riderContactNo),
            orderDetail != null ? orderDetail.getOrderStatus() : "-", unitName, sb.toString());
        LOG.info(logMessage);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void saveMonthlyConsumptionData(UnitBasicDetail ubd, int month, int year,
                                           Collection<OrderItemConsumable> values, String source) {
        dao.saveMonthlyConsumptionData(ubd, month, year, values, source);
    }

    @Override
    public ExternalPartnerDetail getExternalPartnerDetail(String partnerCode) {
        return DataConverter.convert(masterDataCache.getExternalPartnerMap().get(partnerCode), masterDataCache, recipeCache);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void markNPSfeedbackCancelled(Integer orderId) {
        dao.markNPSfeedbackCancelled(orderId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public MenuProductCostData addCogsData(MenuProductCostData cogsData, List<MenuProductCogsDrilldown> drilldowns)
        throws DataUpdationException {
        cogsData = (MenuProductCostData) dao.add(cogsData);
        if (cogsData == null) {
            throw new DataUpdationException("Error in persisting Cogs Data");
        } else {
            for (MenuProductCogsDrilldown d : drilldowns) {
                d.setMenuProductCostData(cogsData);
                d = (MenuProductCogsDrilldown) dao.add(d);
                if (d == null) {
                    throw new DataUpdationException("Error in persisting Cogs Drilldown Data");
                }
            }
        }
        return cogsData;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void updateNewCustomerFlagForOrders(int unitId, Date businessDate) {
        dao.updateNewCustomerFlagForOrders(unitId, businessDate);
    }

    @Override
    public byte[] getOrderReceipt(String orderId) {
        try {
            if (StringUtils.isNotBlank(orderId)) {
                OrderDetail orderDetail = dao.getOrderByExternalOrderId(orderId);
                if (orderDetail != null) {
                    int unitId = orderDetail.getUnitId();
                    String baseDir = "kiosk/" + unitId + "/orders";
                    String fileName = "orderReceipt-" + orderId + ".pdf";
                    FileDetail fileDetail = new FileDetail(props.getS3Bucket(), baseDir + "/" + fileName, null);
                    File file = fileArchiveService.getFileFromS3(props.getBasePath() + "/s3", fileDetail);
                    if (file != null) {
                        try {
                            byte[] fileInBytes = readBytesFromFile(file);
                            if (fileInBytes != null && fileInBytes.length > 0) {
                                //LOG.info("Returning order receipt.."+ Arrays.toString(fileInBytes));
                                return fileInBytes;
                            }
                        } catch (Exception ex) {
                            LOG.error("Exception Occurred while converting file to bytes array.", ex);
                        }
                        //file.delete(); // delete the temporary file created after completing request
                    }
                }
            }
        } catch (Exception ex) {
            LOG.error("Exception Occurred while fetching receipt from S3", ex);
        }
        return null;
    }

    /**
     * Read bytes from a File into a byte[].
     *
     * @param file The File to read.
     * @return A byte[] containing the contents of the File.
     * @throws IOException Thrown if the File is too long to read or couldn't be
     *                     read fully.
     */
    private byte[] readBytesFromFile(File file) throws IOException {
        InputStream is = new FileInputStream(file);

        // Get the size of the file
        long length = file.length();

        // You cannot create an array using a long type.
        // It needs to be an int type.
        // Before converting to an int type, check
        // to ensure that file is not larger than Integer.MAX_VALUE.
        if (length > Integer.MAX_VALUE) {
            is.close();
            throw new IOException("Could not completely read file " + file.getName() + " as it is too long (" + length + " bytes, max supported " + Integer.MAX_VALUE + ")");
        }

        // Create the byte array to hold the data
        byte[] bytes = new byte[(int) length];

        // Read in the bytes
        int offset = 0;
        int numRead = 0;
        while (offset < bytes.length && (numRead = is.read(bytes, offset, bytes.length - offset)) >= 0) {
            offset += numRead;
        }
        // Ensure all the bytes have been read in
        if (offset < bytes.length) {
            is.close();
            throw new IOException("Could not completely read file " + file.getName());
        }
        // Close the input stream and return bytes
        is.close();
        return bytes;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void updateFeedbackUrl(int orderId, String feedbackUrl) {
        dao.updateFeedbackUrl(orderId, feedbackUrl);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public OrderDetail getOrderDetail(int orderId) {

        return dao.find(OrderDetail.class, orderId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void saveRiderDetails(PartnerOrderRiderData request) {
        PartnerOrderRiderDetailData partnerRider = new PartnerOrderRiderDetailData();
        partnerRider.setOrderId(request.getOrder_id());
        partnerRider.setPartnerId(request.getPartner_id());
        partnerRider.setRbt(String.valueOf(request.getRbt()));
        partnerRider.setRiderPhone(request.getRider_phone());
        partnerRider.setHighTemp(String.valueOf(request.isIs_high_temp()));
        partnerRider.setIsRiderWearingMask(request.getIs_rider_wearing_mask());
        dao.add(partnerRider);
    }

    @Override
    public String getCustomerOfferLastAppliedTime(int customerId, String offerCode) {
        Date date = dao.getCustomerOfferLastAppliedTime(customerId, offerCode);
        if (date == null) {
            return null;
        }
        return AppUtils.getTimeWithoutMillisISTString(date);
    }

    @Override
    public void invalidateCashBack(Order order) {
        try {
            cashBackService.invalidateCashBack(order);
        } catch (Exception e) {
            LOG.error("Error in invalidating cash back for order {}", order);
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void attachFeedback(List<Order> orderDetails) throws DataNotFoundException {
        for (Order order : orderDetails) {
            attachFeedback(order);
        }
    }

    private void attachFeedback(Order order) throws DataNotFoundException {
        if (order.getCustomerId() > 5 && AppUtils.isAppOrder(order.getChannelPartner())) {
            FeedbackDetail feedbackDetail = dao.getInAppFeedback(order);
            if (feedbackDetail != null) {
                for (FeedbackEvent event : feedbackDetail.getFeedbackEvents()) {
                    if (event.getEventSource().equals(FeedbackSource.IN_APP.name())) {
                        if (event.getEventLongUrl() != null) {
                            order.setInAppFeedbackUrl(event.getEventLongUrl());
                        } else {
                            Pair<String, String> shortUrl = feedbackManagementService
                                .getFeedbackLinkForSource(feedbackDetail.getFeedbackId(), FeedbackSource.IN_APP);
                            order.setInAppFeedbackUrl(shortUrl.getKey());
                        }
                        order.setRating(feedbackDetail.getRating());
                        order.setFeedbackType(feedbackDetail.getEventType());
                        if(FeedbackEventType.ORDER_FEEDBACK.name().equals(feedbackDetail.getEventType())
                        && FeedbackRatingType.ORDER_RATING.name().equals(feedbackDetail.getRatingType())){
                            order.setMaxRating(5);
                        }else{
                            order.setMaxRating(10);
                        }
                    }
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public OrderInAppUrlResponse getOrdersWithDineInNPSFeedback(List<Integer> orderIds) throws DataNotFoundException {
        List<InAppUrlResponse> response = new ArrayList<>();
        for (Integer orderId : orderIds) {
            OrderFetchStrategy strategy = new OrderFetchStrategy(false, true, false, false, false, null, -1, false);
            OrderDetail orderDetail = dao.find(OrderDetail.class, orderId);
            if(Objects.nonNull(orderDetail.getInvoiceId())){
               try {
                   OrderInvoiceDetail orderInvoiceDetail = dao.getInvoiceDetail(orderDetail.getGeneratedOrderId());
                   orderDetail.setInvoiceDetail(orderInvoiceDetail);
               }catch(Exception e){
                    LOG.info("Unable to fetch invoice Id for this order");
                }
            }
            Order order = DataConverter.convert(masterDataCache, orderDetail, strategy,recipeCache,props);
            attachFeedback(order);
            response.add(new InAppUrlResponse(order.getOrderId(), order.getInAppFeedbackUrl(),
                    order.getRating(),order.getMaxRating(),order.getFeedbackType()));
        }
        return new OrderInAppUrlResponse(response);
    }


    @Override
	public PartnerDataWithOrderConsideration setMissionGaramChai(PartnerDataConsiderRequest request
			) {
		//LOG.info("creating data for : {}", request);
		PartnerDataWithOrderConsideration partner = new PartnerDataWithOrderConsideration();
		partner.setPartnerId(request.getPartnerId());
		partner.setPartnerName(masterDataCache.getChannelPartner(request.getPartnerId()).getName());
		partner.setUnitId(request.getUnitId());
		partner.setUnitName(masterDataCache.getUnitBasicDetail(request.getUnitId()).getName());
		partner.setLastOrderId(request.getOrderId());
		partner.setBusinessDate(AppUtils.getBusinessDate());
		partner.setLastUpdateTime(AppUtils.getCurrentTimestamp());
		partner.setTotalOrders(0);
		partner.setTotalOrderOfConsideration(0);
		partner.setDelayOrders(0);
		return partner;
	}

    @Override
    public PartnerDataWithOrderConsideration setCountsForMissionGaramChai(PartnerDataWithOrderConsideration partner, PartnerDataConsiderRequest request, Integer threshold, Integer maxOrdervalue, PartnerDataWithOrderConsideration missionChaiCache) {
        partner.setTotalOrderOfConsideration(missionChaiCache.getTotalOrderOfConsideration());
        partner.setDelayOrders(missionChaiCache.getDelayOrders());
        partner.setTotalOrders(missionChaiCache.getTotalOrders());
        if (props.getMissionGaramChaiMaximumOrderValue() == -1) {
            partner.setTotalOrders(missionChaiCache.getTotalOrders() + 1);
            partner.setTotalOrderOfConsideration(missionChaiCache.getTotalOrderOfConsideration() + 1);
            if (AppUtils.getTimeDiffernceInMinutes(request.getBillServerTime(), request.getUpdateTime()) > threshold) {
                partner.setDelayOrders(missionChaiCache.getDelayOrders() + 1);
            }
        } else if (props.getMissionGaramChaiMaximumOrderValue() != -1 && request.getAmount().compareTo(new BigDecimal(props.getMissionGaramChaiMaximumOrderValue())) <= 0) {
            partner.setTotalOrders(missionChaiCache.getTotalOrders() + 1);
            partner.setTotalOrderOfConsideration(missionChaiCache.getTotalOrderOfConsideration() + 1);
            if (AppUtils.getTimeDiffernceInMinutes(request.getBillServerTime(), request.getUpdateTime()) > threshold) {
                partner.setDelayOrders(missionChaiCache.getDelayOrders() + 1);
            }
        } else {
            partner.setTotalOrders(missionChaiCache.getTotalOrders() + 1);
        }

        return partner;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Long checkDeliveryOrderForCustomer(Integer customerId) {
        return dao.checkDeliveryOrderForCustomer(customerId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Long checkDineInOrderForCustomer(Integer customerId) {
        return dao.checkDineInOrderForCustomer(customerId, AppUtils.addMonthsInDate(AppUtils.getCurrentDate(), -6));
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean sendFreeChaiDeliveryDetails(String contactNumber) {
        return sendFreeChaiDeliveryMSgToCustomer(contactNumber);
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public CustomerAdditionalDetail saveFreeChaiDeliveryDetails(int customerId, String campaignName, Boolean status) {
        CustomerAdditionalDetail customerAdditionalDetail = new CustomerAdditionalDetail(customerId, campaignName, AppConstants.getValue(status), AppUtils.getBusinessDate());
        customerAdditionalDetail = dao.add(customerAdditionalDetail);
        return customerAdditionalDetail;
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public boolean checkIsNewCustomerBrandWise(int customerId, int brandId) {
        if (customerId > 5) {
            CustomerBrandMapping customerBrandMapping = dao.checkIsNewCustomerBrandWise(customerId, brandId);
            return !(customerBrandMapping.getTotalOrder() > 1);
        }
        return false;
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean saveCustomerInfoBrandWise(int customerId, int brandId, int orderId, Date billingServerTime,boolean isSpecialOrder) {
        CustomerBrandMapping customerBrandMapping = dao.checkIsNewCustomerBrandWise(customerId, brandId);
        int prevTotalOrder = customerBrandMapping.getTotalOrder();
        if(Objects.isNull(customerBrandMapping.getTotalSpecialOrder())){
            customerBrandMapping.setTotalSpecialOrder(0);
        }
        int prevTotalSpecialOrder = customerBrandMapping.getTotalSpecialOrder();
        if(isSpecialOrder){
            customerBrandMapping.setTotalSpecialOrder(prevTotalSpecialOrder + 1);
            customerBrandMapping.setLastSpecialOrderId(orderId);
            customerBrandMapping.setLastSpecialOrderTime(billingServerTime);
        }
        else {
            customerBrandMapping.setTotalOrder(prevTotalOrder + 1);
            customerBrandMapping.setLastOrderId(orderId);
            customerBrandMapping.setLastOrderTime(billingServerTime);
        }
        if (prevTotalOrder > 0 || prevTotalSpecialOrder > 0) {
            dao.update(customerBrandMapping);
            return false;
        }
        dao.add(customerBrandMapping);
        return true;


    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean deleteCogsData(Integer unitId, Integer closureId) throws DataUpdationException {
        LOG.info("trying to delete menu cogs for unit {}", unitId);
        long startTime = System.currentTimeMillis();
        try {
            List<MenuProductCostData> cogsDetail = dao.getCogsData(unitId, closureId);
            if (cogsDetail != null && !cogsDetail.isEmpty()) {
                for (MenuProductCostData menu : cogsDetail) {
                    dao.deleteCogsDrillDown(menu.getDetailId());
                }
                dao.deleteMenuCogsData(cogsDetail.stream().map(MenuProductCostData::getDetailId).collect(Collectors.toList()));
            }
            LOG.info("deleted menu cogs in {} ms", System.currentTimeMillis() - startTime);
            return true;
        } catch (Exception e) {
            throw new DataUpdationException("Error while deleting previous data in cogs: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    @Async(value = "taskExecutor")
    public void pushDataToThirdPartyAnalytics(OrderInfo info, boolean isOrderCancelled) {
        if (!AppConstants.EXCLUDE_CUSTOMER_IDS.contains(info.getCustomer().getId())) {
            try {
                boolean clvFlag = props.getCleverTapEnabled();

                boolean clvNBOFlag = info.getChannelPartner().getId() == AppConstants.CHANNEL_PARTNER_DINE_IN_APP
                        && Objects.nonNull(info.getNextOffer())
                        && Objects.nonNull(info.getNextOffer().getContentUrl())
                        && Objects.nonNull(info.getNextOffer().isAvailable());

                boolean fbFlag = props.getFacebookPushEnabled()
                        && info.getOrder().getOrderType().equals(AppConstants.ORDER_TYPE_REGULAR)
                        && info.getChannelPartner().getId() != AppConstants.CHANNEL_PARTNER_SWIGGY
                        && !info.getOrder().isGiftCardOrder();

                boolean sendCODOrderFeedbackNotification=info.getChannelPartner().getId()== AppConstants.CHANNEL_PARTNER_SWIGGY && info.getChannelPartner().getId()== AppConstants.CHANNEL_PARTNER_ZOMATO && props.getSendFeedbackMessageForCODOrders();
                if ((fbFlag || clvFlag || clvNBOFlag) && !sendCODOrderFeedbackNotification) {
                    Thread t = new Thread(() -> {
                        try{
                            if (clvFlag) {
                                if(info.getBrand().getBrandId().equals(1) && !isOrderCancelled) {
                                    OrderFeedbackMetadata orderFeedbackMetadata = new OrderFeedbackMetadata() ;
                                    try{
                                        orderNotificationService.generateOrderFeedbackNotification(info,orderFeedbackMetadata);
                                    }catch (Exception e) {
                                        LOG.error("Exception while generating order feedback and receipt deatils for orderId ::{}",info.getOrder().getOrderId(), e);
                                    }
                                    orderNotificationService.createOrderNotificationData(info.getOrderNotification(),info);
                                    pushDataToCleverTapNew(info,isOrderCancelled,orderFeedbackMetadata);
                                }
                            }
                            if (!isOrderCancelled) {
                                if (fbFlag) {
                                    //push data for Facebook
//                                    facebookDataPushService.uploadFBEvent(info, FBEvents.PURCHASE);
                                    cleverTapEventPublisher.publishCleverTapEvent(props.getEnvironmentType().name(),info.getOrder(),false);
                                }
                            }
                        }catch (Exception e){
                            LOG.info("Exception occured during pushing data to third party analytics",e);
                        }
                    });
                    t.start();
                }

            } catch (Exception e) {
                LOG.error("Error while initializing push data thread {}", e.getMessage());
            }
        }
        deleteReciepts(info.getUnit().getId(),info.getOrder().getOrderId());
    }

    @Override
    public void publishCustomerTransactionViewEvent(int customerId, int brandId, Integer orderId) {
        if (!AppConstants.EXCLUDE_CUSTOMER_IDS.contains(customerId)) {
            try {
                CustomerTransactionViewEvent event = new CustomerTransactionViewEvent();
                event.setCustomerId(customerId);
                event.setBrandId(brandId);
                sqsNotificationService.publishToSQS(props.getEnvironmentType().name(), event,
                        "_CUSTOMER_TRANSACTION_VIEW");
                LOG.info("Publishing publishCustomerTransactionViewEvent:::customer:{} brand:{} order:{}", customerId, brandId, orderId);
            } catch (JMSException ex) {
                LOG.error("Error publishing publishCustomerTransactionViewEvent ::::::::", ex);
            }
        }
    }

    private void pushDataToCleverTap(OrderInfo info, boolean isOrderCancelled, OrderFeedbackMetadata orderFeedbackMetadata) {
                LOG.info("Checking and updating data for the customer:{}", info.getOrder().getCustomerId());
                try {
                    cleverTapDataPushService.pushUserToCleverTap(info.getOrder().getCustomerId());
                } catch (Exception e) {
                    LOG.error("error while pushing customer data to clevertap {}", e.getMessage());
                    cleverTapDataPushService.persistProfileTrack(
                            new CleverTapProfilePushTrack(info.getOrder().getCustomerId(),
                                    AppConstants.ERROR, CleverTapConstants.REGULAR));
                }

            if (!isOrderCancelled) {
                LOG.info("Publishing new order event detail to clevertap for orderid: {}",
                        info.getOrder().getOrderId());///to change
                try {
                    Map<Integer,OrderNotification> orderNotificationMap = new HashMap<>();
                    orderNotificationMap.putIfAbsent(info.getOrder().getOrderId(), info.getOrderNotification());
                    CleverTapPushResponse response = cleverTapDataPushService.uploadEvent(
                            Arrays.asList(info.getOrder().getOrderId()), getOrderType(info), CleverTapConstants.REGULAR,orderNotificationMap);
                    try{
						if (Objects.nonNull(response) && (response.getStatus().equalsIgnoreCase("Success")
								|| response.getStatus().equalsIgnoreCase("PUSHED_TO_QUEUE"))) {
                            orderNotificationService.updateFeedbackEventStatus(orderFeedbackMetadata, true);
                        }else{
                            orderNotificationService.updateFeedbackEventStatus(orderFeedbackMetadata, false);
                        }
                    }catch(Exception e ){
                        LOG.error("Error while updating Feedbcak event status for order with id :{}", info.getOrder().getOrderId(), e);
                    }
                    cleverTapDataPushService.persistEventTracks(response.getEvents());
                } catch (Exception e) {
                    LOG.error("error while pushing clevertap event data {}", e);
                    cleverTapDataPushService.persistEventTrack(
                            new EventPushTrack(CleverTapEvents.CHARGED, info.getOrder().getOrderId(), AppConstants.ERROR, CleverTapConstants.REGULAR, AppConstants.CLEVERTAP));
                }
            }
    }

    private void pushToCleverTapForNBO(OrderInfo info) {
        try {
            String nextOfferEventName = info.getNextOffer().getContentUrl();
            LOG.info("Publishing NBO event detail to clevertap for orderid: {}", info.getOrder().getOrderId());
            CleverTapPushResponse response = cleverTapDataPushService.uploadNextBestOfferEvents(
                    Arrays.asList(info.getOrder().getOrderId()), CleverTapEvents.NEXT_BEST_OFFER, CleverTapConstants.REGULAR, nextOfferEventName);
            cleverTapDataPushService.persistEventTracks(response.getEvents());
        } catch (Exception e) {
            LOG.error("error while pushing NBO event to clevertap {}", e.getMessage());
            cleverTapDataPushService.persistEventTrack(new EventPushTrack(
                    CleverTapEvents.NEXT_BEST_OFFER, info.getOrder().getOrderId(), AppConstants.ERROR, CleverTapConstants.REGULAR, AppConstants.CLEVERTAP));
        }
    }

    @Override
    public Map<Integer, PartnerDataWithOrderConsideration> getMissionGaramChaiCountForUnit(int unitId) {
        if (!partnerOrderConsiderationCache.isCacheLoaded()) {
            synchronized (this) {
                if (!partnerOrderConsiderationCache.isCacheLoaded()) {
//                     partnerOrderConsiderationCache.setCacheLoaded(true);
                    Thread t = new Thread() {
                        @Override
                        public void run() {
                            try {
                                LOG.info("start loading partner consideration cache inside  thread for unit {}", unitId);
                                loadPartnerOrderCache();
                            } catch (Exception e) {
                                LOG.error("Error loading partner consideration cache :" + e);
                            }
                        }
                    };
                    t.start();
                    LOG.info("sending dummy data for partner consideration cache for unit {} ", unitId);
                    return getDummyDataForCache();
                }
                return getPartnerOrderChaiCount(unitId);
            }
        } else {
            LOG.info("trying to fetch value  from partner order consideration cache for unit {}", unitId);
            return getPartnerOrderChaiCount(unitId);
        }
    }

	public void loadPartnerOrderCache() {
		partnerOrderConsiderationCache.setCacheLoaded(false);
		try {
			if (props.isMissionGaramChaiActive()) {
				Stopwatch watch = Stopwatch.createUnstarted();
				LOG.info("$$$$$$$$$$$ clear mission chai Cache $$$$$$$$$$");
				partnerOrderConsiderationCache.clearCache();
				LOG.info("$$$$$$$$$$$ creating mission chai Cache $$$$$$$$$$");
				watch.start();
				List<PartnerDataConsiderRequest> list = dao.getListOfChannelPartnerOrderDetail(
						AppUtils.getCurrentBusinessDayStartTime(), props.getMissionGaramChaiBrandIds(),
						props.getMissionGaramChaiChannelpartnerIds());
				LOG.info("Inside loadCache - Mission Chai Garam : dao.getListOfChannelPartnerOrderDetail took {} ms",
						watch.stop().elapsed(TimeUnit.MILLISECONDS));
				watch.reset();
				watch.start();
				for (PartnerDataConsiderRequest request : list) {
					PartnerDataWithOrderConsideration partner = setMissionGaramChai(request);
					if (!partnerOrderConsiderationCache.getMissionGaramChai().containsKey(request.getUnitId())) {
						partnerOrderConsiderationCache.getMissionGaramChai().put(request.getUnitId(), new HashMap<>());
					}
					partner = setCountsForMissionGaramChai(partner, request, props.getMissionGaramChaiThresholdMPR(),
							props.getMissionGaramChaiMaximumOrderValue(),
							partnerOrderConsiderationCache.getMissionGaramChai().get(request.getUnitId())
									.getOrDefault(request.getPartnerId(), partner));

					partnerOrderConsiderationCache.getMissionGaramChai().get(request.getUnitId())
							.put(request.getPartnerId(), partner);
				}
				LOG.info("Inside loadCache - Mission Chai Garam : setting the value took {} ms",
						watch.stop().elapsed(TimeUnit.MILLISECONDS));
				partnerOrderConsiderationCache.setCacheLoaded(true);
			}
		} catch (Exception e) {
			LOG.error("Exception Caught:::", e);
			partnerOrderConsiderationCache.setCacheLoaded(false);
		}
	}
    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<Integer> unsettledKettleOrdersDetailList(List<Integer> ids) {
        return dao.unsettledKettleOrdersDetailList(ids);
    }

    @Override
    public CampaignDetailData getCampaignDetailById(Integer campaignId) {
        LOG.info("Fetching campaign detail from master for campaign id :: {}",campaignId);
//        CampaignDetailData campaignDetailData = WebServiceHelper.getRequestWithParam();
        return null;
    }
    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void updateCustomerTransaction(int customerId, int brandId) {
        dao.updateCustomerTransaction(customerId, brandId);
    }


    @Override
    public List<CustomerMappingTypes> getCustomerMappingTypes() {
        return CustomerRepeatType.getCustomerMappingTypes();
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public OrderDetailForFeedback getOrderDetailForFeedback(FeedbackTokenInfo feedbackTokenInfo) throws DataNotFoundException {
        LOG.info("Fetching order detail for order id = {}",feedbackTokenInfo.getOrderId());
        OrderDetail orderDetail = dao.find(OrderDetail.class, feedbackTokenInfo.getOrderId());
        CustomerInfo customerInfo  = customerDao.getCustomerInfoById(orderDetail.getCustomerId());
        if(orderDetail != null) {
            OrderDetailForFeedback orderDetailForFeedback = new OrderDetailForFeedback();
            orderDetailForFeedback.setBid(orderDetail.getBrandId());
            orderDetailForFeedback.setBn(getBrandName(orderDetail.getBrandId()));
            orderDetailForFeedback.setUid(orderDetail.getUnitId());
            orderDetailForFeedback.setUn(masterDataCache.getUnitBasicDetail(orderDetail.getUnitId()).getName());
            orderDetailForFeedback.setUc(masterDataCache.getUnitBasicDetail(orderDetail.getUnitId()).getCity());
            orderDetailForFeedback.setGoid(orderDetail.getGeneratedOrderId());
            orderDetailForFeedback.setOid(orderDetail.getOrderId());
            orderDetailForFeedback.setFid(feedbackTokenInfo.getFeedbackId());
            orderDetailForFeedback.setOd(AppUtils.getMonthDayTimeString(orderDetail.getBillingServerTime()));
            orderDetailForFeedback.setOr(0);
            orderDetailForFeedback.setOc("");
            orderDetailForFeedback.setCpc(masterDataCache.getChannelPartner(orderDetail.getChannelPartnerId()).getCode());
            orderDetailForFeedback.setCpn(masterDataCache.getChannelPartner(orderDetail.getChannelPartnerId()).getName());
            orderDetailForFeedback.setCid(orderDetail.getCustomerId());
            orderDetailForFeedback.setCn(orderDetail.getCustomerName());
            orderDetailForFeedback.setOs(orderDetail.getOrderSource());
            orderDetailForFeedback.setRu("");
            if(KettleUtils.isProfileCompleted(customerInfo)){
                orderDetailForFeedback.setRud(props.getThankYouPageBasePath()+"thankyou");
            }else{
                orderDetailForFeedback.setRud(props.getThankYouPageBasePath()+"thanksToyou");
            }
            orderDetailForFeedback.setBpiu(props.getIconImageHostUrl());
            orderDetailForFeedback.setCc("N");
            orderDetailForFeedback.setOfq(props.getOrderFeedbackQuestion());
            orderDetailForFeedback.setNq(props.getNpsQuestion());
            orderDetailForFeedback.setSor(props.getIsShowOrderFeedbackRating());
            orderDetailForFeedback.setSonr(props.getIsShowNpsRating());
            List<OrderFeedbackQuestionResponse> ynq = getFeedbackQuestions(OrderFeedbackQuestionType.YES_NO.name(),feedbackTokenInfo.getUnitName());
            if(Objects.nonNull(ynq) && !ynq.isEmpty()){
                orderDetailForFeedback.setYnq(ynq);
            }else{
                orderDetailForFeedback.setYnq(getQuestions(OrderFeedbackQuestionType.YES_NO.name()));
            }
            List<OrderFeedbackQuestionResponse> trq = getFeedbackQuestions(OrderFeedbackQuestionType.TEXT.name(),feedbackTokenInfo.getUnitName());
            if(Objects.nonNull(trq) && !trq.isEmpty()){
                orderDetailForFeedback.setTrq(trq);
            }else{
                orderDetailForFeedback.setTrq(getQuestions(OrderFeedbackQuestionType.TEXT.name()));
            }
            List<OrderFeedbackQuestionResponse> mcq = getFeedbackQuestions(OrderFeedbackQuestionType.MCQ.name(),feedbackTokenInfo.getUnitName());
            if(Objects.nonNull(mcq) && !mcq.isEmpty()){
                orderDetailForFeedback.setMcq(mcq);
            }else{
                orderDetailForFeedback.setMcq(new ArrayList<>());
            }

            List<OrderFeedbackQuestionResponse> erq = getFeedbackQuestions(OrderFeedbackQuestionType.ERQ.name(),feedbackTokenInfo.getUnitName());
            if(Objects.nonNull(erq) && !mcq.isEmpty()){
                orderDetailForFeedback.setErq(erq);
            }else{
                orderDetailForFeedback.setErq(new ArrayList<>());
            }

            List<FeedbackOrderItem> feedbackOrderItemList = new ArrayList<>();
            List<Integer> productIds = getProductIdsForImage(orderDetail);
            List<ProductImageMapping> productImageMappingList = getProductImageData(productIds);

            for(com.stpl.tech.kettle.data.model.OrderItem orderItem : orderDetail.getOrderItems()){
                Product product = masterDataCache.getProduct(orderItem.getProductId());
                if(!ProductClassification.MENU.equals(product.getClassification())
                        || isComboItem(orderItem.getOrderItemId(),orderDetail.getOrderItems())
                        || product.getTaxCode().equals("GIFT_CARD")
                        || Objects.nonNull(masterDataCache.getSubscriptionProductDetail(orderItem.getProductId()))){
                    continue;
                }
                if(isRepeatedProduct(orderItem, feedbackOrderItemList)){
                    continue;
                }
                FeedbackOrderItem feedbackOrderItem = new FeedbackOrderItem();
                feedbackOrderItem.setPid(orderItem.getProductId());
                feedbackOrderItem.setIid(orderItem.getOrderItemId());
                feedbackOrderItem.setIn(orderItem.getProductName());
                feedbackOrderItem.setD(orderItem.getDimension());
                feedbackOrderItem.setQt(orderItem.getQuantity());
                feedbackOrderItem.setC(getCustomisation(orderItem.getOrderItemAddons()));
                feedbackOrderItem.setQ("How do you rate the taste?");
                feedbackOrderItem.setIr(0);
                feedbackOrderItem.setIc("");
                feedbackOrderItem.setPi(getImageUrl(orderItem.getProductId(),productImageMappingList));
                feedbackOrderItemList.add(feedbackOrderItem);
            }
            orderDetailForFeedback.setFiol(feedbackOrderItemList);
            LOG.info("Feedback order detail == {}", orderDetailForFeedback.toString());
            return orderDetailForFeedback;
        }else{
            LOG.info("No order detail found for order id = {}", feedbackTokenInfo.getOrderId());
            return null;
        }
    }


    private List<OrderFeedbackQuestionResponse> getQuestions(String type){
        try{
            List<OrderFeedbackQuestionResponse> resQuestions = new ArrayList<>();
            List<ListData> listData = refLookupDao.getAllListData(AppConstants.RTL_GROUP_FEEDBACK_DETAIL, true);
            for(ListData listData1 : listData){
                if(type.equals(listData1.getDetail().getName())){
                    for(IdCodeName  idCodeName : listData1.getContent()){
                        resQuestions.add(OrderFeedbackQuestionResponse
                                .builder().q(idCodeName.getName())
                                .r("").build());
                    }
                }
            }
            return resQuestions;
        }catch (DataNotFoundException e){
            LOG.error("List data not found", e);
        }catch (Exception e){
            LOG.error("Error while getting questions on the basis of type",e);
        }
        return  new ArrayList<>();
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    private List<OrderFeedbackQuestionResponse> getFeedbackQuestions(String type,String unitName){
        try{
            List<OrderFeedbackQuestionResponse> resQuestions = new ArrayList<>();
            List<FeedbackQuestionsUnitMapping> data = feedBackQuestionsUnitMappingDao.findByQuestionTypeAndUnitNameAndMappingStatus(type,unitName,
                    AppConstants.ACTIVE);
            List<Integer> questionIds = new ArrayList<>();
            if(Objects.nonNull(data) && !data.isEmpty()){
                for(FeedbackQuestionsUnitMapping d : data){
                    questionIds.add(d.getQuestionId());
                }
                List<FeedbackQuestionsDetail> feedbackQuestionsDetails = feedBackQuestionDetailDao.findByIdInAndQuestionTypeAndQuestionStatus(questionIds,
                        type,AppConstants.ACTIVE);
                if(Objects.nonNull(feedbackQuestionsDetails) && !feedbackQuestionsDetails.isEmpty()){
                    for(FeedbackQuestionsDetail questionsDetail : feedbackQuestionsDetails){
                        if(questionsDetail.getQuestionStatus().equals(AppConstants.ACTIVE)){
                            resQuestions.add(OrderFeedbackQuestionResponse
                                    .builder().q(questionsDetail.getQuestion())
                                    .r("").build());
                        }
                    }
                }
            }
            return resQuestions;
        }catch (Exception e){
            LOG.info("Error in fetching Feedback questions for type :{} and error is : {}",type,e);
        }
        return new ArrayList<>();
    }


    @Override
    public void refreshAllListData(){
        LOG.info("Refreshing all listData");
        refLookupDao.refreshAllListData();
    }

    private boolean isRepeatedProduct(com.stpl.tech.kettle.data.model.OrderItem orderItem, List<FeedbackOrderItem> feedbackOrderItemList) {
        Integer counter;
        for(counter = 0; counter < feedbackOrderItemList.size(); counter++){
            FeedbackOrderItem feedbackOrderItem = feedbackOrderItemList.get(counter);
            Integer quantity = feedbackOrderItem.getQt();
            if(feedbackOrderItem.getPid().equals(orderItem.getProductId())
            && feedbackOrderItem.getD().equals(orderItem.getDimension())
            && feedbackOrderItem.getC().equals(getCustomisation(orderItem.getOrderItemAddons()))){
                feedbackOrderItemList.get(counter).setQt(quantity + 1);
                return true;
            }
        }
        return false;
    }


    public boolean isComboItem(Integer orderItemId,List<com.stpl.tech.kettle.data.model.OrderItem> orderItems){
        for(com.stpl.tech.kettle.data.model.OrderItem orderItem : orderItems){
            if(orderItemId.equals(orderItem.getParentItemId())){
                return true;
            }
        }
        return false;
    }

    public List<Integer> getProductIdsForImage(OrderDetail orderDetail){
        List<Integer> ids = new ArrayList<>();
        for(com.stpl.tech.kettle.data.model.OrderItem orderItem : orderDetail.getOrderItems()){
            ids.add(orderItem.getProductId());
        }
        return ids;
    }

    public List<ProductImageMapping> getProductImageData(List<Integer> ids)  {
        String auth = props.getKettleClientToken();
        try{
            return Arrays.asList(WebServiceHelper.postRequestWithAuthInternalWithTimeout(props.getMasterBasePath()+"/rest/v1/product-metadata/product-image-detail",ids, ProductImageMapping[].class,auth));
        }catch (Exception e){
            LOG.error("Error occured while getting product images");
            LOG.info(e.getMessage());
        }
        return null;
    }

    public String getImageUrl(Integer productId, List<ProductImageMapping> productImageMappingList){
        String url = "";
        for(ProductImageMapping productImageMapping : productImageMappingList){
            if(productId.equals(productImageMapping.getProductId()) && productImageMapping.getImageType().equals("GRID_MENU_LOW")){
                url = productImageMapping.getImageUrl();
            }
        }
        if(url.equals("")){
            url="1375_grid_menu_low_1.jpg";
            for(ProductImageMapping productImageMapping : productImageMappingList){
                if(productId.equals(productImageMapping.getProductId())){
                    url = productImageMapping.getImageUrl();
                }
            }
        }
        return url;
    }

    public String getBrandName(Integer brandId){
        for(Brand brand : masterDataCache.getAllBrands()){
            if(brand.getBrandId().equals(brandId)){
                return brand.getBrandName();
            }
        }
        return null;
    }

    public String getCustomisation(List<OrderItemAddon> orderItemAddons){
        String customisation = "";
        for(OrderItemAddon orderItemAddon : orderItemAddons){
            customisation += orderItemAddon.getName()+", ";
        }
        if(customisation.equals("")){
            return customisation;
        }
        return customisation.substring(0,customisation.length()-2);
    }


    private Map<Integer, PartnerDataWithOrderConsideration> getDummyDataForCache() {
        Map<Integer, PartnerDataWithOrderConsideration> map = new HashMap<>();
        props.getMissionGaramChaiChannelpartnerIds().forEach(id -> map.put(id, new PartnerDataWithOrderConsideration(id)));
        return map;
    }


    private Map<Integer, PartnerDataWithOrderConsideration> getPartnerOrderChaiCount(int unitId) {
        if (partnerOrderConsiderationCache.getMissionGaramChai().containsKey(unitId)) {
            LOG.info("Found unit order consideration cache for unit {}", unitId);
            props.getMissionGaramChaiChannelpartnerIds().forEach(
                id -> {
                    if (!partnerOrderConsiderationCache.getMissionGaramChai().get(unitId).containsKey(id)) {
                        partnerOrderConsiderationCache.getMissionGaramChai().get(unitId).put(id, new PartnerDataWithOrderConsideration(id));
                    }
                });
            return partnerOrderConsiderationCache.getMissionGaramChai().get(unitId);
        } else {
            LOG.info("Not able to get consideration  data for unit {} sending dummy values", unitId);
            return getDummyDataForCache();
        }
    }

    public String getTaxTypeComparison(String stateCodeOfUnit,OrderInvoiceDetail orderInvoiceDetail){
        if(orderInvoiceDetail.getStateCode().equals(stateCodeOfUnit)){
            orderInvoiceDetail.setTaxType(AppConstants.INTRA_STATE);
        }
        else{
            orderInvoiceDetail.setTaxType(AppConstants.INTER_STATE);
        }

        return orderInvoiceDetail.getTaxType();

    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public OrderInvoiceDetail setInvoiceDetail(RequestInvoiceDetail requestInvoiceDetail){
        try {
            OrderDetail orderDetail = dao.getOrderDetailByGeneratedOrderId(requestInvoiceDetail.getOrderId());
            if (checkIfPossibleToGetInvoice(AppUtils.getCurrentTimestamp(), orderDetail.getBillingServerTime())) {
                OrderInvoiceDetail orderInvoiceDetail = new OrderInvoiceDetail();
                boolean prevInvoice = false;
                if(Objects.nonNull(orderDetail.getInvoiceId())){
                    orderInvoiceDetail = dao.getInvoiceDetail(requestInvoiceDetail.getOrderId());
                    prevInvoice=true;
                }

                orderInvoiceDetail.setGenerationTime(AppUtils.getCurrentTimestamp());
                orderInvoiceDetail.setCompanyAddress(requestInvoiceDetail.getCompanyAddress());
                orderInvoiceDetail.setCompanyName(requestInvoiceDetail.getCompanyName());
                orderInvoiceDetail.setGstIn(requestInvoiceDetail.getGst());
                orderInvoiceDetail.setGeneratedBy(requestInvoiceDetail.getGeneratedBy());
                orderInvoiceDetail.setStateCode(requestInvoiceDetail.getGst().substring(0, 2));
                orderInvoiceDetail.setTaxType(getTaxTypeComparison(requestInvoiceDetail.getStateCode(), orderInvoiceDetail));
//          Date date = new Date(); // This object contains the current date value
                orderDetail.setPrintCount(1);
                if (prevInvoice) {
                    try {
                        dao.update(orderInvoiceDetail);
                    } catch (Exception e) {
                        LOG.error("Failed to update invoice", e);
                    }

                } else {
                    orderDetail.setIsInvoice(AppConstants.getValue(true));
                    orderInvoiceDetail.setOrderId(requestInvoiceDetail.getOrderId());
                    orderDetail.setInvoiceId(generateInvoiceIdDetail(requestInvoiceDetail.getStateCode()));
                    dao.add(orderInvoiceDetail);
                }
                dao.update(orderDetail);
                return orderInvoiceDetail;
            }

      }
      catch(Exception e){
          LOG.info(e.toString());
          return null;
        }
        return null;

    }

    public boolean checkIfPossibleToGetInvoice(Date generationTime, Date billServerTime){
        int fy=0;
        int month = AppUtils.getMonth(billServerTime);

        if (month >= 4) {
            fy = (AppUtils.getYear(billServerTime) + 1)%100;
        } else {
            fy = AppUtils.getYear(billServerTime)%100;
        }

        Date thresholdDate= new Date(100+fy,8,30);
        if(generationTime.after(new Date(100+fy-1,2,31)) && generationTime.before(thresholdDate)){
            return true;
        }

        return false;
    }

    @Override
    public String generateInvoiceIdDetail(String stateCode) {
        int fy = 0;
        LOG.info("Generating Invoice Id for State Code {} at {}",stateCode, AppUtils.getCurrentDate());
        int month = AppUtils.getMonth(AppUtils.getCurrentDate());
        if (month >= 4) {
            fy = (AppUtils.getYear(AppUtils.getCurrentDate()) + 1)%100;
        } else {
            fy = AppUtils.getYear(AppUtils.getCurrentDate())%100;
        }
        int seqInvoiceId = dao.getNextStateInvoiceId(stateCode, String.valueOf(fy));

        return "FY" + fy + "/" + stateCode + "/" + String.format("%07d", seqInvoiceId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Pair<MyOfferResponse, NextOffer> createGeneralOffer(CreateNextOfferRequest request, Customer customer, CampaignDetail campaignDetail, Integer lagDays) {
        int customerId = customer.getId();
        CouponCloneResponse clonedCoupon = null;
        String notificationType = "SMS";
        CustomerCampaignOfferDetail generalOffer = null;
        Integer couponDelay = Objects.nonNull(campaignDetail.getCouponApplicableAfter()) ? campaignDetail.getCouponApplicableAfter() : 0;
        couponDelay = Math.max(couponDelay, lagDays);
        String startDay = AppUtils.getDateString(AppUtils.getNextDate(AppUtils.addDays(AppUtils.getBusinessDate(),couponDelay)),
                AppConstants.DATE_FORMAT);
        if(Objects.nonNull(campaignDetail.getStartDate())){
            startDay = AppUtils.getDateString(campaignDetail.getStartDate(),AppUtils.DATE_FORMAT_STRING);
        }
        try {
            LOG.info("DEFAULT-OFFER-CREATION  :: starting process for campaign id :: {}",campaignDetail.getCampaignId());
            CustomerDineInView oneView = offerService.getCustomerDineInView(customerId,
                    AppConstants.CHAAYOS_BRAND_ID, Arrays.asList(-1));
            CustomerRepeatType type=CustomerRepeatType.DEFAULT;
            CustomerRepeatType customerRepeatType =  CustomerRepeatType.getCustomerRepeatTypeAfterPlacingOrder(
                    oneView.getDineInOrders() == null ? 0 : oneView.getDineInOrders(),
                    oneView.getActiveDineInOrders() == null ? 0 : oneView.getActiveDineInOrders(),
                    AppConstants.getValue(oneView.getAvailedSignupOffer()));
            Map<String, Map<Integer, CampaignMapping>> couponMap = campaignDetail.getMappings();
            CustomerMappingTypes customerMappingTypes = new CustomerMappingTypes(type.name(), 1,
                    couponMap.get(type.name()).get(1).getValidityInDays(), type.getDefaultCloneCode(),type.getReminderDays());

            if(AppConstants.getValue(campaignDetail.getNewCustomerOnly()) && !CustomerRepeatType.REGISTER.equals(customerRepeatType)){
                LOG.info("DEFAULT-OFFER-CREATION  :: skipping offer creation as campaign is only for new customer  customer id :: {}",customerId);
                return new Pair<>(newCustomerOnlyRes(customer),null);
            }
            if(!alreadyAvailedOffer(customerId,campaignDetail.getCampaignId())){
                LOG.info("DEFAULT-OFFER-CREATION  :: creating new offer for customer id :: {}",customerId);
                CampaignMapping cloneCode = campaignCache.getCloneCode(type,
                        type.getJourneyCount(), campaignDetail.getCampaignId());
                if(Objects.nonNull(cloneCode)){
                    LOG.info("DEFAULT-OFFER-CREATION :: found clone Code for type :: {} journey count :: {} campaign id :: {} clone code :: {}",
                            type.name(), type.getJourneyCount(), campaignDetail.getCampaignId(),JSONSerializer.toJSON(cloneCode));
                    if(campaignDetail.isCouponClone()) {
                        CouponDetailData clone = offerService.getCouponDetailData(cloneCode.getCode());
                        clonedCoupon = offerService.getCloneCodeForDefault( Arrays.asList(customer.getContactNumber()), type, cloneCode.getCode(), couponDelay,1,
                                customerMappingTypes.getValidityInDays(), campaignDetail.getCouponPrefix(), null,
                                null, clone);
                        if(clonedCoupon.getErrors().size() > 0){
                            LOG.info("DEFAULT-OFFER-CREATION -- skipping offer creation as error list : {}",JSONSerializer.toJSON(clonedCoupon));
                            return new Pair<>(noOfferResponse(customer),null);
                        }
                        if(Objects.nonNull(clonedCoupon.getCode())){
                            LOG.info("DEFAULT-OFFER-CREATION :: found cloned code :: {}", JSONSerializer.toJSON(clonedCoupon));
                            generalOffer = offerService.createPostOrderOffer(request.getBrandId(), null, campaignDetail.getCampaignId()
                                    , customerId, -1, customer.getContactNumber(), customer.getCountryCode(),
                                    clonedCoupon.getMappings().get(customer.getContactNumber()), customer.getFirstName(),
                                    cloneCode.getDesc(), clonedCoupon.getCode(),campaignDetail,type, 1,request);
                            if(Objects.nonNull(generalOffer)){
                                NextOffer offer = getNextOffer(request.getBrandId(), customer.getContactNumber(), customerId,
                                        customer.getFirstName(), generalOffer, type,
                                        notificationType, getContentUrl(type));
                                offer.setMaxUsage(clone.getMaxUsage());
                                offer.setTnc(campaignCache.getCouponDetail(generalOffer.getCampaignCloneCode()).getOffer().getTermsAndConditions());
                                offer.setCustomerCampaignOfferDetailId(generalOffer.getCapmpaignOfferDetailId());
                                offer.setCrmAppBannerUrl(campaignDetail.getCrmAppBannerUrl());
                                offer.setMinBillValue(clone.getOfferDetail().getMinValue());
                                offer.setOfferValue(clone.getOfferDetail().getValue());
                                offer.setOfferValueType(clone.getOfferDetail().getOfferType());
                                offer.setProductList(getProductsInOfferMapping(clone.getOfferDetail()));
                                sendGeneralOfferNotification(offer,generalOffer,customer.getOptWhatsapp());
                                return new Pair<>(covertToOfferResponse(generalOffer, campaignDetail, false),offer);
                            }
                        }else{
                            LOG.info("DEFAULT-OFFER-CREATION :: no cloned coupon found");
                            return  new Pair<>(noOfferResponse(customer), null);
                        }
                    }else {
                        CouponDetailData couponDetail = offerExternalService.getCoupon(cloneCode.getCode());
                        CouponData couponData = getCouponData(couponDetail, customer.getContactNumber(),couponDelay,customerRepeatType.getValidityInDays());
                        offerService.addCustomerMappingCouponData(couponDetail, customer.getContactNumber());
                        generalOffer = offerService.createPostOrderOffer(request.getBrandId(), null, campaignDetail.getCampaignId(),
                                customerId, -1, customer.getContactNumber(), customer.getCountryCode(), couponData,
                                        customer.getFirstName(), cloneCode.getDesc(), cloneCode.getCode(), campaignDetail,type, 1,request);

                        if(Objects.nonNull(generalOffer)){
                            NextOffer offer = getNextOffer(request.getBrandId(), customer.getContactNumber(), customerId,
                                    customer.getFirstName(), generalOffer, type,
                                    notificationType, getContentUrl(type));
                            offer.setMaxUsage(couponDetail.getMaxUsage());
                            offer.setTnc(campaignCache.getCouponDetail(generalOffer.getCampaignCloneCode()).getOffer().getTermsAndConditions());
                            offer.setCustomerCampaignOfferDetailId(generalOffer.getCapmpaignOfferDetailId());
                            offer.setCrmAppBannerUrl(campaignDetail.getCrmAppBannerUrl());
                            offer.setMinBillValue(couponDetail.getOfferDetail().getMinValue());
                            offer.setOfferValue(couponDetail.getOfferDetail().getValue());
                            offer.setOfferValueType(couponDetail.getOfferDetail().getOfferType());
                            offer.setProductList(getProductsInOfferMapping(couponDetail.getOfferDetail()));
                            sendGeneralOfferNotification(offer,generalOffer,customer.getOptWhatsapp());
                            return new Pair<>(covertToOfferResponse(generalOffer,campaignDetail,false), offer);
                        }
                    }
                }else{
                    LOG.info("DEFAULT-OFFER-CREATION  :: creation of new offer failed as no clone code found for " +
                            "campaign id :: {} and type :: {}",campaignDetail.getCampaignId(),CustomerRepeatType.DEFAULT);
                    return  new Pair<>(noOfferResponse(customer),null);
                }
            }else{
                LOG.info("DEFAULT-OFFER-CREATION :: getting existing offer created for customer id : {}",customerId);
                generalOffer = offerService.getActiveOfferCampaign(customerId,campaignDetail.getCampaignId());
                if(Objects.nonNull(generalOffer)){
                    LOG.info("DEFAULT-OFFER-CREATION :: Found general offer with id :: {}",generalOffer.getCapmpaignOfferDetailId());
                    CouponDetailData clone = offerService.getCouponDetailData(generalOffer.getCouponCode());
                    NextOffer offer = getNextOffer(request.getBrandId(), customer.getContactNumber(), customerId,
                            customer.getFirstName(), generalOffer, type,
                            notificationType, getContentUrl(type));
                    offer.setMaxUsage(clone.getMaxUsage());
                    offer.setTnc(campaignCache.getCouponDetail(generalOffer.getCampaignCloneCode()).getOffer().getTermsAndConditions());
                    offer.setCustomerCampaignOfferDetailId(generalOffer.getCapmpaignOfferDetailId());
                    offer.setCrmAppBannerUrl(campaignDetail.getCrmAppBannerUrl());
                    offer.setMinBillValue(clone.getOfferDetail().getMinValue());
                    offer.setOfferValue(clone.getOfferDetail().getValue());
                    offer.setOfferValueType(clone.getOfferDetail().getOfferType());
                    offer.setProductList(getProductsInOfferMapping(clone.getOfferDetail()));
                    sendGeneralOfferNotification(offer,generalOffer,customer.getOptWhatsapp());
                    return new Pair<>(covertToOfferResponse(generalOffer,campaignDetail,true),offer);
                }else{
                    LOG.info("DEFAULT-OFFER-CREATION :: no active general offer found");
                    return  new Pair<>(noOfferResponse(customer), null);
                }
            }
        }catch (Exception e){
            LOG.error("Error while creating general offer for customer id :: {} and campaign id :: {}",customerId, campaignDetail.getCampaignId(),e);
        }
        return null;
    }

    private List<Integer> getProductsInOfferMapping(OfferDetailData offerDetailData){
        List<Integer> pids = new ArrayList<>();
        for (OfferMetadata om : offerDetailData.getMetaDataMappings()) {
            if (AppConstants.ACTIVE.equals(om.getStatus()) && (OfferMetaDataType.PRODUCT.name().equals(om.getMappingType())
                    || "FREEBIE_PRODUCT".equals(om.getMappingType()))) {
                pids.add(Integer.valueOf(om.getMappingValue()));
            }
        }
        return pids;
    }

    @Override
    public Pair<MyOfferResponse, NextOffer> createDeliveryGeneralOffer(CreateNextOfferRequest request, Customer customer, CampaignDetail campaignDetail, Integer lagDays) {
        int customerId = customer.getId();
        String notificationType = "SMS";
        CustomerCampaignOfferDetail generalOffer = null;
        DeliveryCouponDetailData deliveryCoupon = null;
        String startDay = AppUtils.getDateString(AppUtils.getNextDate(AppUtils.getBusinessDate()),
                AppConstants.DATE_FORMAT);
        if(Objects.nonNull(campaignDetail.getStartDate())){
            startDay = AppUtils.getDateString(campaignDetail.getStartDate(),AppUtils.DATE_FORMAT_STRING);
        }
        try {
            LOG.info("DELIVERY-DEFAULT-OFFER-CREATION  :: starting process for campaign id :: {}",campaignDetail.getCampaignId());
            CustomerDineInView oneView = offerService.getCustomerDineInView(customerId,
                    AppConstants.CHAAYOS_BRAND_ID, Arrays.asList(-1));
            CustomerRepeatType customerRepeatType =  CustomerRepeatType.getCustomerRepeatTypeAfterPlacingOrder(
                    oneView.getDineInOrders() == null ? 0 : oneView.getDineInOrders(),
                    oneView.getActiveDineInOrders() == null ? 0 : oneView.getActiveDineInOrders(),
                    AppConstants.getValue(oneView.getAvailedSignupOffer()));
            CustomerRepeatType type=CustomerRepeatType.DEFAULT;
            Map<String, Map<Integer, CampaignMapping>> couponMap = campaignDetail.getMappings();
            if(AppConstants.getValue(campaignDetail.getNewCustomerOnly()) && !CustomerRepeatType.REGISTER.equals(customerRepeatType)){
                LOG.info("DEFAULT-OFFER-CREATION  :: skipping offer creation as campaign is only for new customer  customer id :: {}",customerId);
                return new Pair<>(newCustomerOnlyRes(customer),null);
            }
            CustomerMappingTypes customerMappingTypes = new CustomerMappingTypes(type.name(), 1,
                    couponMap.get(type.name()).get(1).getValidityInDays(), type.getDefaultCloneCode(), type.getReminderDays());
            if(!alreadyAvailedOffer(customerId,campaignDetail.getCampaignId())){
                LOG.info("DELIVERY-DEFAULT-OFFER-CREATION  :: creating new offer for customer id :: {}",customerId);
                CampaignMapping cloneCode = campaignCache.getCloneCode(type,
                        type.getJourneyCount(), campaignDetail.getCampaignId());
                if(Objects.nonNull(cloneCode)){
                    LOG.info("DELIVERY-DEFAULT-OFFER-CREATION :: found clone Code for type :: {} journey count :: {} campaign id :: {} clone code :: {}",
                            type.name(), type.getJourneyCount(), campaignDetail.getCampaignId(),JSONSerializer.toJSON(cloneCode));
                    if(campaignDetail.isCouponClone()) {
                        deliveryCoupon = offerService.getDeliveryCloneCode(cloneCode.getCode(), request.getBrandId(), true);
                        if(Objects.nonNull(deliveryCoupon)){
                            LOG.info("DELIVERY-DEFAULT-OFFER-CREATION :: found cloned code :: {}", JSONSerializer.toJSON(deliveryCoupon));
                            Integer couponDelay = Objects.nonNull(campaignDetail.getCouponApplicableAfter()) ? campaignDetail.getCouponApplicableAfter() : 0;
                            couponDelay = Math.max(couponDelay, lagDays);
                            Date startDate = AppUtils.getNextDate(AppUtils.addDays(AppUtils.getCurrentTimestamp(),couponDelay-1));
                            if(AppUtils.isBefore(startDate,deliveryCoupon.getStartDate())){
                                startDate = deliveryCoupon.getStartDate();
                            }
                            Date endDate = AppUtils.getDayBeforeOrAfterDay(startDate, cloneCode.getValidityInDays()-1);
                            if(AppUtils.isBefore(deliveryCoupon.getEndDate(), endDate)){
                                endDate = deliveryCoupon.getEndDate();
                            }
                            CouponData couponData = new CouponData(deliveryCoupon.getCouponCode(), AppUtils.getDateString(startDate,AppUtils.DATE_FORMAT_STRING),
                                    AppUtils.getDateString(endDate,AppUtils.DATE_FORMAT_STRING),campaignDetail.getUsageLimit(),null,null);

                            generalOffer = offerService.createDeliveryPostOrderOffer(request.getBrandId(), null, campaignDetail.getCampaignId(),
                                    customer, -1, customer.getContactNumber(), customer.getCountryCode(),
                                    couponData, customer.getFirstName(), cloneCode.getDesc(),deliveryCoupon.getMasterCoupon() ,
                                    deliveryCoupon,campaignDetail.isCouponClone(),campaignDetail, type, 1,request);
                            if(Objects.nonNull(generalOffer)){
                                NextOffer offer = getNextOffer(request.getBrandId(), customer.getContactNumber(), customerId,
                                        customer.getFirstName(), generalOffer, type,
                                        notificationType, getContentUrl(type));
                                offer.setTnc(campaignDetail.getCampaignDesc());
                                offer.setMaxUsage(deliveryCoupon.getMaxUsage());
                                offer.setCustomerCampaignOfferDetailId(generalOffer.getCapmpaignOfferDetailId());
                                offer.setCrmAppBannerUrl(campaignDetail.getCrmAppBannerUrl());
                                sendDeliveryPostOrderOfferNotification(offer,generalOffer,customer.getOptWhatsapp());
                                return new Pair<>(covertToOfferResponse(generalOffer, campaignDetail, false),offer);
                            }
                        }else{
                            LOG.info("DELIVERY-DEFAULT-OFFER-CREATION :: no cloned coupon found");
                            return  new Pair<>(noOfferResponse(customer),null);
                        }
                    }else {
                        LOG.info(
                                "DELIVERY_POST_ORDER_OFFER - Fetching master coupon detail as isClone Coupon is {}",
                                campaignDetail.isCouponClone());
                        deliveryCoupon = offerService.getDeliveryCloneCode(cloneCode.getCode(), request.getBrandId(), false);
                        if(Objects.nonNull(deliveryCoupon)){
                            Integer couponDelay = Objects.nonNull(campaignDetail.getCouponApplicableAfter()) ? campaignDetail.getCouponApplicableAfter() : 0;
                            Date startDate = AppUtils.getNextDate(AppUtils.addDays(AppUtils.getCurrentTimestamp(),couponDelay-1));
                            if(AppUtils.isBefore(startDate,deliveryCoupon.getStartDate())){
                                startDate = deliveryCoupon.getStartDate();
                            }
                            Date endDate = AppUtils.getDayBeforeOrAfterDay(startDate, cloneCode.getValidityInDays()-1);
                            if(AppUtils.isBefore(deliveryCoupon.getEndDate(), endDate)){
                                endDate = deliveryCoupon.getEndDate();
                            }
                            CouponData couponData = new CouponData(deliveryCoupon.getMasterCoupon(), AppUtils.getDateString(startDate,AppUtils.DATE_FORMAT_STRING),
                                    AppUtils.getDateString(endDate,AppUtils.DATE_FORMAT_STRING),campaignDetail.getUsageLimit(),null,null);
                            generalOffer = offerService
                                    .createDeliveryPostOrderOffer(request.getBrandId(), null, campaignDetail.getCampaignId(), customer, -1,
                                            customer.getContactNumber(), customer.getCountryCode(),
                                            couponData, customer.getFirstName(), cloneCode.getDesc(),
                                            deliveryCoupon.getMasterCoupon(),deliveryCoupon,campaignDetail.isCouponClone(),campaignDetail, type, 1,request);
                            if (generalOffer != null) {
                                LOG.info(
                                        "DELIVERY_POST_ORDER_OFFER - Generated Clone Coupon Successfully - Customer Id : {}, Order Id {} ",
                                        customerId, -1);
                                String contentUrl = getContentUrl(type);
                                NextOffer offer = getNextOffer(request.getBrandId(), customer.getContactNumber(), customerId,
                                        customer.getFirstName(), generalOffer, type,
                                        notificationType, contentUrl);
                                offer.setTnc(campaignDetail.getCampaignDesc());
                                offer.setMaxUsage(deliveryCoupon.getMaxUsage());
                                offer.setCustomerCampaignOfferDetailId(generalOffer.getCapmpaignOfferDetailId());
                                sendDeliveryPostOrderOfferNotification(offer, generalOffer,
                                        customer.getOptWhatsapp());
                                offer.setExistingOffer(false);
                                offer.setCrmAppBannerUrl(campaignDetail.getCrmAppBannerUrl());
                                return new Pair<>(covertToOfferResponse(generalOffer, campaignDetail, false),offer);
                            } else {
                                LOG.info(
                                        "DELIVERY_POST_ORDER_OFFER - Failed to Generate Clone Coupon - Customer Id : {}, Order Id {} ",
                                        customerId, -1);
                            }
                        }else {
                            LOG.info(
                                    "DELIVERY_POST_ORDER_OFFER - No Master coupon found for : {}",
                                    cloneCode.getCode());
                        }
                    }
                }else{
                    LOG.info("DELIVERY-DEFAULT-OFFER-CREATION  :: creation of new offer failed as no clone code found for " +
                            "campaign id :: {} and type :: {}",campaignDetail.getCampaignId(),CustomerRepeatType.DEFAULT);
                    return  new Pair<>(noOfferResponse(customer),null);
                }
            }else{
                LOG.info("DELIVERY-DEFAULT-OFFER-CREATION :: getting existing offer created for customer id : {}",customerId);
                generalOffer = offerService.getActiveOfferCampaign(customerId,campaignDetail.getCampaignId());
                if(Objects.nonNull(generalOffer)){
                    LOG.info("DELIVERY-DEFAULT-OFFER-CREATION :: Found delivery general offer with id :: {}",generalOffer.getCapmpaignOfferDetailId());
                    NextOffer offer = getNextOffer(request.getBrandId(), customer.getContactNumber(), customerId,
                            customer.getFirstName(), generalOffer, type,
                            notificationType, getContentUrl(type));
                    offer.setTnc(campaignDetail.getCampaignDesc());
                    offer.setCustomerCampaignOfferDetailId(generalOffer.getCapmpaignOfferDetailId());
                    offer.setCrmAppBannerUrl(campaignDetail.getCrmAppBannerUrl());
                    sendDeliveryPostOrderOfferNotification(offer,generalOffer,customer.getOptWhatsapp());
                    return new Pair<>(covertToOfferResponse(generalOffer,campaignDetail,true),offer);
                }else{
                    LOG.info("DELIVERY-DEFAULT-OFFER-CREATION :: no active delivery general offer found");
                    return  new Pair<>(noOfferResponse(customer),null);
                }
            }
        }catch (Exception e){
            LOG.error("Error while creating delivery general offer for customer id :: {} and campaign id :: {}",customerId, campaignDetail.getCampaignId(),e);
        }
        return null;
    }

    private MyOfferResponse covertToOfferResponse(CustomerCampaignOfferDetail detail, CampaignDetail campaignDetail, boolean isExistingOffer){
        MyOfferResponse response = new MyOfferResponse();
        response.setCampaignId(detail.getCampaignId());
        response.setOfferStatus(MyOfferResponseStatus.OFFER_FOUND.name());
        response.setValidityTill(AppUtils.getDateString(detail.getCouponEndDate(),AppUtils.DATE_FORMAT_STRING));
        response.setValidityFrom(AppUtils.getDateString(detail.getCouponStartDate(),AppUtils.DATE_FORMAT_STRING));
        response.setCustomerName(detail.getFirstName());
        response.setOfferCouponCode(detail.getCouponCode());
        response.setOfferDesc(detail.getOfferText());
        response.setRedirectionUrl(campaignDetail.getRedirectionUrl());
        response.setExistingOffer(isExistingOffer);
        response.setChannelPartnerId(detail.getChannelPartner());
        return response;
    }

    @Override
    public MyOfferResponse noOfferResponse(Customer customer){
        MyOfferResponse response = new MyOfferResponse();
        response.setCustomerName(customer.getFirstName());
        response.setOfferStatus(MyOfferResponseStatus.NO_OFFER_FOUND.name());
        return  response;
    }

    @Override
    public boolean testNotification(TestCampaignNotificationRequest request) {
        try {
            String validTill = AppUtils.getDateString(AppUtils.addDays(AppUtils.getCurrentTimestamp(),
                    request.getValidityInDays()),AppUtils.DATE_FORMAT_STRING);
            String code = null;
            if(request.getCouponClone()){
                code = request.getPrefix() + "1AZ2XA";
            }else{
                code = request.getSourceCoupon();
            }
            SMSWebServiceClient smsWebServiceClient = SolsInfiniWebServiceClient.getTransactionalClient(masterDataCache.getBrandMetaData().get(AppConstants.CHAAYOS_BRAND_ID));
            if(CampaignStrategy.NBO.name().equals(request.getStrategy()) || CampaignStrategy.GENERAL.name().equals(request.getStrategy())){
                NextOffer offer = new NextOffer(request.getFirstName(),request.getCouponDescription(), validTill,code,null);
                if(request.getJourney().equals(1)){
                    String message = CustomerSMSNotificationType.CLM_OFFER.getMessage(offer);
                    return notificationService.sendNotification(CustomerSMSNotificationType.CLM_OFFER.name(), message, request.getContactNumber(),
                            smsWebServiceClient, true, null);
                }else{
                    String messageAvailed = CustomerSMSNotificationType.CLM_NEXT_OFFER_FOR_AVAILED.getMessage(offer);
                    String messageNotAvailed = CustomerSMSNotificationType.CLM_NEXT_OFFER_FOR_NOT_AVAILED.getMessage(offer);
                    return notificationService.sendNotification(CustomerSMSNotificationType.CLM_NEXT_OFFER_FOR_AVAILED.name(), messageAvailed, request.getContactNumber(),
                            smsWebServiceClient, true, null) &&
                            notificationService.sendNotification(CustomerSMSNotificationType.CLM_NEXT_OFFER_FOR_NOT_AVAILED.name(), messageNotAvailed, request.getContactNumber(),
                                    smsWebServiceClient, true, null);
                }
            }else if(CampaignStrategy.DELIVERY_NBO.name().equals(request.getStrategy()) || CampaignStrategy.DELIVERY_GENERAL.name().equals(request.getStrategy())){
                NextOffer offer = new NextOffer(request.getFirstName(),request.getCouponDescription(), validTill,code,"ZOMATO");
                if(request.getJourney().equals(1)){
                    String message = CustomerSMSNotificationType.CLM_OFFER_DELIVERY.getMessage(offer);
                    return notificationService.sendNotification(CustomerSMSNotificationType.CLM_OFFER_DELIVERY.name(), message, request.getContactNumber(),
                            smsWebServiceClient, true, null);
                }else{
                    String messageAvailed = CustomerSMSNotificationType.CLM_DELIVERY_NEXT_OFFER_FOR_AVAILED.getMessage(offer);
                    String messageNotAvailed = CustomerSMSNotificationType.CLM_DELIVERY_NEXT_OFFER_FOR_NOT_AVAILED.getMessage(offer);
                    return notificationService.sendNotification(CustomerSMSNotificationType.CLM_DELIVERY_NEXT_OFFER_FOR_AVAILED.name(), messageAvailed, request.getContactNumber(),
                            smsWebServiceClient, true, null) &&
                            notificationService.sendNotification(CustomerSMSNotificationType.CLM_DELIVERY_NEXT_OFFER_FOR_NOT_AVAILED.name(), messageNotAvailed, request.getContactNumber(),
                                    smsWebServiceClient, true, null);
                }
            }
        }catch (Exception e){
            LOG.error("Error while sending test notification for data : {}",JSONSerializer.toJSON(request),e);
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void removeSecondFreeChai(Integer customerId,String signupOfferStatus) {
        LoyaltyScore score = dao.getCustomerLoyaltyScore(customerId);
        if(Objects.nonNull(score) && AppConstants.NO.equals(score.getAvailedSignupOffer())){
            score.setAvailedSignupOffer(AppConstants.YES);
            score.setSignupOfferExpired(AppConstants.YES);
            score.setSignupOfferExpiryTime(AppUtils.getCurrentTimestamp());
            score.setSignupOfferStatus(signupOfferStatus);
            dao.update(score);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<DayCloseEstimateData> getDayCloseEstimatesData(List<Integer> fountain9UnitIds, Date startDate, Date endDate) {
        List<DayCloseEstimateData> estimateData = new ArrayList<>();
        try {
            List<Object[]> list = dao.getDayCloseEstimatesData(fountain9UnitIds,startDate,endDate);
            for (Object[] data : list) {
                DayCloseEstimateData entry = new DayCloseEstimateData();
                entry.setEstimateDataId((Integer) data[0]);
                entry.setUnitId((Integer) data[1]);
                entry.setBrandId((Integer) data[2]);
                Integer productId = (Integer) data[3];
                entry.setProductId(productId);
                Product product = masterDataCache.getProduct(productId);
                if (Objects.nonNull(product)) {
                    entry.setProductName(masterDataCache.getProduct(productId).getName());
                }
                else {
                    continue;
                }
                entry.setDimension((String) data[4]);
                entry.setQuantity((Integer) data[5]);
                entry.setBusinessDate(AppUtils.getDate((Date) data[6]));
                entry.setOrderSource((String) data[12]);
                estimateData.add(entry);
            }
        }
        catch (Exception e) {
            LOG.error("Error Occurred while getting the day close Estimates Data..!",e);
        }
        return estimateData;
    }

    private MyOfferResponse newCustomerOnlyRes(Customer customer){
        MyOfferResponse response = new MyOfferResponse();
        response.setCustomerName(customer.getFirstName());
        response.setOfferStatus(MyOfferResponseStatus.OFFER_ONLY_FOR_NEW.name());
        return  response;
    }

    private boolean alreadyAvailedOffer(int customerId, Integer campaignId) {
        return dao.alreadyAvailedOffer(customerId,campaignId);
    }

    private CouponData getCouponData(CouponDetailData couponDetail, String contactNumber, Integer couponDelay, Integer validityInDays) {
        couponDelay = (Objects.nonNull(couponDelay)) ? couponDelay :0;
        Date today = AppUtils.getNextDate(AppUtils.addDays(AppUtils.getBusinessDate(),couponDelay-1));
        Date startDate = null;
        if(AppUtils.isBefore(today,couponDetail.getStartDate())){
            startDate = couponDetail.getStartDate();
        }else{
            startDate = today;
        }
        Date endDate = AppUtils.addDays(startDate,validityInDays);
        if(!AppUtils.isBefore(endDate,couponDetail.getEndDate())){
            endDate = AppUtils.addDays(couponDetail.getEndDate(),1);
        }
        if(Objects.nonNull(couponDetail)){
            CouponData couponData  = new CouponData(couponDetail.getCouponCode(), AppUtils.getDateString(startDate),
                    AppUtils.getDateString(endDate), couponDetail.getUsageCount(),
                    couponDetail.getCouponDetailId(), couponDetail.getOfferDetail().getOfferDetailId());
            return couponData;
        }
        return null;
    }

    private String sendGeneralOfferNotification(NextOffer offer, CustomerCampaignOfferDetail postOrderOfferCreationSuccess, String optWhatsapp) {
        boolean status = false;
        Brand brand = masterDataCache.getBrandMetaData().get(offer.getBrandId());
        SMSWebServiceClient smsWebServiceClient = SolsInfiniWebServiceClient.getTransactionalClient(brand);
        LOG.info("Next Off De {} ::::: {}", offer.toString(), offer.isAvailable());
        if (Objects.nonNull(offer) && offer.isAvailable()) {
            LOG.info("Next Best Offer SMS Details to be send to customer :: {}", offer.getContactNumber());
            status = sendNextBestOfferNotification(offer, smsWebServiceClient,postOrderOfferCreationSuccess,optWhatsapp);
        }
        LOG.info("Message Status For Customer {} :: {}", offer.getContactNumber(), status);
        return "SMS";
    }

	private boolean sendNextBestOfferNotification(NextOffer offer, SMSWebServiceClient smsWebServiceClient,
			CustomerCampaignOfferDetail postOrderOfferCreationSuccess, String optWhatsapp) {
		try {
			long startTime = System.currentTimeMillis();
			String message = null;
			boolean clvFlag = properties.getCleverTapEnabled();
			boolean sysGenNotification = properties.getSystemGeneratedNotifications();
			if (offer.getContentUrl() != null && !AppConstants.LOYAL_TEA_COUPON_CODE
					.equals(postOrderOfferCreationSuccess.getCampaignCloneCode())) {
				message = CustomerSMSNotificationType.CLM_OFFER.getMessage(offer);
				if (sysGenNotification || properties.getIsSendSmsForCampaignBySystem()) {
					return notificationService.sendNotification(CustomerSMSNotificationType.CLM_OFFER.name(), message,
							offer.getContactNumber(), smsWebServiceClient, true, getNotificationPayload(
									CustomerSMSNotificationType.CLM_OFFER, postOrderOfferCreationSuccess, optWhatsapp));
				}  if (clvFlag && offer.getBrandId().equals(Integer.valueOf(1))) {
					LOG.info("Sending offer Details to clevertap for Customer {} ",offer.getCustomerId());
					CleverTapPushResponse response = cleverTapDataPushService.uploadProfileAttributes(
							offer.getCustomerId(), AppUtils.getCurrentTimestamp().toInstant().getEpochSecond(),
							CleverTapConstants.REGULAR, cleverTapConverter.convert(offer, CleverTapEvents.GENERAL_OFFER,
									postOrderOfferCreationSuccess));
					LOG.info("Sending General offer Data to Clevertap took {} ms",System.currentTimeMillis()-startTime);
					return response.getStatus().equalsIgnoreCase(CleverTapConstants.SUCCESS);
				}

			} else {
				// DONT_SEND LOYALTEA SMS
				return true;

			}
		} catch (Exception e) {
			LOG.error("Error while sending CLM Repeat SMS to Customer :: {}", offer.getContactNumber());
		}
		return false;
	}

    private NotificationPayload getNotificationPayload(CustomerSMSNotificationType type, CustomerCampaignOfferDetail customerCampaignOfferDetail, String optWhatsapp) {
        try {
            Map<String,String> map= new HashMap<>();
            map.put("couponValidity",AppUtils.getDateInMonth(customerCampaignOfferDetail.getCouponEndDate()));
            map.put("couponCode",customerCampaignOfferDetail.getCouponCode());
            map.put("offerDescription",customerCampaignOfferDetail.getOfferText());
            NotificationPayload payload= new NotificationPayload();
            payload.setContactNumber(customerCampaignOfferDetail.getContactNumber());
            payload.setCustomerId(customerCampaignOfferDetail.getCustomerId());
            payload.setSendWhatsapp(type.isWhatsapp());
            payload.setMessageType(type.name());
            payload.setWhatsappOptIn(AppConstants.YES.equals(optWhatsapp));
            payload.setPayload(map);
            payload.setRequestTime(AppUtils.getCurrentTimestamp());
            return payload;
        } catch (Exception e){
            LOG.error("Exception Faced While Generating Notification Payload for Contact ::: {}",customerCampaignOfferDetail.getContactNumber());
            return null;
        }
    }

    private NextOffer getNextOffer(Integer brandId, String contactNumber, Integer customerId, String firstName,
                                   CustomerCampaignOfferDetail r, CustomerRepeatType type, String notificationType, String contentUrl) {
        String channelPartner = null;
        if (r == null) {
            return new NextOffer(brandId, contactNumber, customerId, firstName, false, null, null, null, null, null,
                    null, null,null,null,null);
        }
        if(Objects.nonNull(r.getChannelPartner())){
            channelPartner = masterDataCache.getChannelPartner(r.getChannelPartner()).getName();
        }
        return new NextOffer(brandId, contactNumber, customerId, firstName, true, r.getCouponCode(),
                AppUtils.getDateString(r.getCouponStartDate()), AppUtils.getDateString(r.getCouponEndDate()),
                r.getOfferText(), type.name(), contentUrl, notificationType, channelPartner,r.getCouponType(),
                r.getChannelPartner());
    }

    private String getContentUrl(CustomerRepeatType type) {
        String contentUrl = null;
        switch (type) {
            case REGISTER:
                contentUrl = "CLM_REGISTER";
                break;
            case REPEAT:
                contentUrl = "CLM_REPEAT";
                break;
            case DORMANT:
                contentUrl = "CLM_DORMANT";
                break;
            case NEW:
                contentUrl = "LOYAL_TEA";
                break;
            case DEFAULT:
                contentUrl = "DEFAULT";
            default:
                contentUrl = "LOYAL_TEA";
        }
        return contentUrl;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public OrderComplaintResponse updateOrderComplaintZomato(OrderComaplaintZomatoRequest orderComplaintZomatoRequest) {
        try {
            OrderComplaintResponse orderComplaintResponse=new OrderComplaintResponse();
            if (Objects.nonNull(orderComplaintZomatoRequest)) {
                OrderComplaint orderComplaint = new OrderComplaint();
                orderComplaint.setExternalOrderId(orderComplaintZomatoRequest.getExternalOrderId());
                orderComplaint.setOrderId(orderComplaintZomatoRequest.getOrderId());
                orderComplaint.setReferenceId(orderComplaintZomatoRequest.getReferenceId());
                orderComplaint.setComplaintMessage(orderComplaintZomatoRequest.getComplaintMessage());
                orderComplaint.setComplaintReason(orderComplaintZomatoRequest.getComplaintReason());
                orderComplaint.setCreatedAt(orderComplaintZomatoRequest.getCreatedAt());
                orderComplaint.setExpiredAt(orderComplaintZomatoRequest.getExpiredAt());
                orderComplaint.setCustomerComplaintsCount(orderComplaintZomatoRequest.getCustomerComplaintsCount());
                orderComplaint.setRepeatCustomerCount(orderComplaintZomatoRequest.getRepeatCustomerCount());
                orderComplaint.setMinCustomRefund(orderComplaintZomatoRequest.getMinCustomRefund());

                OrderComplaint orderComplaint1 = dao.add(orderComplaint);
                if (Objects.nonNull(orderComplaintZomatoRequest.getImageUrlsList())) {
                    List<ImageUrls> imageUrlsList = new ArrayList<>();
                    for (String imageElement : orderComplaintZomatoRequest.getImageUrlsList()) {
                        ImageUrls imageUrls = new ImageUrls();
                        imageUrls.setUrl(imageElement);
                        imageUrls.setOrderComplaint(orderComplaint1);
                        ImageUrls imageUrls1 = dao.add(imageUrls);
                        imageUrlsList.add(imageUrls1);
                    }
                    orderComplaint1.setImageUrlsList(imageUrlsList);
                }
                if (Objects.nonNull(orderComplaintZomatoRequest.getRefundOptionsList())) {
                    List<RefundOptions> refundOptionsList = new ArrayList<>();
                    for (RefundOptionsRequest refundOptionsRequest : orderComplaintZomatoRequest.getRefundOptionsList()) {
                        RefundOptions refundOptions = new RefundOptions();
                        refundOptions.setAmount(refundOptionsRequest.getAmount());
                        refundOptions.setRefundType(refundOptionsRequest.getRefundType());
                        refundOptions.setId(refundOptionsRequest.getId());
                        refundOptions.setOrderComplaint(orderComplaint1);
                        RefundOptions refundOptions1 = dao.add(refundOptions);
                        refundOptionsList.add(refundOptions1);
                    }
                    orderComplaint1.setRefundOptionsList(refundOptionsList);
                }
                if (Objects.nonNull(orderComplaintZomatoRequest.getOrderedItemsList())) {
                    List<OrderItemComplaint> orderItemComplaintList = new ArrayList<>();
                    for (OrderItemRequest orderItemRequest : orderComplaintZomatoRequest.getOrderedItemsList()) {
                        OrderItemComplaint orderItemComplaint = new OrderItemComplaint();
                        orderItemComplaint.setDishId(orderItemRequest.getDishId());
                        orderItemComplaint.setName(orderItemRequest.getName());
                        orderItemComplaint.setQuantity(orderItemRequest.getQuantity());
                        orderItemComplaint.setTotalCost(orderItemRequest.getTotalCost());
                        orderItemComplaint.setOrderComplaint(orderComplaint1);
                        OrderItemComplaint orderItemComplaint1 = dao.add(orderItemComplaint);
                        orderItemComplaintList.add(orderItemComplaint1);
                    }
                    orderComplaint1.setOrderItemComplaintList(orderItemComplaintList);
                }
                dao.update(orderComplaint1);
            }
            orderComplaintResponse.setCode("200");
            orderComplaintResponse.setStatus("success");
            return orderComplaintResponse;
        }
        catch(Exception e){
            return null;
        }

    }

    private String sendDeliveryPostOrderOfferNotification(NextOffer offer, CustomerCampaignOfferDetail postOrderOfferCreationSuccess, String optWhatsapp) {
        boolean status = false;
        Brand brand = masterDataCache.getBrandMetaData().get(offer.getBrandId());
        SMSWebServiceClient smsWebServiceClient = SolsInfiniWebServiceClient.getTransactionalClient(brand);
        LOG.info("Delivery Next Off De {} ::::: {}", offer, offer.isAvailable());
        if (Objects.nonNull(offer) && offer.isAvailable()) {
            LOG.info("Delivery Next Best Offer SMS Details to be send to customer :: {}", offer.getContactNumber());
            status = sendDeliveryNextBestOfferNotification(offer, smsWebServiceClient,postOrderOfferCreationSuccess,optWhatsapp);
        }
        LOG.info("Message Status For Customer {} :: {}", offer.getContactNumber(), status);
        return "SMS";
    }

	private boolean sendDeliveryNextBestOfferNotification(NextOffer offer, SMSWebServiceClient smsWebServiceClient,
			CustomerCampaignOfferDetail postOrderOfferCreationSuccess, String optWhatsapp) {
		try {
			long startTime = System.currentTimeMillis();
			String message = null;
			boolean clvFlag = properties.getCleverTapEnabled();
			boolean sysGenNotification = properties.getSystemGeneratedNotifications();
			if (offer.getContentUrl() != null && !AppConstants.LOYAL_TEA_COUPON_CODE
					.equals(postOrderOfferCreationSuccess.getCampaignCloneCode())) {
				message = CustomerSMSNotificationType.CLM_OFFER_DELIVERY.getMessage(offer);
				if (sysGenNotification || properties.getIsSendSmsForCampaignBySystem()) {
					return notificationService.sendNotification(CustomerSMSNotificationType.CLM_OFFER_DELIVERY.name(),
							message, offer.getContactNumber(), smsWebServiceClient, true,
							getNotificationPayload(CustomerSMSNotificationType.CLM_OFFER_DELIVERY,
									postOrderOfferCreationSuccess, optWhatsapp));
				}  if (clvFlag && offer.getBrandId().equals(Integer.valueOf(1))) {
					LOG.info("Sending offer Details to clevertap for Customer {} ",offer.getCustomerId());
					CleverTapPushResponse response = cleverTapDataPushService.uploadProfileAttributes(offer.getCustomerId(),
							AppUtils.getCurrentTimestamp().toInstant().getEpochSecond(),CleverTapConstants.REGULAR, cleverTapConverter.convert(offer,
									CleverTapEvents.DELIVERY_GENERAL_OFFER, postOrderOfferCreationSuccess));
					LOG.info("Sending delivery general offer Data to Clevertap took {} ms",System.currentTimeMillis()-startTime);
					return response.getStatus().equalsIgnoreCase(CleverTapConstants.SUCCESS);
				}

			} else {
				// DONT_SEND LOYALTEA SMS
				return true;

			}
		} catch (Exception e) {
			LOG.error("Error while sending CLM Repeat SMS to Customer :: {}", offer.getContactNumber());
		}
		return false;
	}

	private void sendSubscriptionPurchaseNotification(SubscriptionPlan subscriptionPlan, Brand brand, Customer customer,
			Integer subscriptionProduct, Integer unitId, Integer orderId) {
		if (Objects.nonNull(subscriptionPlan)) {
			SMSWebServiceClient smsWebServiceClient = SolsInfiniWebServiceClient.getTransactionalClient(brand);
			SubscriptionOfferInfoDetail subscriptionInfoDetail = mappingCache
					.getSubscriptionInfoDetail(subscriptionPlan);
			if (Objects.nonNull(subscriptionInfoDetail)) {
				LOG.info("Subscription SMS Details to be send to customer :: {}", customer.getContactNumber());
				sendSubscriptionNotification(subscriptionPlan, brand, customer, smsWebServiceClient,
						subscriptionInfoDetail, subscriptionProduct, unitId, orderId);
			}
		}

	}

    protected void sendSubscriptionNotification(SubscriptionPlan subscriptionPlan, Brand brand, Customer customer,
                                                SMSWebServiceClient smsWebServiceClient, SubscriptionOfferInfoDetail subscriptionInfoDetail,
                                                Integer subscriptionProduct, Integer unitId, Integer orderId) {
        try {
            String subscriptionName = getSubscriptionName(subscriptionProduct, unitId);
            subscriptionInfoDetail.setCustomerName(customer.getFirstName());
            subscriptionInfoDetail.setValidDays((int) AppUtils.getDayDifference(subscriptionPlan.getPlanStartDate(),
                    subscriptionPlan.getPlanEndDate()));
            subscriptionInfoDetail.setExpiryDate(subscriptionPlan.getPlanEndDate());
            subscriptionInfoDetail
                    .setSubscriptionUrl(Objects.nonNull(brand.getChaayosSubscription()) ? brand.getChaayosSubscription()
                            : "chaayos.com/pages/chaayos-select");
            Map<String, String> map = new HashMap<>();
            map.put("firstName", customer.getFirstName());
            map.put("productName", subscriptionName);
            map.put("validityInDays", subscriptionInfoDetail.getValidDays().toString());
            map.put("offerDescription", subscriptionInfoDetail.getOfferText());
            map.put("endDate", AppUtils.getDateInMonth(subscriptionInfoDetail.getExpiryDate()));
            sendSubscriptionNotification(getSubscriptionView(subscriptionInfoDetail), smsWebServiceClient, customer,
                    map, orderId);
        } catch (Exception e) {
            LOG.error("WHATSAPP_NOTIFICATOIN :: Exception faced while sending Subscription notification :: {}",
                    customer.getId(), e);
        }
    }

    private String getSubscriptionName(Integer subscriptionProduct, Integer unitId) {
        try {

            Map<com.stpl.tech.master.domain.model.Pair<BigDecimal, String>, String> productVOS = masterDataCache
                    .getUnitProductAlias(unitId, subscriptionProduct);
            if (!productVOS.isEmpty()) {
                Optional<Pair<BigDecimal, String>> firstKey = productVOS.keySet()
                        .stream().findFirst();
                if (firstKey.isPresent() && productVOS.get(firstKey.get()) != null) {
                    return productVOS.get(firstKey.get());
                } else {
                    return masterDataCache.getProduct(subscriptionProduct).getName();
                }
            } else {
                return masterDataCache.getProduct(subscriptionProduct).getName();
            }
        } catch (Exception e) {
            LOG.error("CHAAYOS_SUBSCRIPTION ::: Exception Faced While Fetching Subscription Name", e);
        }
        return "Chaayos Membership";
    }

    private SubscriptionViewData getSubscriptionView(SubscriptionOfferInfoDetail subscriptionInfoDetail) {
        SubscriptionViewData subscriptionViewData = new SubscriptionViewData();
        subscriptionViewData.setCustomerName(subscriptionInfoDetail.getCustomerName());
        com.stpl.tech.master.domain.model.Pair<CouponDetail, Product> couponMapping = masterDataCache.getSubscriptionSkuCodeDetail(subscriptionInfoDetail.getSubscriptionCode());
        subscriptionViewData.setSubscriptionName(couponMapping.getValue().getName());
        subscriptionViewData.setOfferDescription(couponMapping.getKey().getOffer().getDescription());
        subscriptionViewData.setValidityDays(subscriptionInfoDetail.getValidDays());
        subscriptionViewData.setPlanEndDate(subscriptionInfoDetail.getExpiryDate());
        return subscriptionViewData;
    }

    protected boolean sendSubscriptionNotification(SubscriptionViewData subscriptionInfoDetail,
                                                   SMSWebServiceClient smsWebServiceClient, Customer customer, Map<String, String> map, Integer orderId)
            throws IOException {
        try {
            String message = CustomerSMSNotificationType.CHAAYOS_SUBSCRIPTION.getMessage(subscriptionInfoDetail);
            return notificationService.sendNotification(CustomerSMSNotificationType.CHAAYOS_SUBSCRIPTION.name(),
                    message, customer.getContactNumber(), smsWebServiceClient,
                    props.getAutomatedNPSSMS(),
                    getNotificationPayload(CustomerSMSNotificationType.CHAAYOS_SUBSCRIPTION, customer, map, orderId));
        } catch (Exception e) {
            LOG.error("Error while sending Subscription SMS to Customer :: {}", customer.getContactNumber());
            return false;
        }
    }

    private NotificationPayload getNotificationPayload(CustomerSMSNotificationType type, Customer customer,
                                                       Map<String, String> map, Integer orderId) {
        try {
            NotificationPayload load = new NotificationPayload();
            if (Objects.nonNull(customer)) {
                load.setCustomerId(customer.getId());
            }
            load.setContactNumber(customer.getContactNumber());
            load.setOrderId(orderId);
            load.setMessageType(type.name());
            load.setSendWhatsapp(type.isWhatsapp());
            if (Objects.nonNull(customer.getOptWhatsapp())) {
                load.setWhatsappOptIn(customer.getOptWhatsapp().equals(AppConstants.YES));
            } else {
                load.setWhatsappOptIn(false);
            }

            load.setRequestTime(AppUtils.getCurrentTimestamp());
            load.setPayload(map);
            return load;
        } catch (Exception e) {
            LOG.error("WHATAPP_NOTIFICATION ::: Exception Faced While Generating the Notification Payload ::: {}",
                    orderId);
            return null;
        }
    }

    private String getOrderType(OrderInfo info) {
        for (OrderItem item : info.getOrder().getOrders()) {
            if (Objects.nonNull(masterDataCache.getSubscriptionProductDetail(item.getProductId()))) {
                return CleverTapEvents.SUBSCRIPTION_PURCHASED_EVENT;
            } else if (info.getOrder().isGiftCardOrder()) {
                return CleverTapEvents.WALLET_PURCHASED_EVENT;
            }
        }
        return CleverTapEvents.CHARGED;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean updateOrderStatus(String generatedOrderId){
        return dao.updateOrderStatusByKettleAdmin(generatedOrderId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<MonthlyAOVDetail> getPartnerMonthAndBrandWiseAOV(PartnerAOVRequest partnerAOVRequest) {
        return dao.getPartnerBrandAndMonthWiseAOVData(partnerAOVRequest);
    }

    private void pushOrderNotification(OrderInfo order) {
        OrderPushNotification notification = new OrderPushNotification(order.getOrder().getOrderId(),
                order.getOrder().getStatus(), order.getOrder().getSource(), order.getUnit().getId(),
                props.getAssemblyFirestoreUnits(), String.valueOf(props.isAssemblyFirestoreEnabledForAll()), FireStoreNotificationType.ORDER);
        Integer unitId = order.getUnit().getId();
        EnvType envType = props.getEnvironmentType();
        String relayType = TransactionUtils.getAssemblyRelay(unitId, order.getOrder().getSource());
        notification.setTopic(AppUtils.getAssemblyChannelName(envType.name(), unitId, relayType));
        try {
            firebaseNotificationService.sendNotification(envType, notification);
        } catch (Exception e) {
            LOG.error("Error while sending push notification to the client", e);
            new ErrorNotification("FCM Push Notification Faliure",
                    "Error while sending push notification to the client", e, envType).sendEmail();
        }
    }

    @Override
    public void notifyOverWebsocket(OrderInfo info,Boolean isRoutedToAssembly) {
        LOG.info("trying to get Unit :: {}",new Gson().toJson(info));
        Integer id = info.getUnit().getId();
        LOG.info("unit Id : {}",id);
        Unit unitData = masterDataCache.getUnit(info.getUnit().getId());
        // preparing socket channel to publish to subscribers
        String webSocketChannel = AppConstants.WEB_SOCKET_CHANNEL + unitData.getId()
                + AppConstants.WEB_SOCKET_CHANNEL_ORDERS;
        if (Boolean.TRUE.equals(isRoutedToAssembly)) {
            // adding to orderInfo cache
            ordersCache.addToCache(info);
            // add to list of orders which are yet to be delivered via sockets
            ordersCache.addToUndelivered(info);

            LOG.info("Sending to the assembly screen of {} :::::: {}", info.getOrder().getUnitName(),
                    info.getOrder().getGenerateOrderId());

            if (unitData.isWorkstationEnabled()) {
                pushOrderNotification(info);
            } else {
                // publishing to web socket channel subscriber
                OrderInfo duplicate = new OrderInfo(null, info.getOrder(), info.getCustomer(),
                        info.getDeliveryPartner(), info.getChannelPartner(),
                        unitData.isWorkstationEnabled() ? null : info.getReceipts(), null, info.getDeliveryDetails(),
                        info.getPrintType());
                template.convertAndSend(webSocketChannel, duplicate);
            }
        }

    }

    private void deleteReciepts(int unitId, int orderId) {
        try {
            String basePath = properties.getBasePath() + AppConstants.FORWARD_SLASH + unitId
                    + AppConstants.ORDER_RECEIPT_PATH + orderId;
            FileUtils.deleteDirectory(new File(basePath));
        } catch (Exception e) {
            LOG.info("Exception occured during cleaning order receipts for order {} ", orderId, e);
        }
    }

    private void settlePartnerOrderAfterPickUp(OrderDeliveryStatusUpdate orderDeliveryStatusUpdate) throws DataNotFoundException, TemplateRenderingException {
        LOG.info("Trying To Settle Order After Order Id : {} is Picked Up",orderDeliveryStatusUpdate.getOrderId());
        Integer orderId  = Integer.valueOf(orderDeliveryStatusUpdate.getOrderId());
        OrderInfo orderToSettle = ordersCache.getOrderById(orderId, UnitCategory.COD, true);
        if(Objects.isNull(orderToSettle) || !orderToSettle.getOrder().getStatus().equals(OrderStatus.READY_TO_DISPATCH)){
            return;
        }
        boolean result = updateOrderStatusInCache(orderId, OrderStatus.SETTLED,
                AppConstants.SYSTEM_EMPLOYEE_ID, orderDeliveryStatusUpdate.getUnitId(),UnitCategory.COD , null, false, null, null);
        LOG.info("Status update for order {} is {}", orderDeliveryStatusUpdate.getOrderId(), result);
        if (result) {
            OrderInfo order = ordersCache.getOrderById(orderId, UnitCategory.COD, false);
            orderToSettle.getOrder().setStatus(OrderStatus.SETTLED);
            if (order == null) {
                LOG.info("###NOT FOUND ORDER IN CACHE for {}",orderDeliveryStatusUpdate.getOrderId());
            } else {
                LOG.info("### FOUND ORDER IN CACHE for {} and customer id {}", orderId, order.getOrder().getCustomerId());
            }
            sendFCMNotificationToAssembly(orderToSettle);
            postUpdateActions(orderId,
                    order != null && order.getOrder().getCustomerId() > 5 ? order.getOrder().getCustomerId() : null,
                    order != null ? order.getOrder().getChannelPartner() : null, true,
                    OrderStatus.SETTLED);
        }
    }

    private PartnerOrderRiderStatesDetail getPartnerOrderRiderLogsObject(OrderDeliveryStatusUpdate orderDeliveryStatusUpdate ){
          return   PartnerOrderRiderStatesDetail.builder().partnerOrderId(orderDeliveryStatusUpdate.getPartnerOrderId()).kettleOrderId(
                    Integer.parseInt(orderDeliveryStatusUpdate.getOrderId())).partnerName(orderDeliveryStatusUpdate.getPartnerName())
                  .billingServerTime(orderDeliveryStatusUpdate.getBillingServerTime()).build();
    }

    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    private PartnerOrderRiderStatesDetail updatePartnerOrderRiderStates(PartnerOrderRiderStatesDetail partnerOrderRiderStatesDetail,OrderDeliveryStatusUpdate orderDeliveryStatusUpdate){
        if(Objects.nonNull(orderDeliveryStatusUpdate.getRiderContact())){
            partnerOrderRiderStatesDetail.setRiderName(orderDeliveryStatusUpdate.getRiderName());
            partnerOrderRiderStatesDetail.setRiderContact(orderDeliveryStatusUpdate.getRiderContact());
        }
        switch (orderDeliveryStatusUpdate.getStatus()){
            case "rider-assigned":
            case "CONFIRMED":
                partnerOrderRiderStatesDetail.setRiderAssignedAt(orderDeliveryStatusUpdate.getAddTime());
                break;
            case "rider-arrived":
            case "ARRIVED":
                partnerOrderRiderStatesDetail.setRiderArrivedAt(orderDeliveryStatusUpdate.getAddTime());
                break;
            case "pickedup":
            case "PICKEDUP":
                partnerOrderRiderStatesDetail.setOrderPickupTime(orderDeliveryStatusUpdate.getAddTime());
                break;
            case "delivered":
            case "DELIVERED":
                partnerOrderRiderStatesDetail.setOrderDeliveryTime(orderDeliveryStatusUpdate.getAddTime());
                break;
        }
        return partnerOrderRiderStatesDetailDao.save(partnerOrderRiderStatesDetail);
    }



    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void updateOrderForDeliveryStatusUpdate(OrderDeliveryStatusUpdate orderDeliveryStatusUpdate) throws DataNotFoundException, TemplateRenderingException {
            PartnerOrderRiderStatesDetail partnerOrderRiderStatesDetail = null;

            Optional<PartnerOrderRiderStatesDetail> partnerOrderRiderStatesDetailOptional = partnerOrderRiderStatesDetailDao.
                    findByPartnerOrderId(orderDeliveryStatusUpdate.getPartnerOrderId());
            if(!partnerOrderRiderStatesDetailOptional.isPresent()){
                    partnerOrderRiderStatesDetail = getPartnerOrderRiderLogsObject(orderDeliveryStatusUpdate);
                    partnerOrderRiderStatesDetail = partnerOrderRiderStatesDetailDao.saveAndFlush(partnerOrderRiderStatesDetail);
            }else{
                partnerOrderRiderStatesDetail = partnerOrderRiderStatesDetailOptional.get();
            }
            partnerOrderRiderStatesDetail = updatePartnerOrderRiderStates(partnerOrderRiderStatesDetail,orderDeliveryStatusUpdate);
            OrderInfo orderInfo = ordersCache.getOrderById(Integer.parseInt(orderDeliveryStatusUpdate.getOrderId()),UnitCategory.COD,false);
            PartnerOrderRiderStatesDetailData partnerOrderRiderStatesDetailData = DataConverter.convert(partnerOrderRiderStatesDetail);
            if(Objects.nonNull(orderInfo)){
                orderInfo.setPartnerOrderRiderStates(partnerOrderRiderStatesDetailData);
                ordersCache.addToCache(orderInfo);
            }

            try{
                switch (orderDeliveryStatusUpdate.getPartnerName()){
                    case "SWIGGY":
                        if(Objects.nonNull(orderDeliveryStatusUpdate.getStatus()) && PartnerOrderRiderStates.ORDER_PICKED_UP.getSwiggy().
                                equals(orderDeliveryStatusUpdate.getStatus())){
                            settlePartnerOrderAfterPickUp(orderDeliveryStatusUpdate);
                        }
                        break;
                    case "ZOMATO":
                        if(Objects.nonNull(orderDeliveryStatusUpdate.getStatus()) && PartnerOrderRiderStates.ORDER_PICKED_UP.getZomato().
                                equals(orderDeliveryStatusUpdate.getStatus())){
                            settlePartnerOrderAfterPickUp(orderDeliveryStatusUpdate);
                        }
                        break;
                }
            }catch (Exception e){
                LOG.error("Error While Settling Order Id :::: {} Of Partner :::: {} After Pickup ",orderDeliveryStatusUpdate.getOrderId() ,
                        orderDeliveryStatusUpdate.getPartnerName(),e.getMessage());
            }

    }

    private void sendFCMNotificationToAssembly(OrderInfo order) {
        Date startTime = AppUtils.getCurrentTimestamp();
        if (Objects.nonNull(order)) {
            OrderPushNotification notification = new OrderPushNotification(order.getOrder().getOrderId(),
                    order.getOrder().getStatus(), order.getOrder().getSource(), order.getUnit().getId(),
                    props.getAssemblyFirestoreUnits(), String.valueOf(props.isAssemblyFirestoreEnabledForAll()), FireStoreNotificationType.ORDER_PICKED_UP);
            Integer unitId = order.getUnit().getId();
            EnvType envType = props.getEnvironmentType();
            String relayType = TransactionUtils.getAssemblyRelay(unitId, order.getOrder().getSource());
            notification.setTopic(AppUtils.getAssemblyOrderDispatchedChannelName(envType.name(), unitId, relayType));
            notification.setSendToAndroid(AppConstants.YES);
            try {
                fireBaseService.sendNotification(envType, notification);
                Date endTime = AppUtils.getCurrentTimestamp();
                LOG.info("Dispatch Status FCM notification pushed for Order Id: {} sent at time: {} and took Total Time: {} secs",
                        order.getOrder().getOrderId(), endTime, ((double) (endTime.getTime() - startTime.getTime())) / 1000);
            } catch (Exception e) {
                LOG.error("Error while sending Partner Order Dispatched push notification to the client for orderId: {}"
                        , order.getOrder().getOrderId(), e);
                new ErrorNotification("FCM Push Notification Failure",
                        "Error while sending push notification to the client", e, envType).sendEmail();
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionReadDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Map<String, BigDecimal> getMonthlyOrdersOfRecipe(Integer recipeId, String region) {
        List<Integer> regionUnitIds = new ArrayList<>();
        if (Objects.nonNull(region)) {
            masterDataCache.getAllUnits().forEach(unitBasicDetail -> {
                if (Objects.nonNull(unitBasicDetail.getRegion()) && region.equalsIgnoreCase(unitBasicDetail.getRegion())) {
                    regionUnitIds.add(unitBasicDetail.getId());
                }
            });
        }
        return kettleReadDao.getMonthlyOrdersOfRecipe(recipeId, regionUnitIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean saveRiderDelayReason(DelayReason delayReason) throws DataNotFoundException, TemplateRenderingException {
       return dao.addRidersDelayReason(delayReason,ordersCache);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<Order> getOrdersWithoutCommission(String startDate, String endDate){
        try {

            if( startDate == null || endDate == null || startDate.isEmpty() || endDate.isEmpty()) {
                 startDate = new SimpleDateFormat(DATE_FORMAT).format(AppUtils.getPreviousDate(AppUtils.getCurrentTimestamp()));
                 endDate = new SimpleDateFormat(DATE_FORMAT).format(AppUtils.getCurrentTimestamp());
            }
            LOG.info("############### Getting missed order between {} and {} date #########################",startDate,endDate);
           List<OrderDetail> orderDetails =  dao.getOrdersCommissionNotCalculated(startDate, endDate);
         return  orderDetails.stream().map(o-> DataConverter.convert(masterDataCache,o,null,recipeCache,props)).collect(Collectors.toList());
        }catch (Exception e){
            LOG.error("Error  while getting orders without commission, error: {}",e.getMessage());
            throw e;
        }
    }

    @Override
    public boolean saveWalletSuggestion(WalletEventData walletEventData) {
        WalletSuggestionEvents walletSuggestionEvents = walletSuggestionEventsDao.findByCustomerIdAndStatus(walletEventData.getCustomerId(), AppConstants.ACTIVE);
        if (Objects.nonNull(walletSuggestionEvents)) {
            LOG.info("Last Event is already active");
            return true;
        }
        try {
            CustomerInfo customerInfo = (customerDao.getCustomerInfoById(walletEventData.getCustomerId()));
            if (Objects.nonNull(customerInfo)) {
                String eventToken = AppUtils.generateRandomCode();
                WalletSuggestionEvents walletSuggestion = new WalletSuggestionEvents();
                walletSuggestion.setCustomerName(customerInfo.getFirstName());
                walletSuggestion.setContactNumber(customerInfo.getContactNumber());
                walletSuggestion.setCustomerId(walletEventData.getCustomerId());
                walletSuggestion.setOrderId(walletEventData.getOrderId());
                walletSuggestion.setBillingTime(walletEventData.getBillingServerTime());
                walletSuggestion.setTotalAmountInWallet(walletEventData.getSuggestedWallet().getTotalAmountInWallet());
                walletSuggestion.setPaidExtra(walletEventData.getSuggestedWallet().getPaidExtra());
                walletSuggestion.setExtraAmountGained(walletEventData.getSuggestedWallet().getExtraAmountGained());
                walletSuggestion.setEventToken(eventToken);
                walletSuggestion.setStatus(AppConstants.ACTIVE);
                walletSuggestion.setGenerationTime(AppUtils.getCurrentTimestamp());
                walletSuggestion.setPartnerId(walletEventData.getPartnerId());
                walletSuggestion.setBrandId(walletEventData.getBrandId());
                walletSuggestionEventsDao.save(walletSuggestion);
                return true;
            } else {
                LOG.error("CustomerId can not be find in customer info");
                return false;
            }
        } catch (Exception e) {
            LOG.error("Unable to save wallet suggestion event data");
        }
        return false;
    }

    @Override
    public List<LoyaltyEvents> getLoyaltyEvents(String id,Integer limit,String searchType) throws DataNotFoundException {
        if(Objects.nonNull(id)){
            if (AppConstants.THROUGH_ORDER_ID.equals(searchType)) {
                OrderDetail orderDetail = customerService.getOrderDetail(Integer.valueOf(id));
                if (Objects.nonNull(orderDetail)) {
                    return loyaltyService.getLoyaltyEvents(orderDetail.getCustomerId().toString(), AppConstants.THROUGH_ORDER_ID, limit);
                }
            } else if (AppConstants.THROUGH_CUSTOMER_ID.equals(searchType)) {
                return loyaltyService.getLoyaltyEvents(id.toString(), AppConstants.THROUGH_CUSTOMER_ID, limit);
            } else if (AppConstants.THROUGH_GENERATED_ORDER_ID.equals(searchType)) {
                Order orderDetail = orderSearchService.getOrderDetail(id.toString());
                if (Objects.nonNull(orderDetail)) {
                    return loyaltyService.getLoyaltyEvents(orderDetail.getCustomerId().toString(), AppConstants.THROUGH_GENERATED_ORDER_ID, limit);
                }
            }
        }
        return null;
    }

    @Override
    public List<OrderPaymentDetailData> getOrderPaymentDetail(Integer customerId, Integer orderId, String paymentSource, String contactNumber, Integer startPosition) {
        if(Objects.nonNull(customerId) || Objects.nonNull(orderId) || Objects.nonNull(contactNumber) || Objects.nonNull(paymentSource)){
            return dao.getOrderPaymentDetailByBatch(customerId,orderId,paymentSource,contactNumber,startPosition);
        }
        LOG.error("No required data is given to find Order Payment Detail");
        return null;
    }

    @Override
    public List<OrderPaymentDetailData> getOrderPaymentDetailBySourceAndStatus(String paymentSource, String paymentStatus, Date startDate) {
        if(Objects.nonNull(paymentSource) && Objects.nonNull(paymentStatus)){
            return dao.getOrderPaymentDetailBySourceAndStatusByBatch(paymentSource, paymentStatus, startDate);
        }
        LOG.error("No required data is given to find Order Payment Detail Data for paymentSource {} and paymentStatus {} and startDate {}", paymentSource, paymentStatus, startDate);
        return null;
    }

    @Override
    public void getAdditionalOfferData(GamifiedOfferResponse res) {
        CouponDetailData couponDetailData = offerService.getCouponDetailData(res.getOfferCode());
        res.setMinBillValue(couponDetailData.getOfferDetail().getMinValue());
        res.setOfferValue(couponDetailData.getOfferDetail().getValue());
        res.setOfferValueType(couponDetailData.getOfferDetail().getOfferType());
        res.setProductList(getProductsInOfferMapping(couponDetailData.getOfferDetail()));
    }

    @Override
    public void updateSugarVariant(Order order){
        try {
            if(Boolean.TRUE.equals(props.isSugarVariantToUpdate())){
                Map<String,Boolean> sweetnerProductMap = Arrays.stream(props.getSweetnerProductIds().split(",")).
                        map(Integer::valueOf).map(productId ->masterDataCache.getProduct(productId).getName())
                        .collect(Collectors.toMap(Function.identity(),productName-> true));
                for (OrderItem orderItem : order.getOrders()) {
                    Boolean sweetnerProductFound = false;
                    if(Objects.nonNull(orderItem.getComposition())){
                        if(!CollectionUtils.isEmpty(orderItem.getComposition().getOptions())){
                            sweetnerProductFound =  orderItem.getComposition().getOptions().stream().anyMatch(sweetnerProductMap::containsKey);
                            if(Boolean.TRUE.equals(sweetnerProductFound)){
                                addNoSugarVariant(orderItem);
                            }
                        }
                        if(!CollectionUtils.isEmpty(orderItem.getComposition().getMenuProducts())){
                            for(OrderItem menuItem : orderItem.getComposition().getMenuProducts()) {
                                Boolean sweetnerProductFoundInComboItem = false;
                                if (Objects.nonNull(menuItem.getComposition()) && !CollectionUtils.isEmpty(menuItem.getComposition().getOptions())) {
                                    sweetnerProductFoundInComboItem = menuItem.getComposition().getOptions().stream().anyMatch(sweetnerProductMap::containsKey);
                                    if (Boolean.TRUE.equals(sweetnerProductFoundInComboItem)) {
                                        addNoSugarVariant(menuItem);
                                    }
                                }
                            }
                        }


                    }

                }
            }
        }catch (Exception e){
            LOG.error("Error While Updating Sugar Variant When Sweetner Is Present :: {} ");
        }
    }

    private void addNoSugarVariant(OrderItem orderItem){
        Integer recipeId = orderItem.getRecipeId();
        AtomicReference<IngredientVariantDetail> noSugarVariant = new AtomicReference<>();
        RecipeDetail recipeDetail = recipeCache.getRecipe(recipeId);
        if(Objects.nonNull(recipeDetail) && Objects.nonNull(recipeDetail.getIngredient())
        && !CollectionUtils.isEmpty(recipeDetail.getIngredient().getVariants())){
            recipeDetail.getIngredient().getVariants().forEach(
                    variantGroup ->{
                        if(variantGroup.getProduct().getProductId() == AppConstants.SCM_SUGAR_PRODUCT_ID){
                              noSugarVariant.set(variantGroup.getDetails().stream().filter(variantDetail -> variantDetail.getAlias().
                                      equalsIgnoreCase(AppConstants.SCM_NO_SUGAR_VARIANT_PRODUCT_ALIAS)).findAny().get());
                        }
                    }
            );
        }else{
            LOG.info("Couldn't Update Sugar Variant As there is Issue In Recipe For Recipe Id : {}" , recipeId);
        }


        if(Objects.nonNull(noSugarVariant.get())){
             orderItem.getComposition().getVariants().forEach(variant ->{
                 if(variant.getProductId() == AppConstants.SCM_SUGAR_PRODUCT_ID){
                     variant.setAlias(noSugarVariant.get().getAlias());
                     variant.setDefaultSetting(noSugarVariant.get().isDefaultSetting());
                     variant.setQuantity(noSugarVariant.get().getQuantity());
                     variant.setUom(noSugarVariant.get().getUom());
                     variant.setCaptured(noSugarVariant.get().isCaptured());
                 }
             });
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public CouponDetailData getCustomerCouponMapping(Order order) {
        try {
            Customer customer = customerService.getCustomer(order.getCustomerId());
            CouponDetailData couponDetailData = offerService.getCouponCustomerMapping(order.getOfferCode(),
                    AppUtils.getValidContactNUmber(customer.getContactNumber()));
            if (Objects.nonNull(couponDetailData)) {
                offerService.updateCustomerCouponMapping(couponDetailData);
                return couponDetailData;
            }
        } catch (Exception e) {
            LOG.info("Error while reverting coupon usage for order id :: {}, {} ", order.getOrderId(), e.getMessage());
        }
        return null;
    }

    @Override
    @Async(value = "taskExecutor")
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void pushDataToCleverTapForPartner(OrderInfo info,boolean isOrderCancelled){
        try {
            boolean clvFlag = props.getCleverTapEnabled();
            if(clvFlag){
                orderNotificationService.createOrderNotificationData(info.getOrderNotification(),info);
                pushPartnerDataToCleverTap(info,isOrderCancelled);
            }
        }catch (Exception e){
            LOG.error("Error in pushing data to clevertap for partner for orderId : {} with error : {}",info.getOrder().getOrderId(),e.getMessage());
        }
        deleteReciepts(info.getUnit().getId(),info.getOrder().getOrderId());
    }

    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    private void pushPartnerDataToCleverTap(OrderInfo info,boolean isOrderCancelled) {
        LOG.info("Checking and updating data for the customer with partner customer id :{}", info.getPartnerCustomerId());
        Integer customerId = customerDao.getKettleCustomerId(info.getPartnerCustomerId());
        if (Objects.nonNull(customerId)) {
            try {
                cleverTapDataPushService.pushUserToCleverTap(customerId);
            } catch (Exception e) {
                LOG.error("Error in pushing user data for partner to cleverTap for order id : {} and partner customer id : {}"
                        , info.getOrder().getOrderId(), info.getPartnerCustomerId());
                cleverTapDataPushService.persistProfileTrack(
                        new CleverTapProfilePushTrack(customerId,
                                AppConstants.ERROR, CleverTapConstants.REGULAR));
            }
            if (!isOrderCancelled) {
                LOG.info("Publishing new order event detail for partner to clevertap for orderid: {}",
                        info.getOrder().getOrderId());
                try {
                    Map<Integer, OrderNotification> orderNotificationMap = new HashMap<>();
                    Map<Integer, Integer> orderListMap = new HashMap<>();
                    orderNotificationMap.putIfAbsent(info.getOrder().getOrderId(), info.getOrderNotification());
                    orderListMap.put(info.getOrder().getOrderId(), customerId);
                    CleverTapPushResponse response = cleverTapDataPushService.uploadEventForPartner(
                            orderListMap, getOrderType(info), CleverTapConstants.REGULAR, orderNotificationMap);
                    cleverTapDataPushService.persistEventTracks(response.getEvents());
                } catch (Exception e) {
                    LOG.error("error while pushing clevertap event data {}", e);
                    cleverTapDataPushService.persistEventTrack(
                            new EventPushTrack(CleverTapEvents.CHARGED, info.getOrder().getOrderId(), AppConstants.ERROR,
                                    CleverTapConstants.REGULAR, AppConstants.CLEVERTAP));
                }
            }
        }else{
            LOG.info("unable to push data to cleverTap for customer with partner customer id : {}",info.getPartnerCustomerId());
        }
    }

    @Override
    public boolean resendEmail(EmailData emailData) {
        try {
            HttpResponse response =  WebServiceHelper.postRequestWithAuth(environmentProperties.getVortexBaseUrl() +
                            Endpoints.SEND_EMAIL, environmentProperties.getVortexMasterToken(),
                    EmailInfoVO.builder()
                            .orderId(emailData.getOrderId())
                            .orderType(emailData.getOrderType())
                            .customerId(emailData.getCustomerId())
                            .email(emailData.getEmail())
                            .resend(true));

            if (HttpStatus.SC_OK == response.getStatusLine().getStatusCode()){
                return true;
            }


        }  catch (Exception e) {
            LOG.error("Unable to send email for orderId {} as {}",emailData.getOrderId(),e);
            return false;
        }


        return false;
    }


    private void pushDataToCleverTapNew(OrderInfo info, boolean isOrderCancelled,
                                        OrderFeedbackMetadata orderFeedbackMetadata){
        LOG.info("Checking and updating data for the customer:{}", info.getOrder().getCustomerId());
        List<ProfileUploadRequest> profileUploadRequestList = null;
        List<EventUploadRequest> eventUploadRequestList=null;
        try{
            profileUploadRequestList = cleverTapDataPushService.getUserProfileForCleverTap(
                    Arrays.asList(info.getOrder().getCustomerId()), CleverTapConstants.REGULAR);
        } catch (Exception e) {
            LOG.error("error while pushing customer data to clevertap {}", e.getMessage());
            cleverTapDataPushService.persistProfileTrack(new CleverTapProfilePushTrack(info.getOrder().getCustomerId(),
                    AppConstants.ERROR, CleverTapConstants.REGULAR));
        }
        if(!isOrderCancelled){
            LOG.info("Publishing order event detail to clevertap for orderid: {}", info.getOrder().getOrderId());
            try {
                Map<Integer,OrderNotification> orderNotificationMap = new HashMap<>();
                orderNotificationMap.putIfAbsent(info.getOrder().getOrderId(), info.getOrderNotification());
                eventUploadRequestList = cleverTapDataPushService.getEventDataList(Arrays.asList(info.getOrder().getOrderId()),
                        CleverTapConstants.REGULAR, orderNotificationMap);
            }catch (Exception e){
                LOG.error("error while pushing clevertap event data {}", e);
                cleverTapDataPushService
                        .persistEventTrack(new EventPushTrack(CleverTapEvents.CHARGED, info.getOrder().getOrderId(),
                                AppConstants.ERROR, CleverTapConstants.REGULAR, AppConstants.CLEVERTAP));
            }
        }
        createEventAndProfileListAndPushDataToQueue(info,profileUploadRequestList,eventUploadRequestList,orderFeedbackMetadata);
    }

    private void createEventAndProfileListAndPushDataToQueue(OrderInfo info,List<ProfileUploadRequest> profileUploadRequestList
            ,List<EventUploadRequest> eventUploadRequestList,OrderFeedbackMetadata orderFeedbackMetadata){
        List<Object> resultList = new ArrayList<>();
        List<CleverTapProfilePushTrack> profilePushTrackList = new ArrayList<>();
        if(!org.springframework.util.CollectionUtils.isEmpty(profileUploadRequestList)){
            for(ProfileUploadRequest p : profileUploadRequestList){
                resultList.add(p);
                CleverTapProfilePushTrack profilePushTrack = new CleverTapProfilePushTrack(info.getOrder().getCustomerId()
                        ,CleverTapConstants.PUSHED_TO_QUEUE, CleverTapConstants.REGULAR);
                profilePushTrack.setPublishTime(AppUtils.getCurrentTimestamp());
                profilePushTrackList.add(profilePushTrack);
            }
        }
        List<EventPushTrack> eventPushTrackList = new ArrayList<>();
        if(!org.springframework.util.CollectionUtils.isEmpty(eventUploadRequestList)){
            EventPushTrack eventPushTrack;
            for(EventUploadRequest e : eventUploadRequestList){
                resultList.add(e);
                if (e.getEvtData() instanceof ClevertapChargedEventData) {
                    ClevertapChargedEventData eventData = (ClevertapChargedEventData) e.getEvtData();
                    eventPushTrack = new EventPushTrack(e.getEvtName(),eventData.getOrderId(),
                            CleverTapConstants.PUSHED_TO_QUEUE, CleverTapConstants.REGULAR,AppConstants.CLEVERTAP);
                    eventPushTrack.setPublishTime(AppUtils.getCurrentTimestamp());
                    eventPushTrackList.add(eventPushTrack);
                }
            }
        }
        pushDataToQueueAndUpdatefeedbackEventStatus(resultList,info.getOrder().getCustomerId(),info.getOrder().getOrderId()
                ,orderFeedbackMetadata,profilePushTrackList,eventPushTrackList);
    }

    private void pushDataToQueueAndUpdatefeedbackEventStatus(List<Object> payload,Integer identity,Integer orderId
            ,OrderFeedbackMetadata orderFeedbackMetadata,List<CleverTapProfilePushTrack> profilePushTrackList
            ,List<EventPushTrack> eventPushTrackList){

        CleverTapPushResponse cleverTapPushResponse = cleverTapDataPushService.publishToCleverTapQueueNew(payload,
                identity,CleverTapConstants.REGULAR);
        if(Objects.nonNull(cleverTapPushResponse)){
            if(CleverTapConstants.FAILED_TO_PUSHED.equals(cleverTapPushResponse.getStatus())){
                for(CleverTapProfilePushTrack p : profilePushTrackList){
                    p.setStatus(CleverTapConstants.FAILED_TO_PUSHED);
                }
                for(EventPushTrack e : eventPushTrackList){
                    e.setStatus(CleverTapConstants.FAILED_TO_PUSHED);
                }
            }
            try {
                cleverTapDataPushService.persistEventTracks(eventPushTrackList);
                cleverTapDataPushService.persistProfileTrack(profilePushTrackList);
            }catch (Exception e){
                LOG.info("Error in persisting event and profile data : {}",e.getMessage());
            }
            try {
                if ((cleverTapPushResponse.getStatus().equalsIgnoreCase("Success")
                        || cleverTapPushResponse.getStatus().equalsIgnoreCase("PUSHED_TO_QUEUE"))) {
                    orderNotificationService.updateFeedbackEventStatus(orderFeedbackMetadata, true);
                } else {
                    orderNotificationService.updateFeedbackEventStatus(orderFeedbackMetadata, false);
                }
            } catch (Exception e) {
                LOG.error("Error while updating Feedbcak event status for order with id :{}",
                        orderId, e);
            }
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean cancelOrderItem(OrderItemCancellationRequest orderItemCancellationRequest ,Integer cancelledBy, Order order) throws DataNotFoundException, TemplateRenderingException {
        OrderInfo info = orderSearchService.getOrderReceipt(orderItemCancellationRequest.getGeneratedOrderId());
        Unit unit = masterDataCache.getUnit(info.getOrder().getUnitId());
        com.stpl.tech.kettle.data.model.OrderItem orderItem = dao.find(com.stpl.tech.kettle.data.model.OrderItem.class,
                orderItemCancellationRequest.getOrderItemId());
        OrderDetail currentOrderDetail = dao.find(OrderDetail.class,orderItem.getOrderDetail().getOrderId());
        if(Objects.isNull(orderItem)) {
            return false;
        }
        if(Objects.nonNull(currentOrderDetail)){
            BigDecimal cancelledOrderItemSettledAmount = AppUtils.add(orderItem.getPaidAmount(),orderItem.getTaxAmount());
            BigDecimal updatedSettledAmount = AppUtils.subtract(currentOrderDetail.getSettledAmount(),cancelledOrderItemSettledAmount);
            if(BigDecimal.ZERO.compareTo(updatedSettledAmount)>0 || BigDecimal.ONE.compareTo(updatedSettledAmount) >0){
                updatedSettledAmount = BigDecimal.ZERO;
            }
            currentOrderDetail.setSettledAmount(updatedSettledAmount);
            dao.update(currentOrderDetail);
        }

        order = cancelOrderItemPaidAddOns(orderItemCancellationRequest, order,currentOrderDetail, cancelledBy);

        OrderDetail orderDetail = new OrderDetail();
        orderDetail.setOrderId(AppConstants.DUMMY_ORDER_CANCELLATION_ID);
        orderItem.setOrderDetail(orderDetail);
        dao.update(orderItem);
        createOrderItemStatusEvent(orderItemCancellationRequest,OrderStatus.CREATED.value(),OrderStatus.CANCELLED.value(),
                TransitionStatus.SUCCESS.name(), cancelledBy);
        if(AppUtils.isCombo(masterDataCache.getProduct(orderItem.getProductId()).getTaxCode())){
            List<com.stpl.tech.kettle.data.model.OrderItem> comboOrderItems = orderSearchService.getComboOrderItems(orderItem.getOrderItemId());
            for(com.stpl.tech.kettle.data.model.OrderItem comboItem : comboOrderItems){
                if(Objects.nonNull(currentOrderDetail)){
                    BigDecimal comboItemTax = comboItem.getTaxAmount();
                    BigDecimal updatedSettledAmount = AppUtils.subtract(currentOrderDetail.getSettledAmount(),comboItemTax);
                    if(BigDecimal.ZERO.compareTo(updatedSettledAmount)>0 || BigDecimal.ONE.compareTo(updatedSettledAmount) >0 ){
                        updatedSettledAmount = BigDecimal.ZERO;
                    }
                    currentOrderDetail.setSettledAmount(updatedSettledAmount);
                    dao.update(currentOrderDetail);
                }
                OrderDetail orderDetail1 = new OrderDetail();
                orderDetail1.setOrderId(AppConstants.DUMMY_ORDER_CANCELLATION_ID);
                comboItem.setOrderDetail(orderDetail1);
                dao.update(comboItem);
                orderItemCancellationRequest.setOrderItemId(comboItem.getOrderItemId());
                createOrderItemStatusEvent(orderItemCancellationRequest,OrderStatus.CREATED.value(),OrderStatus.CANCELLED.value(),
                        TransitionStatus.SUCCESS.name(), cancelledBy);
            }

        }

        OrderInfo infoTemp = ordersCache.getOrderById(orderItemCancellationRequest.getOrderId(),unit.getFamily(),true);
        if(Objects.nonNull(infoTemp)){
            for(OrderItem oi : infoTemp.getOrder().getOrders()){
                if(oi.getItemId() == orderItemCancellationRequest.getOrderItemId().intValue()) {
                    oi.setIsHoldOn(AppConstants.NO);
                    if (Objects.nonNull(oi.getComposition()) && !CollectionUtils.isEmpty(oi.getComposition().getMenuProducts())){
                        for(OrderItem mi : oi.getComposition().getMenuProducts()){
                            mi.setIsHoldOn(AppConstants.NO);
                        }
                    }
                }
            }
        }
        if(ordersCache.updateOrderInCache(infoTemp)){
            LOG.info("Cache updated successfully");
        }else{
            LOG.info("Error in updating Cache ");
        }
        return  true;
    }

    public Order cancelOrderItemPaidAddOns(OrderItemCancellationRequest orderItemCancellationRequest, Order order , OrderDetail currentOrderDetail, Integer cancelledBy) {

        // orders - List of order from order object
        // options - List of options from each order item
        // ord - each child of orders
        // opt - each child of options

        // Map 1: <ProductName, OrderID>
        // Map 2: <OrderID, Quantity>

        Map <String, Integer> idProductNameMap = new HashMap<>();
        Map <Integer, Integer> paidAddOnsQtyMap = new HashMap<>();
        List <OrderItem> orders = order.getOrders();

        // Constructing Map 1
        orders.forEach(ord -> {
            idProductNameMap.put(ord.getProductName(), ord.getItemId());
        });

        // Constructing Map 2
        orders.forEach((ord) -> {
            if (orderItemCancellationRequest.getOrderItemId().equals(ord.getItemId())) {
                if (ord.getComposition() != null) {
                    if (!CollectionUtils.isEmpty(ord.getComposition().getMenuProducts())) {
                        List<OrderItem> menuProducts = ord.getComposition().getMenuProducts();
                        menuProducts.forEach((menuProduct) -> {
                            if (menuProduct.getComposition() != null) {
                                List <String> options = menuProduct.getComposition().getOptions();
                                if (!CollectionUtils.isEmpty(options)) {
                                    createPaidAddOnsQtyMap(idProductNameMap, paidAddOnsQtyMap, options, ord);
                                }
                            }

                        });
                    } else {
                        List <String> options = ord.getComposition().getOptions();
                        if (!CollectionUtils.isEmpty(options)) {
                            createPaidAddOnsQtyMap(idProductNameMap, paidAddOnsQtyMap, options, ord);
                        }
                    }
                }
            }
        });

        Set <Integer> orderItemIds = paidAddOnsQtyMap.keySet();

        // Fetching details of order items corresponding to orderItemIds from the DB
        List<com.stpl.tech.kettle.data.model.OrderItem> processedOrderItems = dao.getOrderItems(orderItemIds);

        // Updating processOrderItems for cancelling of order items
        processedOrderItems.forEach((item) -> {
            item.setQuantity(item.getQuantity() - paidAddOnsQtyMap.get(idProductNameMap.get(item.getProductName())));
            if (item.getQuantity() <= 0) {
                OrderDetail orderDetail = new OrderDetail();
                orderDetail.setOrderId(AppConstants.DUMMY_ORDER_CANCELLATION_ID);

                // setting order ids of paid addons to cancellation id
                List<com.stpl.tech.kettle.data.model.OrderItem> orderItems = orderDetail.getOrderItems();
                orderItems.forEach((orderItem) -> {
                    orderItem.setOrderItemId(AppConstants.DUMMY_ORDER_CANCELLATION_ID);
                });
                orderDetail.setOrderItems(orderItems);
                item.setOrderDetail(orderDetail);
                OrderItemCancellationRequest orderItemCancellationRequestForAddon = orderItemCancellationRequest.toBuilder()
                        .orderItemId(item.getOrderItemId()).build();
                createOrderItemStatusEvent(orderItemCancellationRequestForAddon, OrderStatus.CREATED.value(),OrderStatus.CANCELLED.value(),
                        TransitionStatus.SUCCESS.name(), cancelledBy);
                BigDecimal cancelledOrderItemSettledAmount = AppUtils.add(item.getPaidAmount(),item.getTaxAmount());
                BigDecimal updatedSettledAmount = AppUtils.subtract(currentOrderDetail.getSettledAmount(),cancelledOrderItemSettledAmount);
                if(BigDecimal.ZERO.compareTo(updatedSettledAmount)>0 || BigDecimal.ONE.compareTo(updatedSettledAmount) >0){
                    updatedSettledAmount = BigDecimal.ZERO;
                }
                currentOrderDetail.setSettledAmount(updatedSettledAmount);
            }
        });

        return order;
    }

    public void createPaidAddOnsQtyMap(Map <String, Integer> idProductNameMap, Map <Integer, Integer> paidAddOnsQtyMap,
                                       List <String> options, OrderItem ord) {
        options.forEach((opt) -> {
            if (paidAddOnsQtyMap.containsKey(idProductNameMap.get(opt))) {
                paidAddOnsQtyMap.put(idProductNameMap.get(opt), paidAddOnsQtyMap.get(idProductNameMap.get(opt)) + ord.getQuantity());
            }
            else {
                paidAddOnsQtyMap.put(idProductNameMap.get(opt), ord.getQuantity());
            }
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public  Boolean createOrderItemStatusEvent(OrderItemCancellationRequest orderItemCancellationRequest ,
                                               String fromStatus , String toStatus,
                                               String transitionStatus , Integer updatedBy){
        try {
            OrderItemStatus initiatedOrderItemStatus = dao.getOrderItemStatusByOrderItemId(orderItemCancellationRequest.getOrderItemId());
            if(Objects.nonNull(initiatedOrderItemStatus)){
                initiatedOrderItemStatus.setStatus(toStatus);
                initiatedOrderItemStatus.setUpdatedBy(updatedBy);
                initiatedOrderItemStatus.setUpdationTime(AppUtils.getCurrentTimestamp());
                dao.update(initiatedOrderItemStatus);
            }else{
                OrderItemStatus  orderItemStatus = OrderItemStatus.builder().orderItemId(orderItemCancellationRequest.getOrderItemId())
                        .orderId(orderItemCancellationRequest.getOrderId()).status(toStatus)
                        .updatedBy(updatedBy).updationTime(AppUtils.getCurrentTimestamp())
                        .tableRequestId(orderItemCancellationRequest.getTableRequestId()).build();
                dao.add(orderItemStatus);
            }
            return  true;
        }catch (Exception e){
            LOG.error("Error while Creating Order Item Status  event for order item id ::::: {} , {}" , orderItemCancellationRequest.getOrderItemId(),
                    e);
        }
        return  false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<OrderStatusEvent> getLastOrderTransactionEvent(int orderId) {
        return dao.getLastOrderTransactionEvent(orderId);
    }

    @Override
    public Map<String, Map<String, String>> fetchOrderItemNewStatus(UnitOrder unitOrder) throws DataNotFoundException {
        Map<String,Map<String,String>> orderItemStatus = new HashMap<>();
       try {
           List<OrderInfo> orders = ordersCache.getOrders(unitOrder.getUnitId());
           if (orders != null && !orders.isEmpty()) {
               orders.forEach(o -> {
                   orderItemStatus.put(String.valueOf(o.getOrder().getOrderId()), new HashMap<>());
                   o.getOrder().getOrders().forEach(oI -> {
                       orderItemStatus.get(String.valueOf(o.getOrder().getOrderId())).put(String.valueOf(oI.getItemId()), oI.getIsHoldOn());
                       if (Objects.nonNull(oI.getComposition()) && !CollectionUtils.isEmpty(oI.getComposition().getMenuProducts())) {
                           oI.getComposition().getMenuProducts().forEach(cI -> {
                               orderItemStatus.get(String.valueOf(o.getOrder().getOrderId())).put(String.valueOf(cI.getItemId()), cI.getIsHoldOn());
                           });
                       }
                   });
               });
           }
       }
       catch (Exception e){
           LOG.error("Error while fetching Order Item Statuses ",e);
       }
        return orderItemStatus;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public boolean isOrderContainHoldItem(OrderInfo info){
        try {
            if (Objects.nonNull(info.getOrder()) && !org.apache.commons.collections4.CollectionUtils.isEmpty(info.getOrder().getOrders())) {
                for (OrderItem oi : info.getOrder().getOrders()) {
                    OrderItemStatus itemStatus = dao.getOrderItemStatusByOrderItemId(oi.getItemId());
                    if (Objects.nonNull(itemStatus)) {
                        if (OrderStatus.ON_HOLD.value().equalsIgnoreCase(itemStatus.getStatus())) {
                            return true;
                        }
                        if (Objects.nonNull(oi.getComposition()) && !org.apache.commons.collections4.CollectionUtils.isEmpty(oi.getComposition().getMenuProducts())) {
                            for (OrderItem mi : oi.getComposition().getMenuProducts()) {
                                OrderItemStatus menuItemStatus = dao.getOrderItemStatusByOrderItemId(mi.getItemId());
                                if (OrderStatus.ON_HOLD.value().equalsIgnoreCase(menuItemStatus.getStatus())) {
                                    return true;
                                }
                            }
                        }
                    }
                }
            }
        }catch (Exception e){
            LOG.info("error in checking hold item : {}",e);
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void cancelPendingOrdersOfTestingUnits(Integer unitId) throws DataNotFoundException {
        try {
            List<String> status = new ArrayList<>(Arrays.asList(OrderStatus.SETTLED.value()));
            List<String> generatedOrderIds = orderSearchService.getOrdersForUnitFromStatus(unitId, status);
            if (CollectionUtils.isNotEmpty(generatedOrderIds)) {
                for (String generatedOrderId : generatedOrderIds) {

                    deleteOrder(unitId, generatedOrderId, AppConstants.SYSTEM_EMPLOYEE_ID,
                            AppConstants.SYSTEM_EMPLOYEE_ID, "Auto cancelled by system", null, AppConstants.NO_WASTAGE);

                }
            }
        } catch (Exception e) {
            LOG.error("Error while cancelling pending orders of testing units: {}", e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<OrderRefund> getServiceChargeRefundOrder(Integer unit, Date startDate) throws DataNotFoundException {
        try {
            List<OrderRefund> orderRefunds = new ArrayList<>();
            List<OrderRefundDetail> orderRefundDetail = dao.getOrderRefundByUnitId(unit, startDate);
            if(Objects.nonNull(orderRefundDetail) && !orderRefundDetail.isEmpty()){
                for(OrderRefundDetail ord : orderRefundDetail){
                    OrderRefund orderRefund =  DataConverter.convert(ord);
                    orderRefunds.add(orderRefund);
                }
            }
            return orderRefunds;
        } catch (Exception e) {
            LOG.error("Error while fetching service charge refund order: {}", e.getMessage());
        }
        return null;
    }

}
