package com.stpl.tech.kettle.data.model;


import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static javax.persistence.GenerationType.IDENTITY;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name="CustomerFavChaiMapping", propOrder = {"customerId","productId","productName","status","dimension","creationTime","lastUpdatedTime","consumeType","tagType"})
@Entity
@Table(name="CUSTOMER_FAV_CHAI_MAPPING")
public class CustomerFavChaiMapping {


    private Integer customizationId;
    private int customerId;
    private int productId ;
    private String productName;
    private String status ;
    private String dimension;

    private Date creationTime ;
    private Date lastUpdatedTime ;
    private Date createdAt;
    private String consumeType;
    private String tagType;
    private String isUpdated;
    private List<FavChaiCustomizationDetail> favChaiCustomizationDetailList= new ArrayList<>();
    private Integer sourceId ;
    private String sourceName;
    private Integer recipeId;
    private String recipeProfile;
    private int quantity;
    private String shortCode;
    private BigDecimal price;

    private Integer totalOrderCount;

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "CUSTOMIZATION_ID", unique = true, nullable = false)
    public Integer getCustomizationId() {
        return customizationId;
    }

    public void setCustomizationId(Integer customizationId) {
        this.customizationId = customizationId;
    }

    @Column(name="CUSTOMER_ID", nullable = false)
    public int getCustomerId() {
        return customerId;
    }

    public void setCustomerId(int customerId) {
        this.customerId = customerId;
    }

    @Column(name="PRODUCT_ID", nullable = false)
    public int getProductId() {
        return productId;
    }

    public void setProductId(int productId) {
        this.productId = productId;
    }

    @Column(name="PRODUCT_NAME",nullable = false)
    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    @Column(name="STATUS",nullable = false)
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Column(name="DIMENSION", length = 10)
    public String getDimension() {
        return dimension;
    }

    public void setDimension(String dimension) {
        this.dimension = dimension;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATION_TIME", nullable = false, length = 19)
    public Date getCreationTime() {
        return creationTime;
    }

    public void setCreationTime(Date creationTime) {
        this.creationTime = creationTime;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "LAST_UPDATED_TIME", nullable = true, length = 19)
    public Date getLastUpdatedTime() {
        return lastUpdatedTime;
    }

    public void setLastUpdatedTime(Date lastUpdatedTime) {
        this.lastUpdatedTime = lastUpdatedTime;
    }

    @Temporal(TemporalType.DATE)
    @Column(name="CREATED_AT",nullable=false)
    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    @Column(name="CONSUME_TYPE")
    public String getConsumeType() {
        return consumeType;
    }

    public void setConsumeType(String consumeType) {
        this.consumeType = consumeType;
    }

    @Column(name="TAG_TYPE")
    public String getTagType() {
        return tagType;
    }

    public void setTagType(String tagType) {
        this.tagType = tagType;
    }

    @Column(name="IS_UPDATED")
    public String getIsUpdated() {
        return isUpdated;
    }

    public void setIsUpdated(String isUpdated) {
        this.isUpdated = isUpdated;
    }

    @OneToMany(fetch = FetchType.LAZY,mappedBy = "customerFavChaiMapping" ,cascade = CascadeType.ALL)
    public List<FavChaiCustomizationDetail> getFavChaiCustomizationDetailList() {
        return favChaiCustomizationDetailList;
    }

    public void setFavChaiCustomizationDetailList(List<FavChaiCustomizationDetail> favChaiCustomizationDetailList) {
        this.favChaiCustomizationDetailList = favChaiCustomizationDetailList;
    }
    @Column(name ="SOURCE_ID")// pos- 1
    public Integer getSourceId() {
        return sourceId;
    }

    public void setSourceId(Integer sourceId) {
        this.sourceId = sourceId;
    }

    @Column(name ="SOURCE_NAME") //For now CAFE
    public String getSourceName() {
        return sourceName;
    }

    public void setSourceName(String sourceName) {
        this.sourceName = sourceName;
    }

    @Column(name = "RECIPE_ID", nullable = true)
    public Integer getRecipeId() {
        return recipeId;
    }

    public void setRecipeId(Integer recipeId) {
        this.recipeId = recipeId;
    }

    @Column(name = "RECIPE_PROFILE", nullable = true)
    public String getRecipeProfile() {
        return recipeProfile;
    }

    public void setRecipeProfile(String recipeProfile) {
        this.recipeProfile = recipeProfile;
    }

    @Column(name = "QUANTITY", nullable = false)
    public int getQuantity() {
        return this.quantity;
    }

    public void setQuantity(int quantity) {
        this.quantity = quantity;
    }

    @Column(name ="PRODUCT_SHORT_CODE")
    public String getShortCode() {
        return shortCode;
    }

    public void setShortCode(String shortCode) {
        this.shortCode = shortCode;
    }

    @Column(name ="PRICE")
    public  BigDecimal getPrice(){
        return price ;
    }

    public void setPrice(BigDecimal price){
        this.price= price;
    }

    @Column(name = "TOTAL_ORDER_COUNT")
    public Integer getTotalOrderCount() {
        return totalOrderCount;
    }

    public void setTotalOrderCount(Integer totalOrderCount) {
        this.totalOrderCount = totalOrderCount;
    }
}
