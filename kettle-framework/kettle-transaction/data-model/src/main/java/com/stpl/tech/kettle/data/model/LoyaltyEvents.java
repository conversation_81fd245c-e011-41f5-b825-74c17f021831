/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;
// Generated 27 Jul, 2015 12:04:59 PM by Hibernate Tools 4.0.0

import static javax.persistence.GenerationType.IDENTITY;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 * ChannelPartner generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "LOYALTY_EVENTS")
public class LoyaltyEvents implements java.io.Serializable {

	private Integer loyaltyEventsId;
	private Integer customerId;
	private String transactionType;
	private String transactionCodeType;
	private String transactionCode;
	private String transactionStatus;
	private Integer transactionPoints;
	private Date transactionTime;
	private Integer orderId;
	private Integer eventId;
	private Integer openingBalance;
	private Integer closingBalance;

	private String reason;
	private String loyaltyEventStatus;
	private Integer redeemedPoints=0;
	private Integer expiredPoints=0;
	private Date expirationTime;
	private Date redemptionTime;

	public LoyaltyEvents() {
	}

	public LoyaltyEvents(Integer customerId, String transactionType) {
		this.customerId = customerId;
		this.transactionType = transactionType;
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "LOYALTY_EVENTS_ID", unique = true, nullable = false)
	public Integer getLoyaltyEventsId() {
		return this.loyaltyEventsId;
	}

	public void setLoyaltyEventsId(Integer loyaltyEventsId) {
		this.loyaltyEventsId = loyaltyEventsId;
	}

	@Column(name = "CUSTOMER_ID", nullable = false, length = 50)
	public Integer getCustomerId() {
		return this.customerId;
	}

	public void setCustomerId(Integer customerId) {
		this.customerId = customerId;
	}

	@Column(name = "TRANSACTION_TYPE", nullable = false, length = 10)
	public String getTransactionType() {
		return this.transactionType;
	}

	public void setTransactionType(String transactionType) {
		this.transactionType = transactionType;
	}

	@Column(name = "TRANSACTION_CODE_TYPE", nullable = false, length = 100)
	public String getTransactionCodeType() {
		return this.transactionCodeType;
	}

	public void setTransactionCodeType(String transactionCodeType) {
		this.transactionCodeType = transactionCodeType;
	}

	@Column(name = "TRANSACTION_CODE", nullable = false, length = 500)
	public String getTransactionCode() {
		return this.transactionCode;
	}

	public void setTransactionCode(String transactionCode) {
		this.transactionCode = transactionCode;
	}

	@Column(name = "TRANSACTION_STATUS", nullable = false, length = 10)
	public String getTransactionStatus() {
		return this.transactionStatus;
	}

	public void setTransactionStatus(String transactionStatus) {
		this.transactionStatus = transactionStatus;
	}

	@Column(name = "TRANSACTION_POINTS", nullable = false)
	public Integer getTransactionPoints() {
		return this.transactionPoints;
	}

	public void setTransactionPoints(Integer transactionPoints) {
		this.transactionPoints = transactionPoints;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "TRANSACTION_TIME", nullable = false, length = 19)
	public Date getTransactionTime() {
		return this.transactionTime;
	}

	public void setTransactionTime(Date transactionTime) {
		this.transactionTime = transactionTime;
	}

	@Column(name = "ORDER_ID", nullable = true)
	public Integer getOrderId() {
		return orderId;
	}

	public void setOrderId(Integer orderId) {
		this.orderId = orderId;
	}

	@Column(name = "EVENT_ID", nullable = true)
	public Integer getEventId() {
		return eventId;
	}

	public void setEventId(Integer eventId) {
		this.eventId = eventId;
	}

	@Column(name = "OPENING_BALANCE", nullable = true)
	public Integer getOpeningBalance() {
		return openingBalance;
	}

	public void setOpeningBalance(Integer openingBalance) {
		this.openingBalance = openingBalance;
	}

	@Column(name = "CLOSING_BALANCE", nullable = true)
	public Integer getClosingBalance() {
		return closingBalance;
	}

	public void setClosingBalance(Integer closingBalance) {
		this.closingBalance = closingBalance;
	}

	@Column(name = "REASON",nullable = true)
	public String getReason() {
		return reason;
	}

	public void setReason(String reason) {
		this.reason = reason;
	}

	@Column(name = "LOYALTY_EVENT_STATUS")
	public String getLoyaltyEventStatus() {
		return loyaltyEventStatus;
	}

	public void setLoyaltyEventStatus(String loyaltyEventStatus) {
		this.loyaltyEventStatus = loyaltyEventStatus;
	}

	@Column(name = "REDEEMED_POINTS")
	public Integer getRedeemedPoints() {
		return redeemedPoints;
	}

	public void setRedeemedPoints(Integer redeemedPoints) {
		this.redeemedPoints = redeemedPoints;
	}

	@Column(name = "EXPIRED_POINTS")
	public Integer getExpiredPoints() {
		return expiredPoints;
	}

	public void setExpiredPoints(Integer expiredPoints) {
		this.expiredPoints = expiredPoints;
	}

	@Temporal(TemporalType.DATE)
	@Column(name = "EXPIRATION_TIME")
	public Date getExpirationTime() {
		return expirationTime;
	}

	public void setExpirationTime(Date expirationTime) {
		this.expirationTime = expirationTime;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "REDEMPTION_TIME" ,length = 19)
	public Date getRedemptionTime() {
		return redemptionTime;
	}

	public void setRedemptionTime(Date redemptionTime) {
		this.redemptionTime = redemptionTime;
	}

	@Override
	public String toString() {
		StringBuilder result = new StringBuilder();
		result.append("Loyalty Events Id: " + this.loyaltyEventsId);
		result.append(", Customer Id: " + this.customerId);
		result.append(", Transaction Type: " + transactionType);

		result.append(", Transaction Code Type: " + this.transactionCodeType);
		result.append(", Transaction Code: " + this.transactionCode);
		result.append(", Transaction Status: " + this.transactionStatus);
		result.append(", Transaction Points: " + this.transactionPoints);
		result.append(", Transaction Time: " + this.transactionTime);
		result.append(", Order Id: " + this.orderId);
		return result.toString();
	}

}
