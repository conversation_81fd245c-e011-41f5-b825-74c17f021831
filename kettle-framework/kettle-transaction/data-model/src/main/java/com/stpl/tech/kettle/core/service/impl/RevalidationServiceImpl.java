package com.stpl.tech.kettle.core.service.impl;

import com.stpl.tech.kettle.core.service.RevalidationService;
import com.stpl.tech.kettle.customer.service.CustomerService;
import com.stpl.tech.kettle.data.dao.CustomerOfferDetailDao;
import com.stpl.tech.kettle.data.model.CustomerOfferDetail;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.master.core.WebErrorCode;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.offer.dao.OfferManagementDao;
import com.stpl.tech.master.data.model.CouponDetailData;
import com.stpl.tech.master.domain.model.CustomerAppliedCouponDetail;
import com.stpl.tech.master.lock.service.SyncLock;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Service
@Log4j2
public class RevalidationServiceImpl implements RevalidationService {

    @Autowired
    private OfferManagementDao offerManagementDao;

    @Autowired
    private CustomerOfferDetailDao customerOfferDetailDao;

    @Autowired
    private CustomerService customerService;

    @Autowired
    private SyncLock lock;

    public String revalidateCouponMaxUsageAndForCustomer(Order order, MasterDataCache masterDataCache) {
        String result = AppConstants.REVALIDATION_SUCCESSFUL;
        if (Objects.nonNull(order.getCustomerId()) && Objects.nonNull(order.getOfferCode())) {
            String key = order.getOfferCode() + "_" + order.getCustomerId();
            result = lock.syncLock(key, () -> {
                if (masterDataCache.getCustomerAppliedCouponDetailMap().containsKey(key)) {
                    CustomerAppliedCouponDetail appliedCouponDetail = masterDataCache.getCustomerAppliedCouponDetailMap().get(key);
                    if (appliedCouponDetail.getUsageCount() + 1 <= appliedCouponDetail.getMaxUsage()) {
                        appliedCouponDetail.setUsageCount(appliedCouponDetail.getUsageCount() + 1);
                        masterDataCache.getCustomerAppliedCouponDetailMap().put(key, appliedCouponDetail);
                        return AppConstants.REVALIDATION_SUCCESSFUL;
                    } else {
                        return WebErrorCode.COUPON_REUSABILITY_FOR_CUSTOMER_FAILED.getReason();
                    }
                }
                return revalidateFromDatabase(order, key, masterDataCache);
            });
        }
        return result;
    }

    private String revalidateFromDatabase(Order order, String key, MasterDataCache masterDataCache) {
        CouponDetailData coupon = offerManagementDao.getCouponWithoutMappings(order.getOfferCode());
        if (Objects.nonNull(coupon)) {
            log.info("Revalidating coupon usage coupon from database for key :::::: {}",key);
            if (!AppUtils.getStatus(coupon.getCouponReuse()) && coupon.getUsageCount() > 0) {
                return WebErrorCode.COUPON_REUSABILITY_FAILED.getReason();
            }
            CustomerOfferDetail customerOfferDetail = customerOfferDetailDao.findByOfferCodeAndCustomerId(coupon.getCouponCode(), order.getCustomerId());
            if (Objects.nonNull(customerOfferDetail)) {
                if (!AppUtils.getStatus(coupon.getCustomerReuse()) && AppConstants.YES.equalsIgnoreCase(customerOfferDetail.getAvailed())) {
                    if (!customerService.getOfferDetail(order.getCustomerId(), order.getOfferCode()).isEmpty()) {
                        return WebErrorCode.COUPON_REUSABILITY_FOR_CUSTOMER_FAILED.getReason();
                    }
                }
            }
            addingToCacheAfterSuccessfulValidation(masterDataCache, key, coupon);
        }
        return AppConstants.REVALIDATION_SUCCESSFUL;
    }

    private void addingToCacheAfterSuccessfulValidation(MasterDataCache masterDataCache, String key, CouponDetailData coupon) {
        CustomerAppliedCouponDetail appliedCouponDetail = masterDataCache.getCustomerAppliedCouponDetailMap().get(key);
        if (Objects.nonNull(appliedCouponDetail)) {
            appliedCouponDetail.setUsageCount(appliedCouponDetail.getUsageCount() + 1);
        } else {
            appliedCouponDetail = new CustomerAppliedCouponDetail(
                    AppUtils.getStatus(coupon.getCouponReuse()),
                    AppUtils.getStatus(coupon.getCustomerReuse()),
                    coupon.getMaxUsage(),
                    coupon.getUsageCount() + 1
            );
        }
        masterDataCache.getCustomerAppliedCouponDetailMap().put(key, appliedCouponDetail);
    }

}
