package com.stpl.tech.kettle.data.model;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@Builder
@Entity
@Table(name = "ORDER_ITEM_STATUS")
public class OrderItemStatus {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ORDER_ITEM_STATUS_ID")
    private Integer orderItemStatusId;

    @Column(name = "TABLE_REQUEST_ID")
    private Integer tableRequestId;

    @Column(name = "ORDER_ID")
    private Integer orderId;

    @Column(name = "ORDER_ITEM_ID")
    private Integer orderItemId;


    @Column(name = "STATUS")
    private String status;

    @Column(name = "UPDATED_BY")
    private Integer updatedBy;

    @Column(name = "SERVED_BY")
    private Integer servedBy;



    @Column(name = "UPDATION_TIME")
    private Date updationTime;

    @Column(name = "ITEM_CREATION_TIME")
    private Date itemCreationTime;
    @Column(name = "ITEM_IN_PROCESS_TIME")
    private Date itemInProcessTime;

    @Column(name = "ITEM_COMPLETION_TIME")
    private Date itemCompletionTime;

    @Column(name = "ITEM_SERVE_TIME")
    private Date itemServeTime;

    @Column(name = "TOTAL_PROCESSING_TIME")
    private Integer totalProcessingTime;

    @Column(name = "SETTLEMENT_ORDER_ID")
    private Integer settlementOrderId;

}
