package com.stpl.tech.kettle.core.listener;

import com.amazon.sqs.javamessaging.message.SQSObjectMessage;
import com.amazon.sqs.javamessaging.message.SQSTextMessage;
import com.amazonaws.util.Base64;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import com.stpl.tech.kettle.core.notification.OrderInfoQueueResponse;
import com.stpl.tech.kettle.core.service.OrderManagementService;
import com.stpl.tech.util.JSONSerializer;
import lombok.extern.log4j.Log4j2;

import javax.jms.JMSException;
import javax.jms.Message;
import javax.jms.MessageListener;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.ObjectInputStream;
import java.lang.reflect.Type;

@Log4j2
public class OrderInfoListener implements MessageListener {

    private OrderManagementService orderManagementService;

    public OrderInfoListener(OrderManagementService orderManagementService){this.orderManagementService = orderManagementService;}

    @Override
    public void onMessage(Message message) {
        try {
            if (message instanceof SQSObjectMessage) {
                log.info("On OrderInfoCacheEvent Message " + message.getJMSMessageID());
                String response = null;
                SQSObjectMessage object = (SQSObjectMessage) message;
                response = object.getMessageBody();
                String orderInfoJSON = deserialize(response);
                Type type = new TypeToken<OrderInfoQueueResponse>(){}.getType();
                Gson gson = new GsonBuilder().setDateFormat("MMM d, yyyy HH:mm:ss a").create();
                OrderInfoQueueResponse orderInfo =  gson.fromJson(orderInfoJSON,type);
                log.info("trying to get Unit :::: ");
                log.info("unit id : {} , ",orderInfo.getOrderInfo().getUnit().getId());
                orderManagementService.notifyOverWebsocket(orderInfo.getOrderInfo(),orderInfo.getIsRoutedToAssembly());
                object.acknowledge();
                log.info("Order Acknowledge : SQSObjectMessage : " + message.getJMSMessageID());
            } else if (message instanceof SQSTextMessage) {
                log.info("On OrderInfoCacheEvent Message " + message.getJMSMessageID());
                SQSTextMessage object = (SQSTextMessage) message;
                String response =object.getText();
                log.info("SQSObjectMessage : {}", response);
                String orderInfo = deserialize(response);
                Type type = new TypeToken<OrderInfoQueueResponse>(){}.getType();
                Gson gson = new GsonBuilder().setDateFormat("MMM d, yyyy HH:mm:ss a").create();
                OrderInfoQueueResponse orderInfo1 =  gson.fromJson(orderInfo,type);
                orderManagementService.notifyOverWebsocket(orderInfo1.getOrderInfo(),orderInfo1.getIsRoutedToAssembly());
                object.acknowledge();
                log.info("Order Acknowledge : SQSObjectMessage : " + message.getJMSMessageID());
            } else {
                log.info("Order Not Acknowledged {}" , message.getJMSMessageID());
            }
        }catch (Exception e){
            log.info("error while acknowledging Order Info Queue MSG ",e);

        }



    }

    private static String deserialize(String data) throws JMSException {
        if (data == null) {
            return null;
        } else {
            String obj;
            try {
                byte[] b = Base64.decode(data.getBytes());
                ByteArrayInputStream bi = new ByteArrayInputStream(b);
                ObjectInputStream si = new ObjectInputStream(bi);
                obj = (String) si.readObject();
                return obj;
            } catch (IOException ex) {
                log.error("IOException: cannot serialize objectMessage", ex);
            } catch (Exception ex) {
                log.error("IOException: cannot serialize objectMessage", ex);
            }
        }
        return null;
    }
}
