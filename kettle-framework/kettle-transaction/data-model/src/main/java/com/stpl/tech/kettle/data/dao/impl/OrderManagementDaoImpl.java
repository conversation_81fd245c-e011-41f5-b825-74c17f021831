/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 *
 */
package com.stpl.tech.kettle.data.dao.impl;

import com.google.common.base.Stopwatch;
import com.stpl.tech.kettle.clevertap.domain.model.GameLeaderBoardDTO;
import com.stpl.tech.kettle.commission.MonthlyAOVDetail;
import com.stpl.tech.kettle.commission.PartnerAOVRequest;
import com.stpl.tech.kettle.core.CashCardStatus;
import com.stpl.tech.kettle.core.EmailStatus;
import com.stpl.tech.kettle.core.FeedbackEventType;
import com.stpl.tech.kettle.core.FeedbackSource;
import com.stpl.tech.kettle.core.LoyaltyEventType;
import com.stpl.tech.kettle.core.OrderEmailEntryType;
import com.stpl.tech.kettle.core.SubscriptionEventType;
import com.stpl.tech.kettle.core.TransactionConstants;
import com.stpl.tech.kettle.core.TransactionUtils;
import com.stpl.tech.kettle.core.cache.CampaignCache;
import com.stpl.tech.kettle.core.cache.OrderInfoCache;
import com.stpl.tech.kettle.core.cache.StateTransitionCache;
import com.stpl.tech.kettle.core.cache.StateTransitionObject;
import com.stpl.tech.kettle.core.cache.UnitSessionCache;
import com.stpl.tech.kettle.core.cache.UnitTerminalDetail;
import com.stpl.tech.kettle.core.data.vo.CreateOrderResult;
import com.stpl.tech.kettle.core.data.vo.DelayReason;
import com.stpl.tech.kettle.core.data.vo.PartnerDataConsiderRequest;
import com.stpl.tech.kettle.core.data.vo.SubscriptionProduct;
import com.stpl.tech.kettle.core.data.vo.SubscriptionRequest;
import com.stpl.tech.kettle.core.exception.CardValidationException;
import com.stpl.tech.kettle.core.mapper.OrderDelayReasonMapper;
import com.stpl.tech.kettle.core.mapper.OrderPaymentDetailMapper;
import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.core.service.CustomerOfferManagementService;
import com.stpl.tech.kettle.core.service.OrderManagementService;
import com.stpl.tech.kettle.core.service.VoucherManagementService;
import com.stpl.tech.kettle.customer.dao.CashCardDao;
import com.stpl.tech.kettle.customer.dao.CustomerDao;
import com.stpl.tech.kettle.customer.dao.FeedbackManagementDao;
import com.stpl.tech.kettle.customer.dao.LoyaltyDao;
import com.stpl.tech.kettle.customer.dao.SubscriptionPlanDao;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.data.dao.OrderItemStatusDao;
import com.stpl.tech.kettle.data.dao.OrderManagementDao;
import com.stpl.tech.kettle.data.dao.OrderSearchDao;
import com.stpl.tech.kettle.data.dao.PaidEmployeeMealDao;
import com.stpl.tech.kettle.data.dao.PaymentGatewayDao;
import com.stpl.tech.kettle.data.dao.RulesDao;
import com.stpl.tech.kettle.data.dao.TableDataDao;
import com.stpl.tech.kettle.data.model.CashCardDetail;
import com.stpl.tech.kettle.data.model.CashCardOffer;
import com.stpl.tech.kettle.data.model.CustomerBrandMapping;
import com.stpl.tech.kettle.data.model.CustomerCampaignOfferDetail;
import com.stpl.tech.kettle.data.model.CustomerInfo;
import com.stpl.tech.kettle.data.model.CustomerTransactionDetail;
import com.stpl.tech.kettle.data.model.EmployeeMealData;
import com.stpl.tech.kettle.data.model.FeedbackDetail;
import com.stpl.tech.kettle.data.model.FeedbackEvent;
import com.stpl.tech.kettle.data.model.GameLeaderBoard;
import com.stpl.tech.kettle.data.model.GamifiedOfferDetail;
import com.stpl.tech.kettle.data.model.GamifiedOfferResponse;
import com.stpl.tech.kettle.data.model.GamifiedOfferType;
import com.stpl.tech.kettle.data.model.HouseCostConsumableData;
import com.stpl.tech.kettle.data.model.HouseCostEvent;
import com.stpl.tech.kettle.data.model.HouseCostItemData;
import com.stpl.tech.kettle.data.model.InvoiceSequenceId;
import com.stpl.tech.kettle.data.model.LoyaltyScore;
import com.stpl.tech.kettle.data.model.MenuProductCostData;
import com.stpl.tech.kettle.data.model.OrderDelayReason;
import com.stpl.tech.kettle.data.model.OrderDetail;
import com.stpl.tech.kettle.data.model.OrderEmailNotification;
import com.stpl.tech.kettle.data.model.OrderEnquiryItem;
import com.stpl.tech.kettle.data.model.OrderExternalSettlementData;
import com.stpl.tech.kettle.data.model.OrderInvoiceDetail;
import com.stpl.tech.kettle.data.model.OrderItem;
import com.stpl.tech.kettle.data.model.OrderItemAddon;
import com.stpl.tech.kettle.data.model.OrderItemConsumableData;
import com.stpl.tech.kettle.data.model.OrderItemDispenserShotsData;
import com.stpl.tech.kettle.data.model.OrderItemInvoice;
import com.stpl.tech.kettle.data.model.OrderItemInvoiceTaxDetail;
import com.stpl.tech.kettle.data.model.OrderItemMetaDataDetail;
import com.stpl.tech.kettle.data.model.OrderItemStatus;
import com.stpl.tech.kettle.data.model.OrderItemTaxDetail;
import com.stpl.tech.kettle.data.model.OrderMetadataDetail;
import com.stpl.tech.kettle.data.model.OrderPaymentDenominationDetail;
import com.stpl.tech.kettle.data.model.OrderPaymentDetail;
import com.stpl.tech.kettle.data.model.OrderRePrintDetail;
import com.stpl.tech.kettle.data.model.OrderRefundDetail;
import com.stpl.tech.kettle.data.model.OrderSettlement;
import com.stpl.tech.kettle.data.model.OrderStatusEvent;
import com.stpl.tech.kettle.data.model.OrderTaxDetail;
import com.stpl.tech.kettle.data.model.PartnerOrderDiscountMapping;
import com.stpl.tech.kettle.data.model.PartnerOrderRiderStatesDetail;
import com.stpl.tech.kettle.data.model.SpecialOfferDetail;
import com.stpl.tech.kettle.data.model.StateSequenceId;
import com.stpl.tech.kettle.data.model.SubscriptionDetail;
import com.stpl.tech.kettle.data.model.SubscriptionPlan;
import com.stpl.tech.kettle.data.model.SubscriptionPlanEvent;
import com.stpl.tech.kettle.data.model.UnitSequenceId;
import com.stpl.tech.kettle.data.model.UnitTokenSequence;
import com.stpl.tech.kettle.data.model.VariantDispenserShotsAggregatedData;
import com.stpl.tech.kettle.data.util.DesiChaiConsumptionHelper;
import com.stpl.tech.kettle.data.util.PattiSugarHelper;
import com.stpl.tech.kettle.domain.model.CashCardType;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.CustomerTransactionViewData;
import com.stpl.tech.kettle.domain.model.EnquiryItem;
import com.stpl.tech.kettle.domain.model.ExternalSettlement;
import com.stpl.tech.kettle.domain.model.LoyaltyFailedReason;
import com.stpl.tech.kettle.domain.model.MilkVariant;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderDiscountData;
import com.stpl.tech.kettle.domain.model.OrderItemComposition;
import com.stpl.tech.kettle.domain.model.OrderItemConsumable;
import com.stpl.tech.kettle.domain.model.OrderMetadata;
import com.stpl.tech.kettle.domain.model.OrderNotification;
import com.stpl.tech.kettle.domain.model.OrderPaymentDenomination;
import com.stpl.tech.kettle.domain.model.OrderPaymentDetailData;
import com.stpl.tech.kettle.domain.model.OrderStatus;
import com.stpl.tech.kettle.domain.model.ProductSource;
import com.stpl.tech.kettle.domain.model.Settlement;
import com.stpl.tech.kettle.domain.model.SignupOfferStatus;
import com.stpl.tech.kettle.domain.model.TaxDetail;
import com.stpl.tech.kettle.domain.model.TransactionDetail;
import com.stpl.tech.kettle.domain.model.TransitionData;
import com.stpl.tech.kettle.domain.model.TransitionStatus;
import com.stpl.tech.kettle.referral.dao.CashManagerDao;
import com.stpl.tech.master.core.CacheReferenceType;
import com.stpl.tech.master.core.FrequencyOfferType;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.external.cache.EnvironmentPropertiesCache;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.cache.RecipeCache;
import com.stpl.tech.master.data.model.UnitPartnerBrandMappingMetadataType;
import com.stpl.tech.master.domain.model.Brand;
import com.stpl.tech.master.domain.model.CampaignDetail;
import com.stpl.tech.master.domain.model.Consumable;
import com.stpl.tech.master.domain.model.ConsumptionCodeData;
import com.stpl.tech.master.domain.model.CouponDetail;
import com.stpl.tech.master.domain.model.DispenserCanisterItemDataDto;
import com.stpl.tech.master.domain.model.DispenserPattiSugarShotInfoDataDto;
import com.stpl.tech.master.domain.model.DispenserRecipeVariantKey;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.domain.model.PattiSugarType;
import com.stpl.tech.master.domain.model.PaymentCategory;
import com.stpl.tech.master.domain.model.PaymentMode;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.domain.model.ProductClassification;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.master.domain.model.UnitPartnerBrandKey;
import com.stpl.tech.master.payment.model.PaymentStatus;
import com.stpl.tech.master.recipe.model.IngredientProductDetail;
import com.stpl.tech.master.recipe.model.IngredientVariantDetail;
import com.stpl.tech.master.tax.model.TaxationDetailDao;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.JSONSerializer;
import com.stpl.tech.util.TemplateRenderingException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Repository;

import javax.persistence.NoResultException;
import javax.persistence.NonUniqueResultException;
import javax.persistence.Query;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Repository
public class OrderManagementDaoImpl extends AbstractDaoImpl implements OrderManagementDao {

	private static final Logger LOG = LoggerFactory.getLogger(OrderManagementDaoImpl.class);
	private static final String MONTH = "MONTH";
	private static final String DAY = "DAY";
	private static final String DATE_FORMAT = "yyyy-MM-dd";
	private static final String LOYALTY_EVENT_FAILED_STATUS = "FAILED";
    @Autowired
    public FeedbackManagementDao feedbackManagementDao;
    @Autowired
    public PaymentGatewayDao payment;
    @Autowired
    private CustomerDao customeDao;
    @Autowired
    private LoyaltyDao loyaltyDao;
    @Autowired
    private SubscriptionPlanDao subscriptionDao;
    @Autowired
    private CashCardDao cashCardDao;
    /*
     * @Autowired private TempAccessDao tempAccessDao;
     */
    @Autowired
    private RulesDao rulesDao;
    @Autowired
    private MasterDataCache masterCache;
    @Autowired
    private RecipeCache recipeCache;
    @Autowired
    private EnvironmentProperties props;
    @Autowired
    private OrderSearchDao orderSearchDao;
    @Autowired
    private PaidEmployeeMealDao paidEmpoyeeMealDao;
    @Autowired
    private EnvironmentPropertiesCache propertiesCache;
    @Autowired
    private VoucherManagementService voucherManagementService;
    @Autowired
    private TableDataDao tableDataDao;
    @Autowired
    private CashManagerDao cashManagerDao;
    @Autowired
    private CustomerOfferManagementService customerOfferManagementService;

	@Autowired
	@Lazy
	private OrderInfoCache orderInfoCache;

	@Autowired
	private OrderItemStatusDao orderItemStatusDao;


	public CreateOrderResult createOrder(Order order) throws DataUpdationException {
		Stopwatch watch = Stopwatch.createUnstarted();
		Date currentTimestamp = AppUtils.getCurrentTimestamp();
		OrderDetail orderDetail;
		watch.start();
		SubscriptionProduct subscriptionProduct = getSubscriptionProduct(order);
		boolean createSubscription = false;
		Customer customer = null;
		if (subscriptionProduct != null) {
			try {
				customer = customeDao.getCustomer(order.getCustomerId());
			} catch (DataNotFoundException e) {
				UnitSessionCache.getInstance()
						.generateToken(new UnitTerminalDetail(order.getUnitId(), order.getTerminalId()));
				throw new DataUpdationException("Unable to find customer with id " + order.getCustomerId());
			}
			List<String> errors = validateSubscriptionRequest(order, customer);
			if (errors != null && errors.size() > 0) {
				String error = String.format("Errors while punching the subscription order:\n %s",
						StringUtils.join(errors, ",\n"));
				LOG.error(error);
				throw new DataUpdationException(error);
			} else {
				LOG.info("Got the request to purchase subscription for order id {} and customer id {}",
						order.getGenerateOrderId(), order.getCustomerId());
				createSubscription = true;
			}
		}
		System.out.println("########## , STEP A, - , Check for subscription purchase request ----------,"
				+ watch.stop().elapsed(TimeUnit.MILLISECONDS));
		/*
		 * Get Brand MetaData to set and validate sms,email,nsp settings
		 */
		if (TransactionUtils.isCODOrder(order.getSource())
				&& order.getChannelPartner() != AppConstants.BAZAAR_PARTNER_ID) {
			if (order.getTransactionDetail().getTotalAmount()
					.compareTo(order.getTransactionDetail().getTaxableAmount()) != 0) {
				order.setOfferCode(masterCache.getChannelPartner(order.getChannelPartner()).getCode());
			}
		}
		String orderFeedbackType = props.getOrderFeedbackType();
		Brand brand = masterCache.getBrandMetaData().get(order.getBrandId());
		Unit unit = null;
		try {
			unit = masterCache.getUnit(order.getUnitId());

			watch.start();
			orderDetail = addOrder(unit, order, currentTimestamp);
			System.out.println(
					"########## , STEP 0, - , Add Order ----------," + watch.stop().elapsed(TimeUnit.MILLISECONDS));
		} catch (Exception e) {
			UnitSessionCache.getInstance()
					.generateToken(new UnitTerminalDetail(order.getUnitId(), order.getTerminalId()));
			throw e;
		}
		watch.start();
		addOrderEnquiryItems(order, orderDetail.getOrderId());
		System.out.println(
				"########## , STEP 1, - , Add Enquiry Items ----------," + watch.stop().elapsed(TimeUnit.MILLISECONDS));
		watch.start();
		addMetadataDetails(order, orderDetail.getOrderId());
		System.out.println("########## , STEP 2, - , Add Metadata Details ----------,"
				+ watch.stop().elapsed(TimeUnit.MILLISECONDS));
		boolean generateQR = false;
		boolean generateInAppFeedback = false;
		int feedbackId = -1;
		try {
			if (order.getCustomerId() > 5 && !TransactionUtils.isSpecialOrder(order) && !isSubscriptionOrder(order)) {
				Set<Integer> productIds = getProductIds(order);
				if (customer == null) {
					watch.start();
					customer = customeDao.getCustomer(orderDetail.getCustomerId());
					System.out.println("########## , STEP 3, - , Customer Lookup ----------,"
							+ watch.stop().elapsed(TimeUnit.MILLISECONDS));
				}

				if (order.getBrandId() == 1 && !customer.isChaayosCustomer()) {
					// set is chaayos customer flag to true
					customeDao.updateIsChaayosCustomer(customer.getId(), AppUtils.YES);
				}
				// check if loyalty is allowed for brand
				// ---------------------------------------------------------------------------------------------------------------------------
				watch.start();
				BigDecimal loyaltyAwardAmountThreshold = null;
				try {
					loyaltyAwardAmountThreshold = BigDecimal.valueOf(Integer.parseInt(masterCache.getCacheReferenceMetadata(CacheReferenceType.
							LOYALTY_AWARD_THRESHOLD_AMOUNT)));
				}catch (Exception e){
					loyaltyAwardAmountThreshold = BigDecimal.valueOf(79);
				}
				if (brand.getAwardLoyalty() && Objects.nonNull(order.getTransactionDetail()) && !order.isSkipLoyaltyProducts()) {
					if(order.isBypassLoyateaAward() && !productIds.isEmpty()){
						order.setOrderId(orderDetail.getOrderId());
						handleFailedLoyaltyAwardOrder(order,LoyaltyFailedReason.COUPON_REDEMMED.name());
					}
					else if(Objects.nonNull(order.getCashBackReceived()) && order.getCashBackReceived() && props.getLoyaltyFlagForCashBack()){
						order.setOrderId(orderDetail.getOrderId());
						handleFailedLoyaltyAwardOrder(order,LoyaltyFailedReason.CASHBACK_AWARDED.name());
					}
					else if(Objects.nonNull(order.getOfferCode()) && order.getOfferCode().equals(TransactionConstants.SIGNUP_OFFER_CODE)){
						order.setOrderId(orderDetail.getOrderId());
						handleFailedLoyaltyAwardOrder(order, LoyaltyFailedReason.LOYALTY_REDEMMED.name());
					}else if( Objects.nonNull(order.getTransactionDetail().getPaidAmount()) && order.getTransactionDetail().getPaidAmount().compareTo(loyaltyAwardAmountThreshold) >= 0){
						if (!productIds.isEmpty()) {
							int points = order.getAwardLoyalty() == null || order.getAwardLoyalty()
									? masterCache.getUnitPartnerBrandLoyalty(new UnitPartnerBrandKey(order.getUnitId(),
									order.getBrandId(), order.getChannelPartner()))
									: Integer
									.parseInt(UnitPartnerBrandMappingMetadataType.LOYALTY_POINTS.getDefaultValue());
							boolean awarded = loyaltyDao.updateScore(customer.getId(), LoyaltyEventType.OUTLET_VISIT,
									points, orderDetail.getOrderId(), TransactionUtils.hasSignupOffer(order),
									productIds.size() > 0);
							if (points > 0 && awarded) {
								order.setPointsAcquired(points);
							}
						}
					}else{
						order.setOrderId(orderDetail.getOrderId());
						handleFailedLoyaltyAwardOrder(order,LoyaltyFailedReason.ORDER_AMOUNT_WITHIN_THRESHOLD_AMOUNT.name());
					}
					System.out.println("########## , STEP 4, - , Customer Update Loyalty Award----------,"
							+ watch.stop().elapsed(TimeUnit.MILLISECONDS));
				} else {
					System.out.println("########## , STEP 4, - ,Skipping Customer Update Loyalty Award---------- ,"
							+ brand.getBrandName() + " " + watch.stop().elapsed(TimeUnit.MILLISECONDS));
				}
				if (brand.getAwardLoyalty() ) {
					if (order.getPointsRedeemed() != 0) {
						watch.start();
						boolean haveValidLoyaltyPoints = loyaltyDao.updateScore(customer.getId(), LoyaltyEventType.REGULAR_REDEMPTION,
								order.getPointsRedeemed(), orderDetail.getOrderId(), false, productIds.size() > 0);
						if(!haveValidLoyaltyPoints){
							System.out.println("\n########## Customer Don't have enough Loyalty Points for this order");
							throw new DataUpdationException("Customer Don't have enough Loyalty Points for this order");
						}
						System.out.println("########## , STEP 5, - , Customer Update Loyalty Redemption----------,"
								+ watch.stop().elapsed(TimeUnit.MILLISECONDS));
					}
				} else {
					System.out.println("########## , STEP 5, - ,Skipping Customer Update Loyalty Redemption---------- ,"
							+ brand.getBrandName());
				}

				/*
				 * if (props.getAutomatedFeedbackSMSForAll() || order.isNewCustomer()) {
				 * watch.start();
				 *
				 * if (productIds.size() > 0) {
				 * feedbackManagementDao.generateFeedbackData(orderDetail.getOrderId(),
				 * orderDetail.getUnitId(), orderDetail.getOrderSource(), productIds, customer,
				 * currentTimestamp, FeedbackEventType.REGULAR, FeedbackSource.SMS); }
				 * System.out.
				 * println("########## , STEP 6.1, - , Customer Feedback Creation----------," +
				 * watch.stop().elapsed(TimeUnit.MILLISECONDS)); }
				 */
				// check whether nps should be send or not
				LOG.info("Should NPS be sent for + " + brand.getBrandName() + " ? " + brand.getSendNPS());
				if (brand.getSendNPS()) {
					try {
						FeedbackDetail detail;
						if (orderFeedbackType.equals("internal")) {
							detail = createOrderFeedback(productIds, customer, orderDetail, currentTimestamp);
						} else {
							detail = createNPSFeedback(productIds, customer, orderDetail, currentTimestamp);
						}
						if (detail != null) {
							for (FeedbackEvent event : detail.getFeedbackEvents()) {
								LOG.info("NPS event" + event);
								if (event.getEventSource().equals(FeedbackSource.QR.name())) {
									feedbackId = detail.getFeedbackId();
									generateQR = true;
									LOG.info("NPS QR " + event);
								}
							}
						}

						if (orderEligibleForInAppFeedback(productIds, order)) {
							FeedbackDetail inAppFeedback;
							if (orderFeedbackType.equals("internal")) {
								inAppFeedback = createOrderFeedbackInApp(productIds, customer, orderDetail.getOrderId(),
										orderDetail.getUnitId(), orderDetail.getOrderSource(), currentTimestamp);
							} else {
								inAppFeedback = createNPSFeedbackInApp(productIds, customer, orderDetail.getOrderId(),
										orderDetail.getUnitId(), orderDetail.getOrderSource(), currentTimestamp);
							}
							if (inAppFeedback != null) {
								for (FeedbackEvent event : inAppFeedback.getFeedbackEvents()) {
									if (event.getEventSource().equals(FeedbackSource.IN_APP.name())) {
										feedbackId = inAppFeedback.getFeedbackId();
										generateInAppFeedback = true;
									}
								}
							}
						}
					} catch (Exception e) {
						LOG.error("Error while creating NPS FeedBack", e);
					}
				} else {
					LOG.error("NPS Skipped for brand " + brand.getBrandName());
				}

				watch.start();
				// check if email should be trigger for brand or not
				if (brand.getSendEmail() && !AppConstants.EXCLUDE_CUSTOMER_IDS.contains(order.getCustomerId())
						&& !TransactionUtils.isCODOrder(orderDetail.getOrderSource())) {
					generateOrderEmailEvent(OrderEmailEntryType.ORDER, orderDetail.getOrderId(), 1, customer, true,
							currentTimestamp);
					System.out.println("########## , STEP 7, - , Customer Email Event Creation----------,"
							+ watch.stop().elapsed(TimeUnit.MILLISECONDS));
				} else {
					System.out.println("########## , STEP 7, - , Customer Email Event Skipped----------,"
							+ watch.stop().elapsed(TimeUnit.MILLISECONDS));
				}

			} else {
				//
				watch.start();
				if (!AppConstants.EXCLUDE_CUSTOMER_IDS.contains(orderDetail.getCustomerId())
						&& !TransactionUtils.isCODOrder(orderDetail.getOrderSource())) {
					generateOrderEmailEvent(OrderEmailEntryType.ORDER, orderDetail.getOrderId(), 1,
							props.getUndeliveredEmail(), true, true, currentTimestamp, null);
				}
				System.out.println("########## , STEP 8, - , Customer Email Event Creation 1----------,"
						+ watch.stop().elapsed(TimeUnit.MILLISECONDS));
				int reasonId = -1;
				for (com.stpl.tech.kettle.domain.model.OrderItem item : order.getOrders()) {
					if (item.getComplimentaryDetail().isIsComplimentary()
							&& item.getComplimentaryDetail().getReasonCode() != null
							&& item.getComplimentaryDetail().getReasonCode() != AppConstants.COMPLEMENTARY_CODE_COMBO) {
						reasonId = item.getComplimentaryDetail().getReasonCode();
						break;
					}
				}
				if (reasonId != -1 && reasonId == 2107) {
					sendCharityOrderNotification(orderDetail, currentTimestamp);
				}
			}
		} catch (DataNotFoundException e) {
			if (!AppConstants.EXCLUDE_CUSTOMER_IDS.contains(order.getCustomerId())
					&& !TransactionUtils.isCODOrder(orderDetail.getOrderSource())) {
				generateOrderEmailEvent(OrderEmailEntryType.ORDER, orderDetail.getOrderId(), 1, null, true,
						currentTimestamp);
			}
		}
		Set<Integer> paymentIdSet= null;
		if(order.getChannelPartner() == 1 && TransactionUtils.isRegularOrder(order) && Objects.nonNull(order.getSource())
				&& (TransactionUtils.isCafeUnit(order.getSource()) || TransactionUtils.isTakeawayOrder(order.getSource())) ){
			paymentIdSet = order.getSettlements().stream().map(x->x.getMode()).collect(Collectors.toSet());
		}


		if (AppUtils.isAppOrder(order.getChannelPartner()) || AppUtils.isBazaarOrder(order.getChannelPartner()) || AppUtils.isWebAppOrder (order.getChannelPartner()) || (Objects.nonNull(paymentIdSet)
				&& ( paymentIdSet.contains(AppConstants.PAYMENT_MODE_PAYTM_EDC_DEBIT_CREDIT_CARD)  || paymentIdSet.contains(AppConstants.PAYMENT_MODE_PAYTM_EDC_AMEX_CARD) || paymentIdSet.contains(AppConstants.PAYMENT_MODE_PAYTM_EDC_UPI) || paymentIdSet.contains(AppConstants.PAYMENT_MODE_PAYTM_DQR_UPI)))){
			for (OrderSettlement settlement : orderDetail.getOrderSettlements()) {

				PaymentMode mode = masterCache.getPaymentMode(settlement.getPaymentModeId());
				if (AppConstants.PAYMENT_MODE_GIFT_CARD != settlement.getPaymentModeId() && (PaymentCategory.ONLINE.equals(mode.getCategory()) ||
						(settlement.getPaymentModeId() ==AppConstants.PAYMENT_MODE_PAYTM_EDC_DEBIT_CREDIT_CARD  || settlement.getPaymentModeId() ==AppConstants.PAYMENT_MODE_PAYTM_EDC_AMEX_CARD || settlement.getPaymentModeId() ==AppConstants.PAYMENT_MODE_PAYTM_EDC_UPI || settlement.getPaymentModeId() ==AppConstants.PAYMENT_MODE_PAYTM_DQR_UPI ))) {
					if (settlement.getExternalTransactions() != null) {
						for (OrderExternalSettlementData s : settlement.getExternalTransactions()) {
							OrderPaymentDetail paymentDetail = payment
									.getActivePaymentDetail(s.getExternalTransactionId());
							if (paymentDetail != null) {
								paymentDetail.setOrderId(orderDetail.getOrderId());
								paymentDetail.setOrderSettlementId(settlement.getSettlementId());
								manager.merge(paymentDetail);
							}
						}
					}
				}
			}
		} else {
			if (order.getExternalOrderId() != null && order.getExternalOrderId().trim().length() > 0
					&& !isGiftCardPayment(order)) {
				watch.start();
				OrderPaymentDetail paymentDetail = payment.getActivePaymentDetail(order.getExternalOrderId());
				if (paymentDetail != null) {
					paymentDetail.setOrderId(orderDetail.getOrderId());
					paymentDetail.setOrderSettlementId(orderDetail.getOrderSettlements().get(0).getSettlementId());
					manager.merge(paymentDetail);
				}
				System.out.println("########## , STEP 9, - , Time Taken To Link The Payment Request----------,"
						+ watch.stop().elapsed(TimeUnit.MILLISECONDS));
			}

		}

		if (order.getPaymentDetailId() != null) {
			OrderPaymentDetail orderPaymentDetail = manager.find(OrderPaymentDetail.class, order.getPaymentDetailId());
			orderPaymentDetail.setOrderId(orderDetail.getOrderId());
			orderPaymentDetail.setOrderSettlementId(orderDetail.getOrderSettlements().get(0).getSettlementId());
			orderPaymentDetail.setPaymentStatus(PaymentStatus.SUCCESSFUL.name());
			orderPaymentDetail.setUpdateTime(AppUtils.getCurrentTimestamp());
			manager.merge(orderPaymentDetail);
			manager.flush();
		}

		if (order.getOptionResultEventId() != null) {
			watch.start();
			rulesDao.attach(order.getOptionResultEventId(), orderDetail.getOrderId());
			System.out.println("########## , STEP 10, - , Time Taken To Link The Recommendation----------,"
					+ watch.stop().elapsed(TimeUnit.MILLISECONDS));
		}

		if (TransactionUtils.isTableOrder(order)) {
			if (TransactionUtils.isGyftrCard(propertiesCache.isGyftrActive(), order.getOrders().get(0).getCardType())) {
				// do not attach table for gyftr gift card orders
				orderDetail.setTableNumber(null);
				orderDetail.setTableRequestId(null);
			} else {
				tableDataDao.addOrderTableMapping(orderDetail, order.getTableRequestId(), customer);
				orderItemStatusDao.initiateOrderItemStatus(orderDetail,orderDetail.getTableRequestId());
			}
		}else if(AppConstants.isDineIn(orderDetail.getChannelPartnerId())){
			orderItemStatusDao.initiateOrderItemStatus(orderDetail,null);
		}

		if (AppUtils.CHAAYOS_CASH_OFFER_CODE.contains(order.getOfferCode()) && order.getCashRedeemed() != null
				&& order.getCashRedeemed().intValue() > 0) {
			// cash redemption
			watch.start();

			cashManagerDao.redeemCash(order.getCustomerId(), order.getCashRedeemed(), orderDetail.getOrderId());

			System.out.println("########## , STEP 11, - , Time Taken To update cash redemption----------,"
					+ watch.stop().elapsed(TimeUnit.MILLISECONDS));
		} else if (AppUtils.CHAAYOS_CASH_OFFER_CODE.contains(order.getOfferCode())
				&& (order.getCashRedeemed() == null || order.getCashRedeemed().intValue() == 0)) {
			throw new DataUpdationException("Could not find chaayos cash for the customer");
		}

		if (customer != null && !customer.isReferrerAwarded() && customer.getReferrerId() != null) {
			// activate referral on transaction
			watch.start();
			cashManagerDao.activateReferralCashPacket(order.getCustomerId(), customer.getReferrerId(),
					orderDetail.getOrderId());
			System.out.println("########## , STEP 11, - , Time Taken To activate cash referral----------,"
					+ watch.stop().elapsed(TimeUnit.MILLISECONDS));
		}

		CreateOrderResult result = new CreateOrderResult();
		result.setOrderId(orderDetail.getOrderId());
		List<String> newCards = new ArrayList<>();
		for (com.stpl.tech.kettle.domain.model.OrderItem item : order.getOrders()) {
			if (item.getCardType() != null && (CashCardType.ECARD.name().equalsIgnoreCase(item.getCardType()))
					|| voucherManagementService.isGyftrCard(item.getCardType())) {
				newCards.add(item.getItemCode());
			}
		}
		if (createSubscription) {
			watch.start();
			OrderItem subscriptionProductItem = getSubscriptionProductItem(orderDetail,
					subscriptionProduct.getProductId());
			String channelPartnerName = masterCache.getChannelPartner(orderDetail.getChannelPartnerId()).getName();
			SubscriptionPlan plan = createSubscription(subscriptionProduct, orderDetail.getOrderId(),
					subscriptionProductItem.getOrderItemId(), customer, null,channelPartnerName, 0);
			System.out.println("########## , STEP 12, - , Create subscription purchase request ----------,"
					+ watch.stop().elapsed(TimeUnit.MILLISECONDS));
			result.setSubscriptionPlan(plan);
		}
		result.setGiftCard(newCards);
		result.setFeedbackId(feedbackId);
		result.setGenerateQRCode(generateQR);
		result.setGenerateInAppFeedback(generateInAppFeedback);
		return result;
	}

	private void handleFailedLoyaltyAwardOrder(Order order , String loyaltyFailedReason){
		try {
			LoyaltyScore score = getCustomerLoyaltyScore(order.getCustomerId());
			if (Objects.nonNull(score)) {
				if (AppConstants.NO.equals(score.getAvailedSignupOffer()) && TransactionUtils.hasSignupOffer(order)
						&& Objects.nonNull(score.getOrderCount()) && score.getOrderCount() >= 1) {
					score.setAvailedSignupOffer(AppConstants.YES);
					score.setSignupOfferStatus(SignupOfferStatus.REDEEMED.name());
					score.setRedemptionOrderId(order.getOrderId());
					loyaltyDao.add(score);
				}else if(Objects.isNull(score.getOrderCount())){
					score.setLastOrderId(order.getOrderId());
					score.setLastOrderTime(AppUtils.getCurrentTimestamp());
					score.setOrderCount(1);
					loyaltyDao.add(score);
				}

				int points = order.getAwardLoyalty() == null || order.getAwardLoyalty()
						? masterCache.getUnitPartnerBrandLoyalty(new UnitPartnerBrandKey(order.getUnitId(),
						order.getBrandId(), order.getChannelPartner()))
						: Integer.parseInt(UnitPartnerBrandMappingMetadataType.LOYALTY_POINTS.getDefaultValue());
				loyaltyDao.createLoyaltyEvent(order.getCustomerId(), points, LoyaltyEventType.OUTLET_VISIT,LOYALTY_EVENT_FAILED_STATUS,
						order.getOrderId(), score.getAcquiredPoints(), score.getAcquiredPoints(),
						loyaltyFailedReason);
			}
		} catch (Exception e) {
			LOG.error("Error while updating status for redemption", e);
		}
	}

	public void handleZeroAmountOrder(Order order) {
		try{
			LoyaltyScore score = getCustomerLoyaltyScore(order.getCustomerId());
			if(Objects.nonNull(score)){
				if(AppConstants.NO.equals(score.getAvailedSignupOffer()) && TransactionUtils.hasSignupOffer(order) && Objects.nonNull(score.getOrderCount()) && score.getOrderCount() >= 1){
					score.setAvailedSignupOffer(AppConstants.YES);
					score.setSignupOfferStatus(SignupOfferStatus.REDEEMED.name());
					score.setRedemptionOrderId(order.getOrderId());
				}
				score.setLastOrderId(order.getOrderId());
				score.setLastOrderTime(AppUtils.getCurrentTimestamp());
				score.setOrderCount(score.getOrderCount() == null ? 1 : score.getOrderCount() + 1);
				manager.persist(score);
				manager.flush();
			}
		}catch (Exception e){
			LOG.error("Error while updating status for redemption",e);
		}
	}

	@Override
	public SubscriptionPlan createSubscription(SubscriptionProduct subscriptionProduct, Customer customer, Integer campaignId, String source, Integer lagDays)
			throws DataUpdationException {
		Stopwatch watch = Stopwatch.createUnstarted();
		boolean createSubscription = false;
		if (subscriptionProduct != null) {
			watch.start();
			List<String> errors = validateCustomerSubscriptionRequest(customer);
			if (errors != null && errors.size() > 0) {
				String error = String.format("Errors while enabling subscription %s for customer %s:\n %s",
						subscriptionProduct, customer.getId(), StringUtils.join(errors, ",\n"));
				LOG.error(error);
				throw new DataUpdationException(error);
			} else {
				LOG.info("Got the request to enable subscription for subscription {} and customer id {}",
						subscriptionProduct, customer.getId());
				createSubscription = true;
			}
		}
		System.out.println("########## , STEP A, - , Check for subscription purchase request ----------,"
				+ watch.stop().elapsed(TimeUnit.MILLISECONDS));
		/*
		 * Get Brand MetaData to set and validate sms,email,nsp settings
		 */
		if (createSubscription) {
			watch.start();
			SubscriptionPlan plan = createSubscription(subscriptionProduct, -1, -1, customer,campaignId,source, lagDays);
			System.out.println("########## , STEP 12, - , Create subscription purchase request ----------,"
					+ watch.stop().elapsed(TimeUnit.MILLISECONDS));
			return plan;
		}
		return null;
	}

	@Override
	public CustomerInfo getCustomerFromContact(String contactNumber) {
		try {
			Query query = manager.createQuery("FROM CustomerInfo ci WHERE ci.contactNumber = :contact");
			query.setParameter("contact",contactNumber);
			return (CustomerInfo) query.getSingleResult();
		}catch (Exception e){
			LOG.info("Error while fetching customer info for contact number : {}",contactNumber);
			return null;
		}
	}

	@Override
	public List<String> getOfferStrings(Integer customerId, Integer brandId) {
		try {
			Query query = manager.createNativeQuery("CALL GET_SPECIAL_OFFER_STRING(:customerId)");
			query.setParameter("customerId", customerId);
			List<Object[]> objs = query.getResultList();
			Map<Integer,List<String>> offerMap =new HashMap<>();
			List<String> offerStrings = new ArrayList<>();
			offerStrings.add((String) objs.get(0)[1]);
			offerStrings.add((String) objs.get(0)[2]);
			return offerStrings;
		}catch (Exception e){
			LOG.error("Error while getting offer string for customerId : {}", customerId, e);
		}
		return null;
	}

	@Override
	public Map<Integer,SpecialOfferDetail> getActiveOffer(String contactNumber, Integer campaignId, String utmMedium) {
		try {
			Query query = manager.createQuery("FROM SpecialOfferDetail sod WHERE sod.contactNumber = :contact " +
					" AND sod.recordStatus= :active");
			query.setParameter("contact", contactNumber);
			query.setParameter("active", AppConstants.ACTIVE);
			List<SpecialOfferDetail> offerDetails = query.getResultList();
			Map<Integer,SpecialOfferDetail> offerMap = new HashMap<>();
			if(offerDetails.size() == 0){
				return null;
			}else{
				for(SpecialOfferDetail offerDetail : offerDetails){
					offerMap.put(offerDetail.getCampaignId(),offerDetail);
				}
			}
			return offerMap;
		}catch (NoResultException e){
			LOG.info("ERROR while fetching existing customer offer for contact number : {} and campaign id : {} No offer found", contactNumber,campaignId);
		}catch (Exception e){
			LOG.info("Error while fetching customer offer for contact number : {} and campaign id : {}",contactNumber,campaignId,e);
		}
		return null;
	}

	@Override
	public Map<String, List<GamifiedOfferResponse>> getActiveGameOffer(String contactNumber, Integer campaignId, String source, CampaignCache campaignCache) {
		try {
			Query query = manager.createNativeQuery("SELECT G.OFFER_TYPE, G.MAX_USAGE, G.CASH_AMOUNT, G.COUPON_CODE, " +
					"G.OFFER_TEXT, G.START_DATE, G.END_DATE, G.TNC, G.REF_CODE, " +
					"(SELECT C.CAMPAIGN_ID FROM CUSTOMER_CAMPIAGN_OFFER_DETAIL C where C.CUSTOMER_CAMPIAGN_OFFER_DETAIL_ID = G.CUSTOMER_CAMPAIGN_OFFER_DETAIL_ID) CAMPAIGN_ID, " +
					"(SELECT C.OFFER_APPLIED FROM CUSTOMER_CAMPIAGN_OFFER_DETAIL C where C.CUSTOMER_CAMPIAGN_OFFER_DETAIL_ID = G.CUSTOMER_CAMPAIGN_OFFER_DETAIL_ID) IS_OFFER_APPLIED, " +
					"(SELECT C.COUPON_END_DATE FROM CUSTOMER_CAMPIAGN_OFFER_DETAIL C where C.CUSTOMER_CAMPIAGN_OFFER_DETAIL_ID = G.CUSTOMER_CAMPAIGN_OFFER_DETAIL_ID) COUPON_END_DATE, " +
					"(SELECT sum(CURRENT_AMOUNT) FROM CASH_PACKET_DATA CP where CP.CUSTOMER_ID = G.CUSTOMER_ID and CP.EVENT_STATUS = :active) TOTAL_CHAAYOS_CASH "+
					"FROM GAMIFIED_OFFER_DETAIL G  " +
					"WHERE G.CONTACT_NUMBER = :contact " +
					" AND G.RECORD_STATUS= :active " +
					"AND G.CAMPAIGN_ID = :campaignId " +
					"AND G.OFFER_SOURCE = :offerSource");
			query.setParameter("contact",contactNumber);
			query.setParameter("active", AppConstants.ACTIVE);
			query.setParameter("campaignId", campaignId);
			query.setParameter("offerSource", source);
			List<Object[]> gamifiedOfferDetailList = query.getResultList();
			return getExistingOfferMap(gamifiedOfferDetailList, campaignCache);
		}catch (NoResultException e){
			LOG.error("No active offer found for contact : {} and campaign id : {} ",contactNumber, campaignId);
		}catch (Exception e){
			LOG.error("Exception while fetching active offer found for contact : {} and campaign id : {} ",contactNumber, campaignId,e);
		}
		return  new HashMap<>();
	}



	private Map<String, List<GamifiedOfferResponse>> getExistingOfferMap(List<Object[]> gamifiedOfferDetailList, CampaignCache campaignCache){
		Map<String, List<GamifiedOfferResponse>> data = new HashMap<>();
		for(Object[] detail : gamifiedOfferDetailList){
			String offerType = String.valueOf(detail[0]);
			if(!data.containsKey(offerType)){
				data.put(offerType, new ArrayList<>());
			}
			if(GamifiedOfferType.MEMBERSHIP.name().equals(offerType)){
				boolean isExpired = AppUtils.getCurrentTimestamp().after(AppUtils.getDate(String.valueOf(detail[6]), AppUtils.DATE_FORMAT_STRING));
				data.get(offerType).add(GamifiedOfferResponse.builder()
						.offerType(offerType)
						.isExistingOffer(true)
						.offerCode(String.valueOf(detail[3]))
						.text(String.valueOf(detail[4]))
						.validityFrom(String.valueOf(detail[5]))
						.validityTill(String.valueOf(detail[6]))
						.offerTnCString(Objects.nonNull(detail[7]) ? String.valueOf(detail[7]) : null)
						.refCode(Objects.nonNull(detail[8]) ? String.valueOf(detail[8]) : null)
						.isOfferApplied(isExpired)
						.build());
			}else if(GamifiedOfferType.CHAAYOS_CASH.name().equals(offerType)){
				boolean isExpired = AppUtils.getCurrentTimestamp().after(AppUtils.getDate(String.valueOf(detail[6]), AppUtils.DATE_FORMAT_STRING));
				data.get(offerType).add(GamifiedOfferResponse.builder()
						.offerType(offerType)
						.isExistingOffer(true)
						.chaayosCash(Objects.nonNull(detail[2])?BigDecimal.valueOf(Double.parseDouble(String.valueOf(detail[2]))):BigDecimal.ZERO)
						.offerCode(String.valueOf(detail[3]))
						.text(String.valueOf(detail[4]))
						.validityFrom(String.valueOf(detail[5]))
						.validityTill(String.valueOf(detail[6]))
						.offerTnCString(Objects.nonNull(detail[7]) ? String.valueOf(detail[7]) : null)
						.refCode(Objects.nonNull(detail[8]) ? String.valueOf(detail[8]) : null)
						.isOfferApplied(isExpired)
						.remainingChaayosCash(Objects.nonNull(detail[12])?BigDecimal.valueOf(Double.parseDouble(String.valueOf(detail[12]))):BigDecimal.ZERO)
						.build());
			} else if (GamifiedOfferType.NBO.name().equals(offerType) || GamifiedOfferType.DNBO.name().equals(offerType)) {
				if(!data.containsKey(offerType)){
				List<GamifiedOfferResponse> newList = new ArrayList<>();
				data.put(offerType,newList);
			}
			boolean isCouponUnavailable = (Objects.nonNull(detail[10]) && AppConstants.YES.equals(String.valueOf(detail[10])))
					|| AppUtils.getCurrentTimestamp().after(AppUtils.getDate(String.valueOf(detail[11]), AppUtils.DATE_FORMAT_STRING));
			CampaignDetail campaignDetail = campaignCache.getCampaign(Integer.parseInt(String.valueOf(detail[9])));data.get(offerType).add(GamifiedOfferResponse.builder()
					.offerType(offerType)
					.maxUsage(null)
						.chaayosCash(Objects.nonNull(detail[2]) ? BigDecimal.valueOf(Double.parseDouble(String.valueOf(detail[2]))) : BigDecimal.ZERO)
						.isExistingOffer(true)
						.offerCode(String.valueOf(detail[3]))
						.text(String.valueOf(detail[4]))
						.validityFrom(String.valueOf(detail[5]))
						.validityTill(String.valueOf(detail[6]))
						.offerTnCString(String.valueOf(detail[7]))
						.refCode(String.valueOf(detail[8]))
						.offerCampaignId(Integer.parseInt(String.valueOf(detail[9])))
						.isOfferApplied(isCouponUnavailable)
						.crmAppBannerUrl(Objects.nonNull(campaignDetail) ? campaignDetail.getCrmAppBannerUrl() : null)
						.build());
			}
		}
		return data;
	}

	@Override
	public Boolean isSpecialOfferExist(String contact, Integer campaignId) {
		try{
			Query query = manager.createQuery("FROM SpecialOfferDetail sod WHERE sod.campaignId= :campaignId AND " +
					"sod.contactNumber = :contact");
			query.setParameter("campaignId",campaignId);
			query.setParameter("contact",contact);
			List<SpecialOfferDetail> specialOfferDetails = query.getResultList();
			return specialOfferDetails.size()>0 ? true : false;
		}catch (NoResultException e){
			LOG.error("No special offer found for contact : {} and campaign id: {}",contact,campaignId);
		}catch (Exception e){
			LOG.error("Exception occured while fetching special offer for contact : {} and campaign id: {}",contact,campaignId,e);
		}
		return false;
	}

	@Override
	public Boolean isGamifiedOfferExist(String contact, Integer campaignId, String source) {
		try{
			Query query = manager.createQuery("FROM GamifiedOfferDetail god WHERE god.campaignId= :campaignId AND " +
					"god.contactNumber = :contact AND god.recordStatus = :active AND god.offerSource = :source ");
			query.setParameter("campaignId",campaignId);
			query.setParameter("contact",contact);
			query.setParameter("active",AppConstants.ACTIVE);
			query.setParameter("source",source);
			List<GamifiedOfferDetail> gamifiedOfferDetails = query.getResultList();
			return gamifiedOfferDetails.size()>0 ? true : false;
		}catch (NoResultException e){
			LOG.error("No gamified offer found for contact : {} and campaign id: {}",contact,campaignId);
		}catch (Exception e){
			LOG.error("Exception occured while fetching gamified offer for contact : {} and campaign id: {}",contact,campaignId,e);
		}
		return false;
	}

	@Override
	public void createOrderNotificationData(OrderNotification orderNotification, OrderInfo info) {
		com.stpl.tech.kettle.data.model.OrderNotification orderNotificationData = new com.stpl.tech.kettle.data.model.OrderNotification();
		if(Objects.nonNull(orderNotification)){
			try {
				orderNotificationData.setOrderId(info.getOrder().getOrderId());
				orderNotificationData.setSubscriptionName(orderNotification.getSubscriptionName());
				orderNotificationData.setOfferDescription(orderNotification.getOfferDescription());
				orderNotificationData.setValidDays(orderNotification.getValidDays());
				orderNotificationData.setPlanEndDate(orderNotification.getPlanEndDate());
				orderNotificationData.setCustomerName(orderNotification.getCustomerName());
				orderNotificationData.setSelectOverallSaving(orderNotification.getSelectOverallSaving());
				orderNotificationData.setSelectSavingAmt(orderNotification.getSelectSavingAmount());
				orderNotificationData.setNextOfferText(orderNotification.getNextOfferText());
				orderNotificationData.setValidityTill(orderNotification.getValidityTill());
				orderNotificationData.setChannelPartner(orderNotification.getChannelPartner());
				orderNotificationData.setLoyalTeaTotalCount(orderNotification.getLoyalTeaTotalCount());
				orderNotificationData.setTotalLoyalTeaPoint(orderNotification.getTotalLoyalTeaPoint());
				orderNotificationData.setLoyalTeaPoints(orderNotification.getLoyalTeaPoints());
				orderNotificationData.setOrderAmt(orderNotification.getOrderAmt());
				orderNotificationData.setSavingAmt(orderNotification.getSavingAmt());
				orderNotificationData.setSavingText(orderNotification.getSavingText());
				orderNotificationData.setOrderFeedbackUrl(orderNotification.getOrderFeedBackUrl());
				orderNotificationData.setOrderRecieptUrl(orderNotification.getOrderRecieptUrl());
				orderNotificationData.setIsSubscriptionUsed(AppUtils.setStatus(orderNotification.getIsSubscriptionUsed()));
				orderNotificationData.setSubscriptionValidityInDays(orderNotification.getSubscriptionValidityInDays());
				orderNotificationData.setWalletPurchaseAmt(orderNotification.getWalletPurchaseAmt());
				orderNotificationData.setWalletPendingAmt(orderNotification.getWalletPendingAmount());
//			orderNotificationData.setIs(orderNotification.getIsWalletPurchased());
				orderNotificationData.setWalletSavingAmt(orderNotification.getWalletSavingAmount());
				orderNotificationData.setWalletExtraAmt(orderNotification.getWalletExtraAmount());
//			orderNotificationData.setWalletUsed(orderNotification.getWalletUsed());
				orderNotificationData.setGeneratedOrderId(orderNotification.getGenerateOrderId());
				orderNotificationData.setItemCode(orderNotification.getItemCode());
				orderNotificationData.setCashPendingAmt(orderNotification.getCashPendingAmount());
				orderNotificationData.setVoucherCode(orderNotification.getVoucherCode());
//			orderNotificationData.setSmsTemplateDate(orderNotification.getSmsTemplateDate());
				orderNotificationData.setUsedAmt(orderNotification.getUsedAmount());
				orderNotificationData.setCashBackAmt(orderNotification.getCashBackAmount());
				orderNotificationData.setCashBackStartDate(orderNotification.getCashBackAllotmentStartDate());
//			orderNotificationData.setCashBackAllotmentEndDate(orderNotification.getCashBackAllotmentEndDate());
				orderNotificationData.setRefundAmt(orderNotification.getRefundAmount());
				orderNotificationData.setIsSmsSubscriber(AppUtils.setStatus(orderNotification.isSmsSubscriber()));
				orderNotificationData.setIsWhatsappOptIn(AppUtils.setStatus(orderNotification.isWhatsAppOptIn()));
				orderNotificationData.setCustomerContact(orderNotification.getCustomerContactNumber());
				orderNotificationData.setUnitName(info.getOrder().getUnitName());
				orderNotificationData.setIsLoyaltyUnlocked(AppUtils.setStatus(orderNotification.getIsLoyaltyUnlocked()));
				orderNotificationData.setSubscriptionValidityInDays(orderNotification.getDaysLeft());
				orderNotificationData.setIsSubscriptionPurched(AppUtils.setStatus(orderNotification.getIsSubscriptionPurched()));
		}catch (Exception e) {
				LOG.error("Error while setting orderNotification data for orderId :::{} ::::{}", info.getOrder().getOrderId(), e);
			}
		}
		try{
			manager.persist(orderNotificationData);
		}catch(Exception e){
			LOG.error("Error while persisting orderNotification data for orderId :::{} ::::{}",info.getOrder().getOrderId(),e);
		}
	}


	@Override
    public void updateCustomerTransaction(int customerId, int brandId) {
        try {
            CustomerTransactionDetail customerTransactionDetail = customeDao.getCustomerTransactionDetail(customerId, brandId);
            CustomerTransactionViewData data = customerOfferManagementService.getCustomerTransactionViewData(customerId, brandId, Collections.singletonList(-1));
            if(data.getFirstOrderBusinessDate() == null) {
                data.setFirstOrderBusinessDate(AppUtils.getCurrentDate());
            }
            if(data.getLastOrderBusinessDate() == null) {
                data.setFirstOrderBusinessDate(AppUtils.getCurrentDate());
            }
            if(customerTransactionDetail == null) {
                customerTransactionDetail = new CustomerTransactionDetail();
            }
            if(customerTransactionDetail.getCustomerId() == null) {
                customerTransactionDetail.setCustomerId(customerId);
            }
            if(customerTransactionDetail.getBrandId() == null) {
                customerTransactionDetail.setBrandId(brandId);
            }
            customerTransactionDetail.setFirstOrderBusinessDate(data.getFirstOrderBusinessDate());
            customerTransactionDetail.setFirstOrderId(data.getFirstOrderId());
            customerTransactionDetail.setFirstOrderSource(data.getFirstOrderSource());
            customerTransactionDetail.setFirstOrderChannelPartnerId(data.getFirstOrderChannelPartnerId());
            customerTransactionDetail.setFirstOrderUnitId(data.getFirstOrderUnitId());
            customerTransactionDetail.setLastOrderBusinessDate(data.getLastOrderBusinessDate());
            customerTransactionDetail.setLastOrderId(data.getLastOrderId());
            customerTransactionDetail.setLastOrderSource(data.getLastOrderSource());
            customerTransactionDetail.setLastOrderUnitId(data.getLastOrderUnitId());
            customerTransactionDetail.setLastOrderChannelPartnerId(data.getLastOrderChannelPartnerId());
            customerTransactionDetail.setTotalDeliveryOrders(data.getTotalDeliveryOrders());
            customerTransactionDetail.setTotalDineInOrders(data.getTotalDineInOrders());
            customerTransactionDetail.setTotalOrders(data.getTotalOrders());
            customerTransactionDetail = manager.merge(customerTransactionDetail);
            if(customerTransactionDetail == null) {
                LOG.error("Error saving customerTransactionDetail");
            }
        } catch (Exception e) {
            LOG.error("Error updating customer transaction view data", e);
        }

    }

	private SubscriptionPlan createSubscription(SubscriptionProduct subscriptionProduct, Integer orderId,
			Integer orderItemId, Customer customer, Integer campaignId, String source,Integer lagDays){
		com.stpl.tech.master.domain.model.Pair<CouponDetail,Product> subscriptionObj =
				masterCache.getSubscriptionSkuCodeDetail()
						.get(masterCache.getSubscriptionProductDetail(subscriptionProduct.getProductId()).getSkuCode());
		SubscriptionRequest request = new SubscriptionRequest();
		request.setOrderId(orderId);
		request.setOfferDescription(subscriptionObj.getValue().getSkuCode());
		request.setOrderItemId(orderItemId);
		request.setProductId(subscriptionProduct.getProductId());
		request.setPrice(subscriptionProduct.getPrice());
		request.setDimension(subscriptionProduct.getDimensionCode());
		request.setValidityInDays(subscriptionProduct.getValidityInDays());
		request.setCustomerId(customer.getId());
		SubscriptionPlan plan = subscriptionDao.getActiveSubscription(customer.getId(), request.getOfferDescription());
		if (plan == null) {
			Date today = AppUtils.addDays(AppUtils.getCurrentDate(),lagDays);
			Date finalEndDate = AppUtils.addDays(today, subscriptionProduct.getValidityInDays());
			request.setStartDate(today);
			request.setEndDate(finalEndDate);
			request.setFrequencyStrategy(subscriptionObj.getKey().getOffer().getFrequencyStrategy());
			request.setOverAllFrequency(BigDecimal.ZERO);
			request.setType(SubscriptionEventType.NEW_SUBSCRIPTION);
			if(FrequencyOfferType.QUANTITY_BASED.name().equals(subscriptionObj.getKey().getOffer().getFrequencyStrategy())
					|| FrequencyOfferType.TIME_QUANTITY_BASED.name().equals(subscriptionObj.getKey().getOffer().getFrequencyStrategy())){
				request.setFrequencyLimit(BigDecimal.valueOf(subscriptionObj.getKey().getOffer().getMaxQuantity()));
			} else {
				request.setFrequencyLimit(BigDecimal.valueOf(Integer.MAX_VALUE));
			}
			return subscriptionDao.addSubscription(request, source, campaignId);
		} else {
			request.setPlanId(plan.getSubscriptionPlanId());
			// Case 1 : Current Subscription is active and the customer needs to extend it.
			if (plan.getPlanEndDate().compareTo(AppUtils.getBusinessDate()) >=  0) {
				Date finalEndDate = AppUtils.addDays(plan.getPlanEndDate(), subscriptionProduct.getValidityInDays());
				request.setStartDate(plan.getPlanStartDate());
				request.setEndDate(finalEndDate);
				request.setType(SubscriptionEventType.RENEW_BEFORE_EXPIRY);
				request.setFrequencyStrategy(subscriptionObj.getKey().getOffer().getFrequencyStrategy());
				request.setOverAllFrequency(plan.getOverAllFrequency());
				if(FrequencyOfferType.QUANTITY_BASED.name().equals(subscriptionObj.getKey().getOffer().getFrequencyStrategy())
						|| FrequencyOfferType.TIME_QUANTITY_BASED.name().equals(subscriptionObj.getKey().getOffer().getFrequencyStrategy())){
					request.setFrequencyLimit(AppUtils.add(plan.getFrequencyLimit(),
							BigDecimal.valueOf(subscriptionObj.getKey().getOffer().getMaxQuantity())));
				} else {
					request.setFrequencyLimit(BigDecimal.valueOf(Integer.MAX_VALUE));
				}
				return subscriptionDao.updateSubscriptionData(request, source, campaignId);
			} else {
				Date today = AppUtils.getCurrentDate();
				Date finalEndDate = AppUtils.addDays(today, subscriptionProduct.getValidityInDays());
				request.setStartDate(today);
				request.setEndDate(finalEndDate);
				request.setType(SubscriptionEventType.RENEW_AFTER_EXPIRY);
				return subscriptionDao.updateSubscriptionData(request, source, campaignId);
			}
		}
	}

	private int getValidityDaysInDays(String dimensionCode) {
		int months = 1;
		int days = 45;
		String addType = MONTH;
		if (dimensionCode.toLowerCase().contains("month")) {
			int endIndex = dimensionCode.toLowerCase().indexOf("month");
			String monthString = dimensionCode.substring(0, endIndex);
			months = Integer.parseInt(monthString);
		}else if(dimensionCode.toLowerCase().contains("day")){
			addType = DAY;
			String[] str = dimensionCode.toLowerCase().split("day");
			days = Integer.parseInt(str[0]);
		}
		Date today = AppUtils.getBusinessDate();
		Date later = MONTH.equals(addType) ? AppUtils.addMonthsInDate(today, months) : AppUtils.addDays(today, days);
		return AppUtils.getDaysDiff(later, today);
	}

	private List<String> validateSubscriptionOrderRequest(Order order, Customer customer){
		List<String> errors = new ArrayList<>();
		int count = 0;
		for (com.stpl.tech.kettle.domain.model.OrderItem item : order.getOrders()) {
			if (Objects.nonNull(masterCache.getSubscriptionProductDetail(item.getProductId()))) {
				count++;
				if (item.getQuantity() != 1) {
					errors.add(String.format("Subscription Item cannot have quantity other than 1 : %d",
							item.getQuantity()));
				}
			}
		}
		if (count != 1) {
			errors.add("Cannot Add More than 1 items of subscription in order");
		}
		if (TransactionUtils.isCODOrder(order.getSource()) && order.getChannelPartner() != AppConstants.BAZAAR_PARTNER_ID) {
			errors.add("Subscription Cannot be added in COD Order");
		}
//		if (order.getOfferCode() != null && order.getOfferCode().length() > 0) {
//			errors.add("Subscription Cannot be added in Orders with Offers");
//		}
		if (TransactionUtils.isSpecialOrder(order)) {
			errors.add("Subscription Cannot be added through special orders");
		}
		if (AppConstants.EXCLUDE_CUSTOMER_IDS.contains(order.getCustomerId())) {
			errors.add("Subscription Cannot be added for the customer Id " + order.getCustomerId());
		}
		return errors;
	}

	private List<String> validateCustomerSubscriptionRequest(Customer customer) {
		List<String> errors = new ArrayList<>();
		try {
			if (customer == null) {
				errors.add("Subscription Cannot be added for unavailable customer Id ");
			}
			if (customer != null && !customer.isContactNumberVerified()) {
				errors.add("Subscription Cannot be added for unverified customer Id " + customer.getId());
			}
			if (AppConstants.EXCLUDE_CUSTOMER_IDS.contains(customer.getId())) {
				errors.add("Subscription Cannot be added for excluded customer Id " + customer.getId());
			}
			LOG.info("Fetching Subscription Info for Customer {}", customer.getId());
			SubscriptionPlan plan = subscriptionDao.getSubscription(customer.getId());
			if (Objects.nonNull(plan) && Objects.nonNull(plan.getPlanStartDate())
					&& Objects.nonNull(plan.getPlanEndDate()) && plan.getStatus().equals(AppConstants.ACTIVE)) {
//                Integer subscriptionValidity = AppUtils.getActualDayDifference(plan.getPlanStartDate(),plan.getPlanEndDate());
				Integer remainingDays = AppUtils.getActualDayDifference(AppUtils.getCurrentDate(),
						plan.getPlanEndDate());
				if (props.getSubscriptionValidBuyingDay() <= remainingDays) {
					errors.add("Subscription Cannot be purchased as customer has reached the validity limit");
				} else if (remainingDays < 0) {
					plan.setStatus(AppConstants.IN_ACTIVE);
					manager.persist(plan);
					manager.flush();
				}
//                if (subscriptionValidity > 92 && remainingDays > 92) {
//                    errors.add("Subscription Cannot be purchased as customer has reached the validity limit");
//                }
//                if (Objects.nonNull(couponCode) && !plan.getSubscriptionPlanCode().equals(couponCode)){
//                    errors.add("Subscription Cannot be purchased as customer has different subscription offer");
//                }
			}
		} catch (Exception e) {
			LOG.error("Exception Faced While Fetching Subscription {}", customer.getId());
		}
		return errors;
	}

	private List<String> validateSubscriptionRequest(Order order, Customer customer) {

		List<String> errors = new ArrayList<>();
		List<String> currentErrors = validateSubscriptionOrderRequest(order, customer);
		if (currentErrors != null && currentErrors.size() > 0) {
			errors.addAll(currentErrors);
		}
		currentErrors = validateCustomerSubscriptionRequest(customer);
		if (currentErrors != null && currentErrors.size() > 0) {
			errors.addAll(currentErrors);
		}

		return errors;
	}


	private SubscriptionProduct getSubscriptionProduct(Order order) {
		for (com.stpl.tech.kettle.domain.model.OrderItem item : order.getOrders()) {
			if (masterCache.getSubscriptionProductDetails().containsKey(item.getProductId())) {
				Product product = masterCache.getSubscriptionProductDetail(item.getProductId());
				return convert(item, product);
			}
		}
		return null;
	}




	private SubscriptionProduct convert(com.stpl.tech.kettle.domain.model.OrderItem item, Product prd) {
		SubscriptionProduct product = new SubscriptionProduct();
		product.setDimensionCode(item.getDimension());
		product.setPrice(item.getPrice());
		product.setProductId(item.getProductId());
		product.setProductName(item.getProductName());
		product.setSubscriptionCode(prd.getSkuCode());
		product.setValidityInDays(getValidityDaysInDays(item.getDimension()));
		return product;
	}

	private OrderItem getSubscriptionProductItem(OrderDetail order, int productId) {
		for (OrderItem item : order.getOrderItems()) {
			if (item.getProductId() == productId)
				return item;
		}
		return null;
	}

	private Set<Integer> getProductIds(Order order) {
        Set<Integer> productIds = new HashSet<>();
		for (com.stpl.tech.kettle.domain.model.OrderItem item : order.getOrders()) {
			if(AppConstants.SKIP_LOYALTY_PRODUCTS.contains(item.getProductId())){
				order.setSkipLoyaltyProducts(true);
			}
			if (!AppUtils.isGiftCard(item.getCode()) && Objects.isNull(masterCache.getSubscriptionProductDetail(item.getProductId()))) {
				productIds.add(item.getProductId());
			}
		}
        return productIds;
    }


    private boolean isGiftCardPayment(Order order) {
        boolean gcPayment = false;
        for (Settlement s : order.getSettlements()) {
            if (s.getMode() == AppConstants.PAYMENT_MODE_GIFT_CARD) {
                gcPayment = true;
            }
        }
        return gcPayment;
    }

    private FeedbackDetail createNPSFeedbackInApp(Set<Integer> productIds, Customer customer, Integer orderId, int unitId, String orderSource,
                                                  Date currentTimestamp) {
        FeedbackDetail feedback = null;
        Stopwatch watch = Stopwatch.createUnstarted();
        FeedbackEventType eventType = FeedbackEventType.NPS;
        FeedbackSource eventSource = FeedbackSource.IN_APP;
        watch.start();
        feedback = feedbackManagementDao.getFeedback(orderId, eventSource, eventType);
        if(feedback == null){
            feedback = feedbackManagementDao.getFeedback(orderId, FeedbackSource.IN_APP, FeedbackEventType.ORDER_FEEDBACK);
        }
        FeedbackSource source = null;
//        if (Objects.nonNull(customer.getOptWhatsapp())){
//            source = AppUtils.getStatus(customer.getOptWhatsapp()) ? FeedbackSource.WHATS_APP : FeedbackSource.SMS;
//        }else {
            source=FeedbackSource.SMS;
       // }
        if (feedback != null) {
            boolean notFound = true;
            for (FeedbackEvent event : feedback.getFeedbackEvents()) {
                if (event.getEventSource().equals(eventSource.name())) {
                    notFound = false;
                }
            }
            if (notFound) {
                FeedbackSource[] feedbackSources = {eventSource, source};
                feedbackManagementDao.generateFeedBackEvent(orderId, orderSource, currentTimestamp, eventType, feedback, feedbackSources);
            }
        } else {
            FeedbackSource[] feedbackSources = {eventSource, source};
            feedback = feedbackManagementDao.generateFeedbackData(orderId,
                    unitId, orderSource, productIds, customer,
                    currentTimestamp, eventType, feedbackSources);
        }

        System.out.println("########## , STEP 6.2.3, - , NPS In APP Feedback Creation----------,"
                + watch.stop().elapsed(TimeUnit.MILLISECONDS));
        return feedback;

    }

    private FeedbackDetail createOrderFeedbackInApp(Set<Integer> productIds, Customer customer, Integer orderId, int unitId, String orderSource,
                                                  Date currentTimestamp) {
        FeedbackDetail feedback = null;
        Stopwatch watch = Stopwatch.createUnstarted();
        FeedbackEventType eventType = FeedbackEventType.ORDER_FEEDBACK;
        watch.start();
        feedback = feedbackManagementDao.getFeedback(orderId, FeedbackSource.IN_APP, eventType);
        if(feedback == null){
            feedback = feedbackManagementDao.getFeedback(orderId, FeedbackSource.IN_APP, FeedbackEventType.NPS);
        }
        FeedbackSource source = null;
//        if (Objects.nonNull(customer.getOptWhatsapp())){
//            source = AppUtils.getStatus(customer.getOptWhatsapp()) ? FeedbackSource.WHATS_APP : FeedbackSource.SMS;
//        }else {
            source=FeedbackSource.SMS;
       // }
        if (feedback != null) {
            boolean notFoundInApp = true;
            boolean notFoundSms = true;
            for (FeedbackEvent event : feedback.getFeedbackEvents()) {
                if (event.getEventSource().equals(FeedbackSource.IN_APP.name())) {
                    notFoundInApp = false;
                }
                if (event.getEventSource().equals(source.name())) {
                    notFoundSms = false;
                }
            }
            if(notFoundInApp && notFoundSms){
                FeedbackSource[] feedbackSources = {FeedbackSource.IN_APP, source};
                feedbackManagementDao.generateFeedBackEvent(orderId, orderSource, currentTimestamp, eventType, feedback, feedbackSources);
            }else if(notFoundInApp){
                FeedbackSource[] feedbackSources = {FeedbackSource.IN_APP};
                feedbackManagementDao.generateFeedBackEvent(orderId, orderSource, currentTimestamp, eventType, feedback, feedbackSources);
            }
            else if (notFoundSms) {
                FeedbackSource[] feedbackSources = {source};
                feedbackManagementDao.generateFeedBackEvent(orderId, orderSource, currentTimestamp, eventType, feedback, feedbackSources);
            }
        } else {
            FeedbackSource[] feedbackSources = {FeedbackSource.IN_APP, source};
            feedback = feedbackManagementDao.generateFeedbackData(orderId,
                    unitId, orderSource, productIds, customer,
                    currentTimestamp, eventType, feedbackSources);
        }

        System.out.println("########## , STEP 6.2.3, - , NPS In APP Feedback Creation----------,"
                + watch.stop().elapsed(TimeUnit.MILLISECONDS));
        return feedback;

    }

    private FeedbackDetail createNPSFeedback(Set<Integer> productIds, Customer customer, OrderDetail orderDetail,
                                             Date currentTimestamp) {
        if(!customer.isContactNumberVerified() || (!props.getNPSConsiderationForDelivery() && orderDetail.getOrderSource().equals(AppConstants.COD))){
            //!false && COD && !false
            LOG.info("Skipping Feedback For COD Order with Order ID {}",orderDetail.getOrderId());
            return null;
        }
        FeedbackDetail feedback = null;
        Stopwatch watch = null;
        // check if nps is set or not for brand
        Brand brand = masterCache.getBrandMetaData().get(orderDetail.getBrandId());
        if (brand.getSendNPS() && !productIds.isEmpty()) {
            if (props.getSendFeedbackMessageForSwiggyDelivery() && orderDetail.getChannelPartnerId() == AppConstants.CHANNEL_PARTNER_SWIGGY) {
                watch = Stopwatch.createUnstarted();
                watch.start();
                feedback = feedbackManagementDao.generateFeedbackData(orderDetail.getOrderId(), orderDetail.getUnitId(),
                        orderDetail.getOrderSource(), productIds, customer, currentTimestamp, FeedbackEventType.NPS,
                        FeedbackSource.QR);
                System.out.println("########## , STEP 6.2.1, - ,Swiggy NPS Feedback Creation----------,"
                        + watch.stop().elapsed(TimeUnit.MILLISECONDS));
            } else {
                watch = Stopwatch.createUnstarted();
                // product Id Size checks if order has gift card items only
                Date triggerTime = feedbackManagementDao.getTriggerTime(FeedbackSource.SMS,
                        orderDetail.getOrderSource(), currentTimestamp, FeedbackEventType.NPS);
                if (triggerTime == null) {
                    LOG.info("NPS Trigger Time is Null");
                }
                boolean npsApplicable = AppUtils.checkNPSApplicable(triggerTime, customer.getLastNPSTime());
                boolean isCustomerAvilable = customer.isSmsSubscriber() && !customer.isBlacklisted()
                        && !customer.isDND();

                if (npsApplicable && isCustomerAvilable) {
                    watch.start();
                   // FeedbackSource source = AppUtils.getStatus(customer.getOptWhatsapp()) ? FeedbackSource.WHATS_APP : FeedbackSource.SMS;
					FeedbackSource source =  FeedbackSource.SMS;
					if (feedbackManagementDao.availableForNPSEvent(customer.getId(), triggerTime)) {
                        feedback = feedbackManagementDao.generateFeedbackData(orderDetail.getOrderId(),
                                orderDetail.getUnitId(), orderDetail.getOrderSource(), productIds, customer,
                                currentTimestamp, FeedbackEventType.NPS, source);
                    }
                    System.out.println("########## , STEP 6.2.2, - , NPS Feedback Creation----------,"
                            + watch.stop().elapsed(TimeUnit.MILLISECONDS));
                }
            }

        }
        return feedback;
    }

    public FeedbackDetail createOrderFeedback(Set<Integer> productIds, Customer customer, OrderDetail orderDetail,
                                              Date currentTimestamp){
        if(!customer.isContactNumberVerified() || (!props.getNPSConsiderationForDelivery() && orderDetail.getOrderSource().equals(AppConstants.COD))){
            //!false && COD && !false
            LOG.info("Skipping Feedback For COD Order with Order ID {}",orderDetail.getOrderId());
            return null;
        }
        FeedbackDetail feedback = null;
        Stopwatch watch = null;
        // check if nps is set or not for brand
        Brand brand = masterCache.getBrandMetaData().get(orderDetail.getBrandId());
        if (brand.getSendNPS() && !productIds.isEmpty()) {
            if (props.getSendFeedbackMessageForSwiggyDelivery() && orderDetail.getChannelPartnerId() == AppConstants.CHANNEL_PARTNER_SWIGGY) {
                watch = Stopwatch.createUnstarted();
                watch.start();
                feedback = feedbackManagementDao.generateFeedbackData(orderDetail.getOrderId(), orderDetail.getUnitId(),
                        orderDetail.getOrderSource(), productIds, customer, currentTimestamp, FeedbackEventType.ORDER_FEEDBACK,
                        FeedbackSource.QR);
                System.out.println("########## , STEP 6.2.1, - ,Swiggy NPS Feedback Creation----------,"
                        + watch.stop().elapsed(TimeUnit.MILLISECONDS));
            } else {
                watch = Stopwatch.createUnstarted();
                // product Id Size checks if order has gift card items only
//                Date triggerTime = feedbackManagementDao.getTriggerTime(FeedbackSource.SMS,
//                        orderDetail.getOrderSource(), currentTimestamp, FeedbackEventType.NPS);
//                if (triggerTime == null) {
//                    LOG.info("NPS Trigger Time is Null");
//                }
//                boolean npsApplicable = AppUtils.checkNPSApplicable(triggerTime, customer.getLastNPSTime());
                boolean isCustomerAvilable = customer.isSmsSubscriber() && !customer.isBlacklisted()
                        && !customer.isDND();

                if (isCustomerAvilable) {
                    watch.start();
                    //FeedbackSource source = AppUtils.getStatus(customer.getOptWhatsapp()) ? FeedbackSource.WHATS_APP : FeedbackSource.SMS;
					FeedbackSource source =  FeedbackSource.SMS;
					feedback = feedbackManagementDao.generateFeedbackData(orderDetail.getOrderId(),
                            orderDetail.getUnitId(), orderDetail.getOrderSource(), productIds, customer,
                            currentTimestamp, FeedbackEventType.ORDER_FEEDBACK, source);

                    System.out.println("########## , STEP 6.2.2, - , NPS Feedback Creation----------,"
                            + watch.stop().elapsed(TimeUnit.MILLISECONDS));
                }
            }

        }
        return feedback;
    }

    private void sendCharityOrderNotification(OrderDetail orderDetail, Date currentTimestamp) {
        String mailIds = propertiesCache.getCharityMailIds();
        if (mailIds != null && !AppConstants.EXCLUDE_CUSTOMER_IDS.contains(orderDetail.getCustomerId())
                && !TransactionUtils.isCODOrder(orderDetail.getOrderSource())) {
            generateOrderEmailEvent(OrderEmailEntryType.CHARITY_ORDER, orderDetail.getOrderId(), 1, mailIds, true, true,
                    currentTimestamp, null);
        }
    }

	private void setPriceProfileMetadata(Order order , Integer orderId){
		if(Objects.nonNull(order.getPriceProfileId()) && Objects.nonNull(order.getPriceProfileVersion())){
			OrderMetadataDetail detail = new OrderMetadataDetail();
			detail.setOrderId(orderId);
			detail.setAttributeName(AppConstants.PRICE_PROFILE_ID);
			detail.setAttributeValue(String.valueOf(order.getPriceProfileId()));
			manager.persist(detail);
			OrderMetadataDetail detail1 = new OrderMetadataDetail();
			detail1.setOrderId(orderId);
			detail1.setAttributeName(AppConstants.PRICE_PROFILE_VERSION);
			detail1.setAttributeValue(String.valueOf(order.getPriceProfileVersion()));
			manager.persist(detail1);

		}
	}

    private void addMetadataDetails(Order order, Integer orderId) {
        try {
			setPriceProfileMetadata(order,orderId);
            List<OrderMetadata> list = order.getMetadataList();
            if (list != null && !list.isEmpty()) {
                for (OrderMetadata data : list) {
                    OrderMetadataDetail detail = new OrderMetadataDetail();
                    detail.setOrderId(orderId);
                    detail.setAttributeName(data.getAttributeName());
                    detail.setAttributeValue(data.getAttributeValue());
                    manager.persist(detail);
                }
                manager.flush();
            }
        } catch (Exception e) {
            LOG.error("Error While Saving Metadata for Order Id {}", orderId, e);
        }
    }

	private void addOrderItemMetaDataDetails(com.stpl.tech.kettle.domain.model.OrderItem item, Integer orderItemId,
											 Integer orderId, Integer customerId,Boolean isCodOrder) {
		try{
			OrderItemMetaDataDetail orderItemMetaDataDetail = new OrderItemMetaDataDetail();
			orderItemMetaDataDetail.setOrderItemId(orderItemId);
			orderItemMetaDataDetail.setOrderId(orderId);
			orderItemMetaDataDetail.setCustomerId(customerId);
			if(Objects.nonNull(item.getItemName()) && Boolean.TRUE.equals(isCodOrder)){
				orderItemMetaDataDetail.setItemName(item.getItemName());
			}
			else{
				if(Objects.nonNull(item.getPreferenceDetail()) && Objects.nonNull(item.getPreferenceDetail().getPreferenceId())){
					orderItemMetaDataDetail.setIsSavedChai(AppConstants.YES);
					orderItemMetaDataDetail.setSavedChaiId(item.getPreferenceDetail().getPreferenceId());
				}
			}
			setSpecialMilkVariant(orderItemMetaDataDetail,item);
			manager.persist(orderItemMetaDataDetail);
		}catch (Exception e){
			LOG.error("Error While Saving Item Metadata Detail For Order Item Id :::: {}  :::: {} ",item.getItemId(),e);
		}
	}

	private void setSpecialMilkVariant(OrderItemMetaDataDetail orderItemMetaDataDetail , com.stpl.tech.kettle.domain.model.OrderItem orderItem){
		if(Objects.nonNull(orderItem.getComposition()) && !CollectionUtils.isEmpty(orderItem.getComposition().getOptions())){
			orderItem.getComposition().getOptions().forEach(option ->{
				if(Objects.nonNull(masterCache.getSpecialMilkVariant(option))){
					orderItemMetaDataDetail.setSpecialMilkVariant(masterCache.getSpecialMilkVariant(option));
					MilkVariant milkVariant = MilkVariant.builder().productName(option).productId(masterCache.getSpecialMilkVariant(option)).
					profile(AppUtils.getMilkVariantPaidAddonPrefix(option) + orderItem.getRecipeProfile()).build();
					orderItem.setMilkVariant(milkVariant);
				}
			});
		}
	}

    public boolean addOrderEnquiryItems(Order order, Integer orderId) {
        try {
            List<EnquiryItem> enquiryItems = order.getEnquiryItems();
            Date time = AppUtils.getCurrentTimestamp();
            for (EnquiryItem enquiryItem : enquiryItems) {
                OrderEnquiryItem orderEnquiryItem = new OrderEnquiryItem();
                orderEnquiryItem.setOrderId(orderId);
                orderEnquiryItem.setProductId(enquiryItem.getId());
                orderEnquiryItem.setCustomerId(enquiryItem.getLinkedCustomerId());
                orderEnquiryItem.setUnitId(enquiryItem.getLinkedUnitId());
                orderEnquiryItem.setAvailableQuantity(enquiryItem.getAvailableQuantity());
                orderEnquiryItem.setOrderderedQuantity(enquiryItem.getOrderedQuantity());
                orderEnquiryItem.setReplacementServed(enquiryItem.isReplacementServed());
                orderEnquiryItem.setEnquiryTime(time);
                orderEnquiryItem.setEnquiryOrderId(Long.valueOf(time.getTime()).toString());
                manager.persist(orderEnquiryItem);
            }
            manager.flush();
            return true;
        } catch (Exception e) {
            LOG.info("Failed to save enquiry items for orderId: " + orderId);
            return false;
        }
    }

    public boolean updateOrder(Order order) throws DataUpdationException {
        OrderDetail orderDetail = getOrderDetailObject(order.getGenerateOrderId());
        orderDetail.setDeliveryPartnerId(order.getDeliveryPartner());
        orderDetail = manager.merge(orderDetail);
        manager.flush();
        return orderDetail.getDeliveryPartnerId() != order.getDeliveryPartner() ? false : true;
    }

    public OrderStatusEvent deleteOrder(int unitId, String generatedOrderId, int cancelledBy, int cancelApprovedBy,
                                        String reason, Integer reasonId, String bookWastage)
            throws DataUpdationException, DataNotFoundException, CardValidationException {
        try {
            int lastOrderId = orderSearchDao.getLastDayCloseOrderId(unitId);
            OrderDetail data = getOrderDetailObject(generatedOrderId, lastOrderId, unitId);
            OrderStatusEvent ose = generateOrderStatusEvent(true, data.getOrderId(),
                    OrderStatus.valueOf(data.getOrderStatus()), OrderStatus.CANCELLED, cancelApprovedBy, cancelledBy,
                    reason);
            if (data != null && TransitionStatus.SUCCESS.name().equals(ose.getTransitionStatus())) {
                data = cancelOrder(data, reason, cancelApprovedBy, cancelledBy, null, reasonId, bookWastage);
                List<CashCardDetail> cards = cashCardDao.getCardsByPurchaseOrderId(data.getOrderId());
                if (cards != null) {
                    cashCardDao.blockCards(data.getOrderId());
                }
                data = manager.merge(data);
                manager.flush();
            }
            return ose;
        } catch (NoResultException e) {
            throw new DataNotFoundException(
                    String.format("Did not find Active Order Details with ID : %s", generatedOrderId), e);

        } catch (CardValidationException cashException) {
            throw cashException;
        } catch (Exception e) {
            LOG.error("Encountered error while updating", e);
            throw new DataUpdationException(
                    String.format("Error while cancelling the order  with ID : %s", generatedOrderId), e);
        }
    }

    private OrderDetail cancelOrder(OrderDetail data, String reason, int cancelApprovedBy, int cancelledBy,
                                    Boolean refund, Integer reasonId, String bookWastage)
            throws CardValidationException, DataUpdationException {
        Date currentTimeStamp = AppUtils.getCurrentTimestamp();
        long toleranceLimit = 180;
        long minutes = (data.getBillingServerTime().getTime() - currentTimeStamp.getTime()) / 60000;
        if (!props.getEnvironmentType().equals(EnvType.DEV) && minutes >= toleranceLimit) {
            throw new DataUpdationException("Orders older than three hours cannot be cancelled or refunded");
        }

        refundCashCard(data);
        if (refund != null) {
            if (refund) {
                initiateRefund(data, reason);
            } else {
                disassociatePaymentDetail(data);
            }
        }
        Date cancellationTime = AppUtils.getCurrentTimestamp();
        data.setOrderStatus(OrderStatus.CANCELLED.name());
        data.setCancelationReason(reason);
        data.setBillCancellationTime(cancellationTime);
        data.setCancelApprovedBy(cancelApprovedBy);
        data.setCancelledBy(cancelledBy);
        if (reasonId != null) {
            data.setCancellationReasonId(reasonId);
        }
        if (bookWastage != null) {
            data.setWastageType(bookWastage);
        }
        for (OrderItem item : data.getOrderItems()) {
            if (reasonId != null) {
                item.setCancellationReasonId(reasonId);
            }
            if (bookWastage != null) {
                item.setWastageBooked(AppConstants.getValue(!bookWastage.equals("NO_WASTAGE")));
            }

        }
        try {
            if (!AppConstants.EXCLUDE_CUSTOMER_IDS.contains(data.getCustomerId())
                    && !TransactionUtils.isCODOrder(data.getOrderSource())){
                generateCancelledOrderEmailEvent(OrderEmailEntryType.ORDER_CANCELLATION, data.getOrderId(), 1,
                        customeDao.getCustomer(data.getCustomerId()), true, cancellationTime, data.getUnitId());
            }        } catch (DataNotFoundException e) {
            LOG.error("Error in generating cancellation email event for order id {} ", data.getOrderId());
        }

        manager.flush();
        return data;
    }

	@Override
	public void resetOverallFrequency(Order orderDetail){
		LOG.info("Resetting overall frequency on cancellation for order id {}", orderDetail.getOrderId());
		BigDecimal frequencyUsed = BigDecimal.ZERO;
		for (com.stpl.tech.kettle.domain.model.OrderItem orderItem : orderDetail.getOrders()){
			if(Objects.nonNull(orderItem.getDiscountDetail().getDiscountReason()) &&
					orderItem.getDiscountDetail().getDiscountReason().equals(AppConstants.CHAI_PREPAID)){
				frequencyUsed = AppUtils.add(frequencyUsed,AppUtils.divide(
						AppUtils.subtract(
								AppUtils.multiply(orderItem.getPrice(),
										BigDecimal.valueOf(orderItem.getQuantity())),orderItem.getAmount()),orderItem.getPrice()));
			}
		}
		SubscriptionPlan subscriptionPlan = subscriptionDao.getChaayosPrepaidSubscription(orderDetail.getCustomerId());
		if(Objects.isNull(subscriptionPlan)){
			LOG.info("Can't reset overall frequency as no subscription plan found for customer id {} and order id {}", orderDetail.getCustomerId(), orderDetail.getOrderId());
			return;
		}
		SubscriptionPlanEvent subscriptionPlanEvent = manager.find(SubscriptionPlanEvent.class, subscriptionPlan.getLastRenewalEventId());
		subscriptionPlan.setOverAllFrequency(AppUtils.subtract(subscriptionPlan.getOverAllFrequency(), frequencyUsed));
		if(AppConstants.IN_ACTIVE.equals(subscriptionPlan.getStatus())){
			LOG.info("Activating subscription as quantity is reverted");
			subscriptionPlan.setStatus(AppConstants.ACTIVE);
			subscriptionPlanEvent.setStatus(AppConstants.ACTIVE);
			subscriptionPlan.setOverAllSaving(AppUtils.subtract(subscriptionPlan.getOverAllSaving(), orderDetail.getTransactionDetail().getSavings()));
		}
		manager.persist(subscriptionPlan);
		manager.persist(subscriptionPlanEvent);
		manager.flush();
	}

    private void disassociatePaymentDetail(OrderDetail orderDetail) {
        Integer orderId = orderDetail.getOrderId();
        List<OrderSettlement> settlements = getOnlineSettlements(orderDetail);
        for (OrderSettlement settlement : settlements) {
            OrderPaymentDetail paymentDetail = payment.getSuccessfulPaymentDetail(settlement.getSettlementId(),
                    orderId);
            if (paymentDetail != null) {
                paymentDetail.setPaymentStatus(PaymentStatus.DISASSOCIATED.name());
                paymentDetail.setRefundRequestTime(AppUtils.getCurrentTimestamp());
                paymentDetail.setRefundRequested(AppConstants.NO);
                manager.merge(paymentDetail);
                manager.flush();
            }
        }
    }

    private List<OrderSettlement> getOnlineSettlements(OrderDetail orderDetail) {
        List<Integer> onlinePaymentModes = masterCache.getPaymentModes().values().stream()
                .filter(paymentMode -> paymentMode.getCategory().equals(PaymentCategory.ONLINE))
                .map(paymentMode -> paymentMode.getId()).collect(Collectors.toList());

        List<OrderSettlement> settlements = orderDetail.getOrderSettlements().stream()
                .filter(orderSettlement -> onlinePaymentModes.contains(orderSettlement.getPaymentModeId()))
                .collect(Collectors.toList());

        return settlements;
    }

    private void initiateRefund(OrderDetail orderDetail, String cancellationReason) {
        Integer orderId = orderDetail.getOrderId();
        List<OrderSettlement> settlements = getOnlineSettlements(orderDetail);
        for (OrderSettlement settlement : settlements) {
            OrderPaymentDetail paymentDetail = payment.getSuccessfulPaymentDetail(settlement.getSettlementId(),
                    orderId);
            if (paymentDetail != null) {
                paymentDetail.setRefundStatus(PaymentStatus.INITIATED.name());
                paymentDetail.setRefundRequestTime(AppUtils.getCurrentTimestamp());
                paymentDetail.setRefundReason(cancellationReason);
                paymentDetail.setRefundRequested(AppConstants.YES);
                paymentDetail.setPaymentStatus(PaymentStatus.REFUND_INITIATED.name());
                manager.merge(paymentDetail);
                manager.flush();
            }
        }
    }

    private boolean refundCashCard(OrderDetail order) throws CardValidationException {
        boolean flag = false;

        List<OrderSettlement> orderSettlements = order.getOrderSettlements();

        for (OrderSettlement orderSettlement : orderSettlements) {
            if (orderSettlement.getPaymentModeId() == TransactionConstants.PAYMENT_MODE_CASH_CARD) {
                try {
                    for (OrderExternalSettlementData external : orderSettlement.getExternalTransactions()) {
                        cashCardDao.cancelCardEvent(order.getOrderId(), orderSettlement.getSettlementId(),
                                Integer.valueOf(external.getExternalTransactionId()));
                        flag = true;
                    }
                } catch (Exception e) {
                    LOG.error("Encountered error while updating", e);
                    throw new CardValidationException(
                            "Could not cancel card event for this order " + order.getGeneratedOrderId());
                }
            }
        }
        return flag;
    }

    private OrderDetail addOrder(Unit unit, Order order, Date currentTimestamp) throws DataUpdationException {
        int stateId = unit.getLocation().getState().getId();
        Stopwatch watch = Stopwatch.createUnstarted();
        watch.start();
        if (order.getOrderId() != null && order.getOrderId() != 0) {
            throw new DataUpdationException(
                    String.format("Cannot create the order that already exists with ID %d ", order.getOrderId()));

        }
        OrderDetail detail = new OrderDetail();
        detail.setUnitOrderId(getNextUnitOrderId(order.getUnitId(), order.getSource()));
        if (unit.isTokenEnabled()) {
            detail.setTokenNumber(getNextTokenNumber(order.getUnitId(), unit.getTokenLimit()));
        }
        detail.setRefOrderId(order.getRefOrderId());
        detail.setIsInvoice(AppConstants.getValue(false));
        detail.setOrderType(order.getOrderType());
        detail.setLinkedOrderId(order.getLinkedOrderId());
        detail.setBillGenerationTime(currentTimestamp);
        detail.setBillingServerTime(currentTimestamp);
        detail.setBillStartTime(order.getBillStartTime());
        detail.setBillCreationSeconds(order.getBillCreationSeconds());
        detail.setOrderSource(order.getSource());
        detail.setOrderSourceId(order.getSourceId());
        detail.setChannelPartnerId(order.getChannelPartner());
        detail.setDeliveryPartnerId(order.getDeliveryPartner());
        setTransactionDetail(detail, order.getTransactionDetail());
        detail.setEmpId(order.getEmployeeId());
        boolean isTakeAway = !AppConstants.isDineIn(order.getChannelPartner());
        detail.setHasParcel(AppConstants.getValue(isTakeAway));
        detail.setUnitId(order.getUnitId());
        detail.setTerminalId(order.getTerminalId());
        detail.setTableNumber(order.getTableNumber());
        detail.setOrderStatus(order.getStatus().name());
        detail.setSettlementType(order.getSettlementType().name());
        detail.setPrintCount(1);
        detail.setDeliveryAddress(order.getDeliveryAddress());
        detail.setOrderRemark(order.getOrderRemark());
        detail.setGeneratedOrderId(order.getGenerateOrderId());
        detail.setCustomerId(order.getCustomerId());
        detail.setPointsRedeemed(order.getPointsRedeemed());
        detail.setCampaignId(order.getCampaignId());
        detail.setBrandId(order.getBrandId());
        detail.setPartnerCustomerId(order.getPartnerCustomerId());
		detail.setSourceVersion(AppConstants.KETTLE_ORDER_VERSION);
        if (order.getBillBookNo() != null) {
            detail.setManualBillBookNo(order.getBillBookNo());
        }
        if (isSubscriptionOrder(order)) {
            detail.setSubscriptionDetail(
                    manager.find(SubscriptionDetail.class, order.getSubscriptionDetail().getSubscriptionId()));
        }
        detail.setOfferCode(order.getOfferCode());
        //MOHIT - Truncated the customer name to 45 characters
		detail.setCustomerName(order.getCustomerName() != null && order.getCustomerName().trim().length() > 0
				? order.getCustomerName().substring(0,
						order.getCustomerName().length() > 45 ? 45 : order.getCustomerName().length())
				: null);
        detail.setTableRequestId(order.getTableRequestId());
        detail.setGiftCardOrder(AppConstants.getValue(order.isGiftCardOrder()));
        detail.setOrderAttribute(order.getOrderAttribute());

        manager.persist(detail);
        System.out.println(
                "&&&&&&&&&& , STEP 1, - , Add order detail ----------," + watch.stop().elapsed(TimeUnit.MILLISECONDS));
        watch.start();
        /*
         * Unit unitData = masterCache.getUnit(order.getUnitId()); String code = null;
         * if (unitData.isFreeInternetAccess() &&
         * order.getSource().equals(UnitCategory.CAFE.name())) { code =
         * tempAccessDao.generateAccessCode(detail.getOrderId()); }
         * detail.setTempCode(code);
         */
		//here5
		Map<String, OrderItemInvoice> invoices = new HashMap<>();
        detail.getOrderItems().addAll(addItems(stateId, detail, order.getOrders(),order.getMetadataList(), invoices));
		if (Objects.nonNull(order.getTransactionDetail().getServiceCharge())
				&& order.getTransactionDetail().getServiceCharge().compareTo(BigDecimal.ZERO) > 0
				&& Objects.nonNull(order.getServiceChargeItem())) {
			detail.getOrderItems().addAll(addItems(stateId, detail, Collections.singletonList(order.getServiceChargeItem()),
					order.getMetadataList(), invoices));
		}
        System.out.println(
                "&&&&&&&&&& , STEP 2, - , Add Order Item ----------," + watch.stop().elapsed(TimeUnit.MILLISECONDS));
        watch.start();
        detail.getOrderSettlements().addAll(addSettlements(detail, order.getSettlements()));
        System.out.println("&&&&&&&&&& , STEP 3, - , Add Order Settlement ----------,"
                + watch.stop().elapsed(TimeUnit.MILLISECONDS));
        watch.start();
        generateOrderStatusEvent(false, detail.getOrderId(), OrderStatus.INITIATED, order.getStatus(),
                order.getEmployeeId(), order.getEmployeeId(), "Order Created");
        System.out.println("&&&&&&&&&& , STEP 4, - , Add Order Status Event ----------,"
                + watch.stop().elapsed(TimeUnit.MILLISECONDS));
        if (order.isEmployeeMeal() != null && order.isEmployeeMeal()) {
            watch.start();
            createEmployeeMeal(detail, order);
            System.out.println("&&&&&&&&&& , STEP 5, - , Create Employee Meal ----------,"
                    + watch.stop().elapsed(TimeUnit.MILLISECONDS));
        }
        if (TransactionUtils.isPaidEmployeeMeal(order)) {
            watch.start();
            updatePaidEmployeeMealData(detail, order);
            System.out.println("&&&&&&&&&& , STEP 5.a, - , Create Paid Employee Meal ----------,"
                    + watch.stop().elapsed(TimeUnit.MILLISECONDS));
        }
        if (order.getOrderDiscountData() != null && !order.getOrderDiscountData().isEmpty()) {
            watch.start();
            createOrderDiscountMapping(detail, order);
            System.out.println("&&&&&&&&&& , STEP 6, - , Create Partner Order Discount Mapping ----------,"
                    + watch.stop().elapsed(TimeUnit.MILLISECONDS));
        }
        watch.start();
        setTaxDetail(detail, order.getTransactionDetail().getTaxes());
        System.out.println(
                "&&&&&&&&&& , STEP 7, - , Set Order Taxes ----------," + watch.stop().elapsed(TimeUnit.MILLISECONDS));

        return detail;

    }

    private void createOrderDiscountMapping(OrderDetail detail, Order order) {
        for (OrderDiscountData orderDiscountData : order.getOrderDiscountData()) {
            PartnerOrderDiscountMapping partnerOrderDiscountMap = new PartnerOrderDiscountMapping();
            partnerOrderDiscountMap.setBrandId(detail.getBrandId());
            partnerOrderDiscountMap.setDiscountName(orderDiscountData.getDiscountName());
            partnerOrderDiscountMap.setDiscountType(orderDiscountData.getDiscountType());
            partnerOrderDiscountMap.setIsPartnerDiscount(orderDiscountData.getIsPartnerDiscount());
            if (orderDiscountData.getDiscountValue() != null) {
                partnerOrderDiscountMap.setDiscountValue(new BigDecimal(Float.toString(orderDiscountData.getDiscountValue())));
            }
            if (orderDiscountData.getDiscountAmount() != null) {
                partnerOrderDiscountMap.setDiscountAmount(new BigDecimal(Float.toString(orderDiscountData.getDiscountAmount())));
            }
            partnerOrderDiscountMap.setDiscountCategory(orderDiscountData.getDiscountCategory());
            if (orderDiscountData.getDiscountAppliedOn() != null) {
                partnerOrderDiscountMap.setDiscountAppliedOn(new BigDecimal(Float.toString(orderDiscountData.getDiscountAppliedOn())));
            }
            if (orderDiscountData.getDiscountIsTaxed() != null) {
                partnerOrderDiscountMap.setDiscountIsTaxed(orderDiscountData.getDiscountIsTaxed().toString());
            }
            partnerOrderDiscountMap.setOrderDetail(detail);
            partnerOrderDiscountMap.setVoucherCode(orderDiscountData.getVoucherCode());
            partnerOrderDiscountMap.setPartnerName(orderDiscountData.getPartnerName());
            manager.persist(partnerOrderDiscountMap);
        }

    }

    private void updatePaidEmployeeMealData(OrderDetail detail, Order order) {
        paidEmpoyeeMealDao.addPaidEmployeeMeal(detail, order);
    }

    private Integer getNextTokenNumber(int unitId, int maxLimit) {
        Query query = manager.createQuery("FROM UnitTokenSequence E where E.unitId = :unitId");
        query.setParameter("unitId", unitId);
        UnitTokenSequence sequence = null;
        try {
            sequence = (UnitTokenSequence) query.getSingleResult();
        } catch (NoResultException e) {
            sequence = addUnitTokenSequence(unitId);
        }
        int currentValue = sequence.getNextValue();
        if (currentValue >= maxLimit) {
            // reset
            sequence.setNextValue(1);
        } else {
            sequence.setNextValue(currentValue + 1);
        }
        return currentValue;
    }

    private void createEmployeeMeal(OrderDetail detail, Order order) {
        for (com.stpl.tech.kettle.domain.model.OrderItem item : order.getOrders()) {
            EmployeeMealData data = new EmployeeMealData();
            data.setBusinessDate(AppUtils.getBusinessDate());
            data.setDimension(item.getDimension());
            data.setEmployeeId(order.getEmployeeIdForMeal());
            data.setOrderDetail(detail);
            data.setProductId(item.getProductId());
            data.setProductTypeId(masterCache.getProduct(item.getProductId()).getType());
            data.setQuantity(item.getQuantity());
            data.setUnitId(order.getUnitId());
            manager.persist(data);
        }

    }

    private boolean isSubscriptionOrder(Order order) {
        return order.getSubscriptionDetail() != null && order.getSubscriptionDetail().getSubscriptionId() > 0;
    }

    private int getNextUnitOrderId(int unitId, String source) {

        Query query = manager.createQuery("FROM UnitSequenceId E where E.unitId = :unitId and dataSource = :source");
        query.setParameter("unitId", unitId);
        query.setParameter("source", source);
        UnitSequenceId sequence = null;
        try {
            sequence = (UnitSequenceId) query.getSingleResult();
        } catch (NoResultException e) {
            sequence = addUnitSequenceId(unitId, source);
        }
        int currentValue = sequence.getNextValue();
        sequence.setNextValue(currentValue + 1);
        return currentValue;
    }

    private int getNextStateInvoiceId(int stateId) {

        Query query = manager.createQuery("FROM StateSequenceId E where E.stateId = :stateId");
        query.setParameter("stateId", stateId);
        StateSequenceId sequence = null;
        try {
            sequence = (StateSequenceId) query.getSingleResult();
        } catch (NoResultException e) {
            sequence = addStateSequenceId(stateId);
        }
        int currentValue = sequence.getNextValue();
        sequence.setNextValue(currentValue + 1);
        return currentValue;
    }

    private UnitSequenceId addUnitSequenceId(int unitId, String source) {
        UnitSequenceId info = new UnitSequenceId(unitId, source, 1);
        manager.persist(info);
        return info;
    }

    private StateSequenceId addStateSequenceId(int stateId) {
        StateSequenceId info = new StateSequenceId(stateId, 1);
        manager.persist(info);
        return info;
    }

    private UnitTokenSequence addUnitTokenSequence(int unitId) {
        UnitTokenSequence info = new UnitTokenSequence(unitId, 1);
        manager.persist(info);
        return info;
    }

    private List<OrderItem> addItems(int stateId, OrderDetail order,
                                     List<com.stpl.tech.kettle.domain.model.OrderItem> items,
									 List<OrderMetadata> metadataList,
									 Map<String, OrderItemInvoice> invoices) throws DataUpdationException {
        List<OrderItem> objects = new ArrayList<OrderItem>();
		Boolean isCodOrder = TransactionUtils.isCODOrder(order.getOrderSource()) &&
				order.getChannelPartnerId() != AppConstants.BAZAAR_PARTNER_ID;

        if (items != null && items.size() > 0) {
			boolean suggestWalletGiftCard = false;
			for (OrderMetadata metadata : metadataList) {
				if (metadata.getAttributeName().equals(AppConstants.WALLET_TYPE) && metadata.getAttributeValue().equals(AppConstants.SUGGEST_WALLET)) {
					suggestWalletGiftCard = true;
					break;
				}
			}
            for (com.stpl.tech.kettle.domain.model.OrderItem item : items) {
				//here4
                OrderItem parent = addItem(order, item, false, null,suggestWalletGiftCard);
                setTaxDetail(order.getOrderId(), stateId, parent, item, invoices);
                parent.setOrderItemInvoice(invoices.get(item.getCode()));
                objects.add(parent);

                if (item.getComposition() != null && item.getComposition().getMenuProducts() != null
                        && item.getComposition().getMenuProducts().size() > 0) {
                    BigDecimal parentOriginalPrice=new BigDecimal(0);
                    for (com.stpl.tech.kettle.domain.model.OrderItem menuItem : item.getComposition()
                            .getMenuProducts()) {
						//here4
                        OrderItem child = addItem(order, menuItem, true, parent,suggestWalletGiftCard);
						setDispenserShotsInfo(menuItem, child, order);
                        setTaxDetail(order.getOrderId(), stateId, child, menuItem, invoices);
                        child.setOrderItemInvoice(invoices.get(menuItem.getCode()));
                        objects.add(child);
                        parentOriginalPrice =  AppUtils.add(parentOriginalPrice,menuItem.getOriginalPrice()!=null?menuItem.getOriginalPrice():menuItem.getPrice());
                    }
                    setParentOriginalPriceCombo(parent.getOrderItemId(),parentOriginalPrice);
                }
				addOrderItemMetaDataDetails(item,parent.getOrderItemId(),order.getOrderId(),order.getCustomerId(),isCodOrder);
				setDispenserShotsInfo(item, parent, order);
            }
        }
        return objects;
    }

	private void setDispenserShotsInfo(com.stpl.tech.kettle.domain.model.OrderItem itemDto, OrderItem itemData, OrderDetail order) {
		try {
			if (itemDto.getProductType() == 5) {
				Unit unit = masterCache.getUnit(order.getUnitId());
				if (AppUtils.getStatus(unit.getIsDispenserEnabled())) {
					String consumptionHelperProfile = Objects.nonNull(itemDto.getMilkVariant()) ?
							AppUtils.getMilkVariantPaidAddonPrefix(itemDto.getMilkVariant().getProductName()) + itemData.getRecipeProfile() : itemData.getRecipeProfile();
					Map<Integer, DispenserCanisterItemDataDto> dispenserCanisterItemDataMap = masterCache.getDispenserCanisterItemDataMap();

					// 4th Iteration
					List<OrderItemDispenserShotsData> orderItemDispenserShotsDataList = new ArrayList<>();
					String pattiText = PattiSugarHelper.getPattiText(itemDto, consumptionHelperProfile);
					String pattiType = pattiText.equalsIgnoreCase(AppConstants.K) ? DesiChaiConsumptionHelper.KADAK : DesiChaiConsumptionHelper.REGULAR;
					DispenserRecipeVariantKey pattiKey = new DispenserRecipeVariantKey(itemDto.getRecipeId(), consumptionHelperProfile,
							DesiChaiConsumptionHelper.PATTI, pattiType);
					ConsumptionCodeData pattiConsumptionCode = orderInfoCache.getDispenserConsumptionCodeData(pattiKey);
					String sugarText = PattiSugarHelper.getSugarText(itemDto, consumptionHelperProfile);
					String sugarType = sugarText.equalsIgnoreCase(AppConstants.NS) ? DesiChaiConsumptionHelper.NO_SUGAR : DesiChaiConsumptionHelper.REGULAR;
					DispenserRecipeVariantKey sugarKey = new DispenserRecipeVariantKey(itemDto.getRecipeId(), consumptionHelperProfile,
							DesiChaiConsumptionHelper.SUGAR, sugarType);
					ConsumptionCodeData sugarConsumptionCode = orderInfoCache.getDispenserConsumptionCodeData(sugarKey);
					OrderItemDispenserShotsData pattiOrderItemDispenserShotsData = null;
					if (Objects.nonNull(pattiConsumptionCode)) {
						pattiOrderItemDispenserShotsData = addOrderItemVariantDispenserShotsData(itemData, dispenserCanisterItemDataMap,
								orderItemDispenserShotsDataList, pattiType, pattiConsumptionCode, PattiSugarType.PATTI, itemDto, getShortCode(pattiText, false));
					} else {
						LOG.info("No Patti Consumption Code found for Order Item Id {} and Patti Text is : {} and Patti Type is : {}",
								itemData.getOrderItemId(), pattiText, pattiType);
						OrderItemDispenserShotsData orderItemDispenserShotsData = new OrderItemDispenserShotsData();
						orderItemDispenserShotsData.setOrderItemId(itemData);
						orderItemDispenserShotsData.setCanisterItemId(PattiSugarType.PATTI.getCanisterItemId());
						orderItemDispenserShotsData.setVariantType(pattiType);
						orderItemDispenserShotsData.setItemRemarks("No Patti Consumption Code found . Patti Text is :  " + pattiText + " and Patti Type is : " + pattiType);
						orderItemDispenserShotsData.setUsedDispenser(AppConstants.NO);
						orderItemDispenserShotsDataList.add(orderItemDispenserShotsData);
						orderSearchDao.add(orderItemDispenserShotsData);
					}
					OrderItemDispenserShotsData sugarOrderItemDispenserShotsData = null;
					if (Objects.nonNull(sugarConsumptionCode)) {
						sugarOrderItemDispenserShotsData = addOrderItemVariantDispenserShotsData(itemData, dispenserCanisterItemDataMap,
								orderItemDispenserShotsDataList, sugarType, sugarConsumptionCode, PattiSugarType.SUGAR, itemDto, getShortCode(sugarText, true));
					} else {
						LOG.info("No Sugar Consumption Code found for Order Item Id {} and Sugar Text is : {} and Sugar Type is : {}",
								itemData.getOrderItemId(), sugarText, sugarType);
						OrderItemDispenserShotsData orderItemDispenserShotsData = new OrderItemDispenserShotsData();
						orderItemDispenserShotsData.setOrderItemId(itemData);
						orderItemDispenserShotsData.setCanisterItemId(PattiSugarType.SUGAR.getCanisterItemId());
						orderItemDispenserShotsData.setVariantType(pattiType);
						orderItemDispenserShotsData.setItemRemarks("No Sugar Consumption Code found . Sugar Text is :  " + sugarText + " and Sugar Type is : " + sugarType);
						orderItemDispenserShotsData.setUsedDispenser(AppConstants.NO);
						orderItemDispenserShotsDataList.add(orderItemDispenserShotsData);
						orderSearchDao.add(orderItemDispenserShotsData);
					}
					saveAggregatedVariantData(itemData, order, consumptionHelperProfile, pattiConsumptionCode, sugarConsumptionCode, pattiOrderItemDispenserShotsData, sugarOrderItemDispenserShotsData);
					if (Objects.isNull(pattiConsumptionCode) && Objects.isNull(sugarConsumptionCode)) {
						LOG.error("No Consumption Code found for Order Item Id {} and SugarText is : {} and sugar Type is : {} and patti text is : {} and Patti type is : {}",
								itemData.getOrderItemId(), sugarText, sugarType, pattiText, pattiType);
					}
					if (Objects.nonNull(itemDto.getComposition()) && Objects.nonNull(itemDto.getComposition().getAddons())) {
						for (IngredientProductDetail addon : itemDto.getComposition().getAddons()) {
							if (Objects.nonNull(dispenserCanisterItemDataMap) && Objects.nonNull(dispenserCanisterItemDataMap.get(addon.getProduct().getProductId()))) {
								DispenserCanisterItemDataDto dispenserCanisterItemDataDto = dispenserCanisterItemDataMap.get(addon.getProduct().getProductId());
								if (Objects.nonNull(dispenserCanisterItemDataDto)) {
									OrderItemDispenserShotsData orderItemDispenserShotsData = new OrderItemDispenserShotsData();
									orderItemDispenserShotsData.setOrderItemId(itemData);
									Product product = masterCache.getProduct(addon.getProduct().getProductId());
									if (Objects.nonNull(product)) {
										orderItemDispenserShotsData.setShortCode(product.getShortCode());
										orderItemDispenserShotsData.setProductName(product.getName());
									}
									orderItemDispenserShotsData.setCanisterItemId(dispenserCanisterItemDataDto.getDispenserCanisterItemDataId());
									orderItemDispenserShotsData.setCanisterStatus(dispenserCanisterItemDataDto.getCanisterStatus());
									setShotsAndGramsBasedOnDimension(dispenserCanisterItemDataDto, addon, itemDto, orderItemDispenserShotsData, itemData);
									orderItemDispenserShotsDataList.add(orderItemDispenserShotsData);
								}
							}
						}
					}
					if (!CollectionUtils.isEmpty(orderItemDispenserShotsDataList)) {
						orderSearchDao.addAll(orderItemDispenserShotsDataList);
						itemData.getOrderItemDispenserShotsDataSet().addAll(orderItemDispenserShotsDataList);
					}
				}
			}
		} catch (Exception e) {
			LOG.error("Error while setting Patti Sugar Dispenser Shots Info for Order Item Id {}", itemData.getOrderItemId(), e);
		}
	}

	private static String getShortCode(String text, boolean isSugar) {
		if (isSugar) {
			if (text.equalsIgnoreCase(AppConstants.R)) {
				return AppConstants.RS;
			} else {
				return AppConstants.NS;
			}
		} else {
			if (text.equalsIgnoreCase(AppConstants.R)) {
				return AppConstants.RP;
			} else {
				return AppConstants.K;
			}
		}
	}

	private static void setShotsAndGramsBasedOnDimension(DispenserCanisterItemDataDto dispenserCanisterItemDataDto, IngredientProductDetail i,
														 com.stpl.tech.kettle.domain.model.OrderItem orderItem, OrderItemDispenserShotsData orderItemDispenserShotsData, OrderItem itemData) {
		orderItemDispenserShotsData.setPerShotQuantity(dispenserCanisterItemDataDto.getPerShotQuantity());
		if (DesiChaiConsumptionHelper.REGULAR.equalsIgnoreCase(orderItem.getDimension())) {
			orderItemDispenserShotsData.setQuantityInGramsPerSachet(dispenserCanisterItemDataDto.getRegularGrams());
			orderItemDispenserShotsData.setTotalShots(AppUtils.multiplyWithScale10(AppUtils.multiplyWithScale10(i.getQuantity(),
					AppUtils.divideWithScale10(dispenserCanisterItemDataDto.getRegularGrams(), dispenserCanisterItemDataDto.getPerShotQuantity())), BigDecimal.valueOf(orderItem.getQuantity())));
			orderItemDispenserShotsData.setTotalQuantityToBeDispensed(AppUtils.multiplyWithScale10(AppUtils.multiplyWithScale10(i.getQuantity(),
					dispenserCanisterItemDataDto.getRegularGrams()), BigDecimal.valueOf(orderItem.getQuantity())));
		} else if (DesiChaiConsumptionHelper.MINI_KETLI.equalsIgnoreCase(orderItem.getDimension())) {
			orderItemDispenserShotsData.setQuantityInGramsPerSachet(dispenserCanisterItemDataDto.getMiniKetliGrams());
			orderItemDispenserShotsData.setTotalShots(AppUtils.multiplyWithScale10(AppUtils.multiplyWithScale10(i.getQuantity(),
					AppUtils.divideWithScale10(dispenserCanisterItemDataDto.getMiniKetliGrams(), dispenserCanisterItemDataDto.getPerShotQuantity())), BigDecimal.valueOf(orderItem.getQuantity())));
			orderItemDispenserShotsData.setTotalQuantityToBeDispensed(AppUtils.multiplyWithScale10(AppUtils.multiplyWithScale10(i.getQuantity(),
					dispenserCanisterItemDataDto.getMiniKetliGrams()), BigDecimal.valueOf(orderItem.getQuantity())));
		}  else if (DesiChaiConsumptionHelper.CHOTI_KETLI.equalsIgnoreCase(orderItem.getDimension())) {
			orderItemDispenserShotsData.setQuantityInGramsPerSachet(dispenserCanisterItemDataDto.getChotiKetliGrams());
			orderItemDispenserShotsData.setTotalShots(AppUtils.multiplyWithScale10(AppUtils.multiplyWithScale10(i.getQuantity(),
					AppUtils.divideWithScale10(dispenserCanisterItemDataDto.getChotiKetliGrams(), dispenserCanisterItemDataDto.getPerShotQuantity())), BigDecimal.valueOf(orderItem.getQuantity())));
			orderItemDispenserShotsData.setTotalQuantityToBeDispensed(AppUtils.multiplyWithScale10(AppUtils.multiplyWithScale10(i.getQuantity(),
					dispenserCanisterItemDataDto.getChotiKetliGrams()), BigDecimal.valueOf(orderItem.getQuantity())));
		}  else if (DesiChaiConsumptionHelper.BADI_KETLI.equalsIgnoreCase(orderItem.getDimension())) {
			orderItemDispenserShotsData.setQuantityInGramsPerSachet(dispenserCanisterItemDataDto.getBadiKetliGrams());
			orderItemDispenserShotsData.setTotalShots(AppUtils.multiplyWithScale10(AppUtils.multiplyWithScale10(i.getQuantity(),
					AppUtils.divideWithScale10(dispenserCanisterItemDataDto.getBadiKetliGrams(), dispenserCanisterItemDataDto.getPerShotQuantity())), BigDecimal.valueOf(orderItem.getQuantity())));
			orderItemDispenserShotsData.setTotalQuantityToBeDispensed(AppUtils.multiplyWithScale10(AppUtils.multiplyWithScale10(i.getQuantity(),
					dispenserCanisterItemDataDto.getBadiKetliGrams()), BigDecimal.valueOf(orderItem.getQuantity())));
		} else if (DesiChaiConsumptionHelper.TEA_FOR_2.equalsIgnoreCase(orderItem.getDimension())) {
			orderItemDispenserShotsData.setQuantityInGramsPerSachet(dispenserCanisterItemDataDto.getTeaForTwoGrams());
			orderItemDispenserShotsData.setTotalShots(AppUtils.multiplyWithScale10(AppUtils.multiplyWithScale10(i.getQuantity(),
					AppUtils.divideWithScale10(dispenserCanisterItemDataDto.getTeaForTwoGrams(), dispenserCanisterItemDataDto.getPerShotQuantity())), BigDecimal.valueOf(orderItem.getQuantity())));
			orderItemDispenserShotsData.setTotalQuantityToBeDispensed(AppUtils.multiplyWithScale10(AppUtils.multiplyWithScale10(i.getQuantity(),
					dispenserCanisterItemDataDto.getTeaForTwoGrams()), BigDecimal.valueOf(orderItem.getQuantity())));
		} else if (DesiChaiConsumptionHelper.TEA_FOR_4.equalsIgnoreCase(orderItem.getDimension())) {
			orderItemDispenserShotsData.setQuantityInGramsPerSachet(dispenserCanisterItemDataDto.getTeaForFourGrams());
			orderItemDispenserShotsData.setTotalShots(AppUtils.multiplyWithScale10(AppUtils.multiplyWithScale10(i.getQuantity(),
					AppUtils.divideWithScale10(dispenserCanisterItemDataDto.getTeaForFourGrams(), dispenserCanisterItemDataDto.getPerShotQuantity())), BigDecimal.valueOf(orderItem.getQuantity())));
			orderItemDispenserShotsData.setTotalQuantityToBeDispensed(AppUtils.multiplyWithScale10(AppUtils.multiplyWithScale10(i.getQuantity(),
					dispenserCanisterItemDataDto.getTeaForFourGrams()), BigDecimal.valueOf(orderItem.getQuantity())));
		}
		if (Objects.isNull(orderItemDispenserShotsData.getQuantityInGramsPerSachet())) {
			orderItemDispenserShotsData.setItemRemarks("Grams Info Not Found ");
			LOG.info("Grams Info Not Found for Order Item Id {} for Product Id : {} and Product Name : {}", itemData.getOrderItemId(), i.getProduct().getProductId(), i.getProduct().getName());
		} else {
			orderItemDispenserShotsData.setUsedDispenser(AppConstants.YES);
		}
	}

	private OrderItemDispenserShotsData addOrderItemVariantDispenserShotsData(OrderItem itemData, Map<Integer, DispenserCanisterItemDataDto> variantMap,
																			  List<OrderItemDispenserShotsData> orderItemDispenserShotsDataList, String variantType,
																			  ConsumptionCodeData pattiConsumptionCode, PattiSugarType pattiSugarType, com.stpl.tech.kettle.domain.model.OrderItem itemDto, String shortCode) {
		StringBuilder itemRemarks = new StringBuilder();
		OrderItemDispenserShotsData orderItemDispenserShotsData = new OrderItemDispenserShotsData();
		orderItemDispenserShotsData.setOrderItemId(itemData);
		orderItemDispenserShotsData.setVariantType(variantType);
		orderItemDispenserShotsData.setShortCode(shortCode);
		orderItemDispenserShotsData.setCanisterItemId(variantMap.get(pattiSugarType.getDefaultProductId()).getDispenserCanisterItemDataId());
		orderItemDispenserShotsData.setCanisterStatus(variantMap.get(pattiSugarType.getDefaultProductId()).getCanisterStatus());
		orderItemDispenserShotsData.setProductId(pattiConsumptionCode.getId());
		Map<Integer, DispenserPattiSugarShotInfoDataDto> dispenserShotsInfo = variantMap.get(pattiSugarType.getDefaultProductId())
				.getDispenserPattiSugarShotInfoDataDtoMap();
		if (Objects.nonNull(dispenserShotsInfo)) {
			DispenserPattiSugarShotInfoDataDto pattiSugarShotInfoDataDto = dispenserShotsInfo.get(pattiConsumptionCode.getId());
			if (Objects.nonNull(pattiSugarShotInfoDataDto)) {
				orderItemDispenserShotsData.setProductName(pattiSugarShotInfoDataDto.getPattiSugarType().name());
				orderItemDispenserShotsData.setQuantityInGramsPerSachet(pattiSugarShotInfoDataDto.getQuantityInGrams());
				orderItemDispenserShotsData.setPerShotQuantity(pattiSugarShotInfoDataDto.getPerShotQuantity());
				orderItemDispenserShotsData.setTotalShots(AppUtils.multiplyWithScale10(AppUtils.divideWithScale10(pattiSugarShotInfoDataDto.getQuantityInGrams(),
						pattiSugarShotInfoDataDto.getPerShotQuantity()), BigDecimal.valueOf(itemData.getQuantity())));
				orderItemDispenserShotsData.setTotalQuantityToBeDispensed(AppUtils.multiplyWithScale10(AppUtils.multiplyWithScale10(pattiSugarShotInfoDataDto.getQuantityInGrams(),
						pattiConsumptionCode.getQty()) , BigDecimal.valueOf(itemData.getQuantity())));
				orderItemDispenserShotsData.setUsedDispenser(AppConstants.YES);
			} else {
				itemRemarks.append(pattiSugarType.name()).append(" Shots Info not found In Cache for Product Id").append(pattiConsumptionCode.getId()).append(" ");
			}
		}
		if (!StringUtils.isEmpty(itemRemarks)) {
			orderItemDispenserShotsData.setItemRemarks(itemRemarks.toString());
		}
		orderItemDispenserShotsDataList.add(orderItemDispenserShotsData);
		return orderItemDispenserShotsData;
	}

	private void saveAggregatedVariantData(OrderItem itemData,
										   OrderDetail order, String recipeProfile, ConsumptionCodeData pattiConsumptionCode,
										   ConsumptionCodeData sugarConsumptionCode, OrderItemDispenserShotsData pattiOrderItemDispenserShotsData,
										   OrderItemDispenserShotsData sugarOrderItemDispenserShotsData) {
		try {
			Unit unit = masterCache.getUnit(order.getUnitId());
			List<VariantDispenserShotsAggregatedData> aggregatedDataList = orderSearchDao.findAggregatedVariantData(itemData.getProductId(), itemData.getDimension(), recipeProfile, unit);
			if (!CollectionUtils.isEmpty(aggregatedDataList)) {
				VariantDispenserShotsAggregatedData aggregatedData = aggregatedDataList.get(0);
				aggregatedData.setPattiShots(AppUtils.add(aggregatedData.getPattiShots(), Objects.nonNull(pattiOrderItemDispenserShotsData) ?
						AppUtils.convertvalueToBigDecimal(pattiOrderItemDispenserShotsData.getTotalShots()) : BigDecimal.ZERO));
				aggregatedData.setSugarShots(AppUtils.add(aggregatedData.getSugarShots(), Objects.nonNull(sugarOrderItemDispenserShotsData) ?
						AppUtils.convertvalueToBigDecimal(sugarOrderItemDispenserShotsData.getTotalShots()) : BigDecimal.ZERO));
				aggregatedData.setPattiQuantity(AppUtils.add(aggregatedData.getPattiQuantity(), Objects.nonNull(pattiOrderItemDispenserShotsData) ?
						AppUtils.convertvalueToBigDecimal(pattiOrderItemDispenserShotsData.getTotalQuantityToBeDispensed()) : BigDecimal.ZERO));
				aggregatedData.setSugarQuantity(AppUtils.add(aggregatedData.getSugarQuantity(), Objects.nonNull(sugarOrderItemDispenserShotsData) ?
						AppUtils.convertvalueToBigDecimal(sugarOrderItemDispenserShotsData.getTotalQuantityToBeDispensed()) : BigDecimal.ZERO));
				aggregatedData.setPattiId(Objects.nonNull(pattiConsumptionCode) ? pattiConsumptionCode.getId() : null);
				aggregatedData.setSugarId(Objects.nonNull(sugarConsumptionCode) ? sugarConsumptionCode.getId() : null);
				Integer ordersCount = aggregatedData.getOrdersCount();
				if (ordersCount == null) {
					ordersCount = 0;
				}
				aggregatedData.setOrdersCount(ordersCount + itemData.getQuantity());
				orderSearchDao.update(aggregatedData);
			} else {
				VariantDispenserShotsAggregatedData aggregatedData = new VariantDispenserShotsAggregatedData();
				aggregatedData.setProductId(itemData.getProductId());
				aggregatedData.setRegion(unit.getRegion());
				aggregatedData.setCity(unit.getLocation().getName());
				aggregatedData.setRecipeProfile(recipeProfile);
				aggregatedData.setDimension(itemData.getDimension());
				aggregatedData.setPattiShots(AppUtils.add(aggregatedData.getPattiShots(), Objects.nonNull(pattiOrderItemDispenserShotsData) ?
						AppUtils.convertvalueToBigDecimal(pattiOrderItemDispenserShotsData.getTotalShots()) : BigDecimal.ZERO));
				aggregatedData.setSugarShots(AppUtils.add(aggregatedData.getSugarShots(), Objects.nonNull(sugarOrderItemDispenserShotsData) ?
						AppUtils.convertvalueToBigDecimal(sugarOrderItemDispenserShotsData.getTotalShots()) : BigDecimal.ZERO));
				aggregatedData.setPattiQuantity(AppUtils.add(aggregatedData.getPattiQuantity(), Objects.nonNull(pattiOrderItemDispenserShotsData) ?
						AppUtils.convertvalueToBigDecimal(pattiOrderItemDispenserShotsData.getTotalQuantityToBeDispensed()) : BigDecimal.ZERO));
				aggregatedData.setSugarQuantity(AppUtils.add(aggregatedData.getSugarQuantity(), Objects.nonNull(sugarOrderItemDispenserShotsData) ?
						AppUtils.convertvalueToBigDecimal(sugarOrderItemDispenserShotsData.getTotalQuantityToBeDispensed()) : BigDecimal.ZERO));
				aggregatedData.setPattiId(Objects.nonNull(pattiConsumptionCode) ? pattiConsumptionCode.getId() : null);
				aggregatedData.setSugarId(Objects.nonNull(sugarConsumptionCode) ? sugarConsumptionCode.getId() : null);
				aggregatedData.setOrdersCount(itemData.getQuantity());
				orderSearchDao.add(aggregatedData);
			}
		} catch (Exception e) {
			LOG.error("Error while saving Aggregated Variant Data for Order Item Id {}", itemData.getOrderItemId());
		}
	}

	private void setParentOriginalPriceCombo(Integer orderItemId, BigDecimal parentOriginalPrice) {
        try {
            OrderItem item = manager.find(OrderItem.class, orderItemId);
            item.setOriginalPrice(parentOriginalPrice);
            manager.persist(item);
            manager.flush();
        }catch (Exception e){
            LOG.error("Error while updating the original price for parent Order Item Id {}",orderItemId);
        }
    }

    private OrderItem addItem(OrderDetail order, com.stpl.tech.kettle.domain.model.OrderItem item,
                              boolean isComboConsituent, OrderItem parent,boolean suggestWalletGiftCard) throws DataUpdationException {
        OrderItem info = new OrderItem();
        setData(order, info, item, isComboConsituent, parent);
        manager.persist(info);
        if (AppUtils.isGiftCard(item.getCode())) {
            item.setVoucherCode(item.getItemCode());
			//here2
            updateCashCardDetails(order, item,suggestWalletGiftCard);
            voucherManagementService.updateVoucher(item.getVoucherCode(), item.getItemCode(), item.getCardType());
        }
        addAddons(info, item.getComposition());
        if(Objects.nonNull(item.getHasPreference()) && item.getHasPreference() && Objects.nonNull(item.getPreferenceDetail())){
            addPreferenceToOrderItemAddon(info , item);
        }
        return info;
    }

    private void updateCashCardDetails(OrderDetail orderDetail, com.stpl.tech.kettle.domain.model.OrderItem item,boolean suggestWalletGiftCard)
            throws DataUpdationException {
//		LOG.info("Item is qwertyuio {}",item);
        CashCardOffer offer = cashCardDao.getCashCardOffer(item.getPrice(), orderDetail.getUnitId(),orderDetail.getChannelPartnerId());
        if (((item.getCardType().equalsIgnoreCase(CashCardType.ECARD.name()) || item.getCardType().equalsIgnoreCase(CashCardType.AP01.name()) || item.getCardType().equalsIgnoreCase(CashCardType.MICRO.name())) && item.getItemCode() == null)
                || voucherManagementService.isGyftrCard(item.getCardType())) {
            String cardNumber = cashCardDao.getUniqueCashCardNumber();
            item.setItemCode(cardNumber);
            addNewCardEntry(cardNumber, item);
        }
        CashCardDetail cashCardDetail = getCashCardDetailByCode(item.getItemCode());
        cashCardDetail.setBuyerId(orderDetail.getCustomerId());
        cashCardDetail.setCardStatus(CashCardStatus.READY_FOR_ACTIVATION.name());
        cashCardDetail.setPurchaseTime(AppUtils.getCurrentTimestamp());
        cashCardDetail.setLastModified(AppUtils.getCurrentTimestamp());
        cashCardDetail.setPurchaseOrderId(orderDetail.getOrderId());
        if(CashCardType.AP01.name().equalsIgnoreCase(item.getCardType())){
			BigDecimal offerAmount = BigDecimal.ZERO;
//			cashCardDetail.setCashPendingAmount(AppUtils.add(cashCardDetail.getCashInitialAmount(), offerAmount));
			cashCardDetail.setInitialOffer(offerAmount);
		}
		if (item.getProductId() == AppConstants.PSEUDO_GIFT_CART_ID || (CashCardType.ECARD.name().equals(item.getCardType()) && Objects.nonNull(orderDetail.getChannelPartnerId()) && AppUtils.isAppOrder(orderDetail.getChannelPartnerId())))  {
			BigDecimal offerAmount = item.getOfferAmount();
			cashCardDetail.setCashPendingAmount(AppUtils.add(cashCardDetail.getCashInitialAmount(), offerAmount));
			cashCardDetail.setInitialOffer(offerAmount);
		}
		else if(item.getProductId() == AppConstants.MICRO_WALLET_ID && Objects.nonNull(orderDetail.getChannelPartnerId()) && (orderDetail.getChannelPartnerId()==1 || orderDetail.getChannelPartnerId()== AppConstants.CHANNEL_PARTNER_WEB_APP)) {
			BigDecimal offerAmount = item.getOfferAmount();
			cashCardDetail.setCashPendingAmount(AppUtils.add(cashCardDetail.getCashInitialAmount(), offerAmount));
			cashCardDetail.setInitialOffer(offerAmount);
		}
		else if (item.getProductId() == AppConstants.PSEUDO_GIFT_CART_ID || (CashCardType.ECARD.name().equals(item.getCardType()) && offer == null))  {
			BigDecimal offerAmount = item.getOfferAmount();
            cashCardDetail.setCashPendingAmount(AppUtils.add(cashCardDetail.getCashInitialAmount(), offerAmount));
            cashCardDetail.setInitialOffer(offerAmount);
        } else {
			if (!CashCardType.GYFTR.name().equals(item.getCardType()) && !CashCardType.AP01.name().equalsIgnoreCase(item.getCardType()) && !CashCardType.MICRO.name().equalsIgnoreCase(item.getCardType()) && offer != null) {
				BigDecimal giftWalletOffer = Objects.nonNull(offer.getPercentage()) ? offer.getPercentage() : BigDecimal.ZERO;
				BigDecimal suggestWalletOffer = Objects.nonNull(offer.getSuggestWalletPercentage()) ? offer.getSuggestWalletPercentage() : BigDecimal.ZERO;
				BigDecimal offerAmount = suggestWalletGiftCard ? AppUtils.percentOf(suggestWalletOffer, cashCardDetail.getCashInitialAmount()) : AppUtils.percentOf(giftWalletOffer, cashCardDetail.getCashInitialAmount());
				cashCardDetail.setCashPendingAmount(AppUtils.add(cashCardDetail.getCashInitialAmount(), offerAmount));
                cashCardDetail.setOfferId(offer.getCashCardOfferId());
                cashCardDetail.setInitialOffer(offerAmount);
            } else {
                cashCardDetail.setCashPendingAmount(cashCardDetail.getCashInitialAmount());
                cashCardDetail.setInitialOffer(BigDecimal.ZERO);
            }
        }
        manager.merge(cashCardDetail);

        if (!item.getCardType().equalsIgnoreCase(CashCardType.GIFT.name())) {
            cashCardDao.activateCashCard(cashCardDetail, orderDetail);
        }

    }

    private void addNewCardEntry(String cardNumber, com.stpl.tech.kettle.domain.model.OrderItem item) {
        try {
            CashCardDetail detail = new CashCardDetail();
            detail.setCardNumber(cardNumber);
            detail.setCardSerial(cardNumber);
            detail.setSerialNumber(cardNumber);
            detail.setCreationTime(AppUtils.getCurrentTimestamp());
            detail.setCardStatus(CashCardStatus.INITIATED.name());
            detail.setStartDate(AppUtils.getCurrentDate());
            if (CashCardType.GYFTR.name().equals(item.getCardType())) {
                detail.setEndDate(item.getValidUpto());
            } else {
                detail.setEndDate(AppUtils.getInfiniteDate());
            }
			if(item.getCardType().equalsIgnoreCase(CashCardType.AP01.name())){
				detail.setCashInitialAmount(item.getTotalAmount());
			}
			else if(item.getCardType().equalsIgnoreCase(CashCardType.MICRO.name())){
				detail.setCashInitialAmount(item.getTotalAmount());
			}
			else{
            	detail.setCashInitialAmount(item.getPrice());
			}
            detail.setCashPendingAmount(BigDecimal.ZERO);
            detail.setCardType(item.getCardType());
            cashCardDao.add(detail);
        } catch (Exception e) {
            LOG.info("Error while creating system generated E-card for");
        }
    }


    private CashCardDetail getCashCardDetailByCode(String cardNumber) {
        Query query = manager.createQuery("FROM CashCardDetail c WHERE c.cardNumber = :cardNumber");
        query.setParameter("cardNumber", cardNumber);
        return (CashCardDetail) query.getSingleResult();
    }

    private void setData(OrderDetail order, OrderItem info, com.stpl.tech.kettle.domain.model.OrderItem item,
                         boolean isComboConsituent, OrderItem parent) {
        int productId = item.getProductId();
        String productName = item.getProductName();
        if (DesiChaiConsumptionHelper.isPlaceholderProduct(productId)) {
            Integer pid = getMappedVariant(item);
            productId = pid == null ? productId : pid;
            productName = masterCache.getProductBasicDetail(productId).getDetail().getName();
        }
        setComplimentaryDetails(info, item, isComboConsituent);
		info.setOrderItemRemark(item.getOrderItemRemark());
        info.setBillType(item.getBillType() == null ? null : item.getBillType().name());
        info.setTaxCode(item.getCode());
        info.setDimension(item.getDimension());
        if (item.getDiscountDetail() != null) {
            setDiscount(info, item);
            info.setDiscountReason(item.getDiscountDetail().getDiscountReason());
            info.setDiscountReasonId(item.getDiscountDetail().getDiscountCode());
            if (item.getDiscountDetail().getPromotionalOffer() != null) {
                info.setPromotionalDiscount(item.getDiscountDetail().getPromotionalOffer());
            } else {
                info.setPromotionalDiscount(BigDecimal.ZERO);
            }
        }

        info.setHasAddon(AppConstants.getValue(item.getComposition() != null
                && item.getComposition().getAddons() != null && item.getComposition().getAddons().size() > 0));
        info.setPrice(item.getPrice() == null ? BigDecimal.ZERO : item.getPrice());
        info.setOriginalPrice(item.getOriginalPrice() == null ? item.getPrice() : item.getOriginalPrice());
        info.setProductId(productId);
        info.setParentItemId(parent != null ? parent.getOrderItemId() : null);
        info.setProductName(productName);
		info.setProductAliasName(Objects.nonNull(item.getProductAliasName()) ? item.getProductAliasName() : productName);
        info.setQuantity(parent != null ? item.getQuantity() * parent.getQuantity() : item.getQuantity());
        info.setTotalAmount(item.getPrice() == null ? BigDecimal.ZERO
                : item.getPrice().multiply(new BigDecimal(info.getQuantity())));
        if (isComboConsituent) {
            info.setPaidAmount(BigDecimal.ZERO);
        } else {
            info.setPaidAmount(item.getAmount() == null ? BigDecimal.ZERO : item.getAmount());
        }
        info.setComboConstituent(AppConstants.getValue(isComboConsituent));
        info.setOrderDetail(order);
        if (AppConstants.CHAAYOS_BRAND_ID == order.getBrandId()) {
            Integer recipeId = null;
            try {
                recipeId = recipeCache.getUnitProductRecipeId(order.getUnitId(), productId, item.getDimension());
            } catch (Exception e) {
                // Do Nothing
            }
            if (recipeId != null && recipeId != item.getRecipeId()) {
                info.setRecipeId(recipeId);
                item.setRecipeId(recipeId);
            } else {
                info.setRecipeId(item.getRecipeId());
            }
        } else {
            info.setRecipeId(item.getRecipeId());
        }
        String profile = null;
        try {
            profile = recipeCache.getRecipe(info.getRecipeId()).getProfile();
        } catch (Exception e) {
            profile = AppConstants.DEFAULT_RECIPE_PROFILE;
        }
        info.setRecipeProfile(profile);
        info.setTaxAmount(item.getTax());
        boolean takeAway = order.getChannelPartnerId() == 9 || (item.getTakeAway() != null && item.getTakeAway());
        info.setTakeAway(AppConstants.getValue(takeAway));
        //recom category is added here which has index, and tags,
        info.setRecomCategory(item.getRecomCategory());
        info.setTaxDeductedByPartner(AppUtils.setStatus(Boolean.TRUE.equals(item.getTaxDeductedByPartner())));

		if(Objects.isNull(item.getTaxDeductedByPartner()) && TransactionUtils.isCODOrder(order.getOrderSource())
				 && !AppUtils.isBazaarOrder(order.getChannelPartnerId()) &&
				!TransactionUtils.isMRP(item.getBillType())){
            info.setTaxDeductedByPartner(AppUtils.setStatus(Boolean.TRUE));
		}

        //Added here the category and sub category
		info.setSourceCategory(item.getSourceCategory());
		info.setSourceSubCategory(item.getSourceSubCategory());


    }

    @Override
    public Integer getMappedVariant(com.stpl.tech.kettle.domain.model.OrderItem item) {
        int identifierProduct = DesiChaiConsumptionHelper.placeholderIdentifier(item.getProductId());
        List<IngredientVariantDetail> variants = new ArrayList<>();
        Integer productId = null;
        if (item.getComposition() != null && item.getComposition().getVariants() != null) {
            for (IngredientVariantDetail v : item.getComposition().getVariants()) {

                if (identifierProduct == v.getProductId()) {
                    productId = DesiChaiConsumptionHelper.getActualProduct(item.getProductId(), v.getAlias());
                } else {
                    variants.add(v);
                }
            }
            item.getComposition().setVariants(variants);
        }
        return productId;
    }

    @Override
    public void setDiscount(OrderItem info, com.stpl.tech.kettle.domain.model.OrderItem item) {
        if (item.getDiscountDetail() == null || item.getDiscountDetail().getDiscount() == null) {
            info.setDiscountAmount(BigDecimal.ZERO);
            info.setDiscountPercent(BigDecimal.ZERO);
            info.setPromotionalDiscount(BigDecimal.ZERO);
            return;
        }
        info.setDiscountAmount(item.getDiscountDetail().getDiscount().getValue());
        info.setDiscountPercent(item.getDiscountDetail().getDiscount().getPercentage());
    }

    @Override
    public void setComplimentaryDetails(OrderItem info, com.stpl.tech.kettle.domain.model.OrderItem item,
                                        boolean isComboConsituent) {
        if (isComboConsituent) {
            setComboComplimentaryDetails(info);
            return;
        }
        if (item.getComplimentaryDetail() == null) {
            return;
        }
        info.setComplimentaryReason(item.getComplimentaryDetail().getReason());
        info.setComplimentaryTypeId(item.getComplimentaryDetail().getReasonCode());
        info.setIsComplimentary(AppConstants.getValue(item.getComplimentaryDetail().isIsComplimentary()));
    }

    private void setComboComplimentaryDetails(OrderItem info) {
        info.setComplimentaryTypeId(AppConstants.COMPLEMENTARY_CODE_COMBO);
        info.setIsComplimentary(AppConstants.YES);
    }

    private List<OrderSettlement> addSettlements(OrderDetail order, List<Settlement> items)
            throws DataUpdationException {
        List<OrderSettlement> objects = new ArrayList<OrderSettlement>();
        if (items != null && items.size() > 0) {
            for (Settlement item : items) {
                objects.add(addSettlement(order, item));
            }
        }
        return objects;
    }

    private OrderSettlement addSettlement(OrderDetail order, Settlement item) throws DataUpdationException {
        return addSettlement(order, item, null);
    }

    @Override
    public OrderSettlement addSettlement(OrderDetail order, Settlement item, OrderSettlement oldSettlement)
            throws DataUpdationException {
        OrderSettlement orderSettlement = null;

        if (oldSettlement != null) {
            orderSettlement = oldSettlement;
        } else {
            orderSettlement = new OrderSettlement();
        }

        setData(order, orderSettlement, item);
        manager.persist(orderSettlement);
        if (item.getExternalSettlements() != null && item.getExternalSettlements().size() > 0) {
            List<OrderExternalSettlementData> settlements = new ArrayList<>();
            for (ExternalSettlement externalSettlement : item.getExternalSettlements()) {
                OrderExternalSettlementData externalSettlementData = new OrderExternalSettlementData(orderSettlement,
                        externalSettlement.getAmount(), externalSettlement.getExternalTransactionId());
                manager.persist(externalSettlementData);
                settlements.add(externalSettlementData);
            }
            orderSettlement.getExternalTransactions().addAll(settlements);
        }
        manager.flush();
        for (OrderPaymentDenomination denomination : item.getDenominations()) {
            orderSettlement.getDenominations().add(addOrderPaymentDenominations(order, denomination, orderSettlement));
        }
        if (item.getMode() == TransactionConstants.PAYMENT_MODE_CASH_CARD) {
            if (item.getExternalSettlements() == null || item.getExternalSettlements().size() == 0) {
                throw new DataUpdationException("Settlement type gift card should have a transaction id");
            } else if (order.getCustomerId() <= 5) {
                throw new DataUpdationException("Card Can Only Be settled for a valid customer");
            } else {
                try {
                    validateSettlement(orderSettlement, item.getExternalSettlements());
                    for (ExternalSettlement settlement : item.getExternalSettlements()) {
                        cashCardDao.createCardEvent(Integer.valueOf(settlement.getExternalTransactionId()),
                                order.getCustomerId(), order.getOrderId(), orderSettlement.getSettlementId(),
                                settlement.getAmount());
                        CashCardDetail cashCardDetail = cashCardDao.find(CashCardDetail.class,
                                Integer.valueOf(settlement.getExternalTransactionId()));
                        cashCardDetail.setLastModified(AppUtils.getCurrentTimestamp());
                        cashCardDao.update(cashCardDetail);
                    }
                } catch (NumberFormatException | CardValidationException e) {
                    throw new DataUpdationException(e.getMessage());
                }
            }
        }
        return orderSettlement;
    }

    @Override
    public OrderDetail getOrderByExternalOrderId(String externalOrderId) {
        try {
            Query query = manager.createQuery("FROM OrderDetail c WHERE c.generatedOrderId = :generatedOrderId");
            query.setParameter("generatedOrderId", externalOrderId);
            Object orderObject = query.getSingleResult();
            if (orderObject != null) {
                OrderDetail orderDetail = (OrderDetail) orderObject;
                return orderDetail;
            }
        } catch (NoResultException nre) {
            LOG.info("No Order details found by external order Id", externalOrderId);
        } catch (Exception ex) {
            LOG.error("Exception Occurred while fetching order details", ex);
        }
        return null;
    }

	private OrderDetail getOrderByPartnerOrderId(String externalOrderId) {
        try {
            Query query = manager.createQuery("FROM OrderDetail c WHERE c.orderSourceId = :orderSourceId and " +
					                          "c.orderStatus <> :orderStatus");
            query.setParameter("orderSourceId", externalOrderId);
			query.setParameter("orderStatus",OrderStatus.CANCELLED.name());
            Object orderObject = query.getSingleResult();
            if (orderObject != null) {
                OrderDetail orderDetail = (OrderDetail) orderObject;
                return orderDetail;
            }
        } catch (NoResultException nre) {
            LOG.info("No Order details found by external order Id", externalOrderId);
        } catch (Exception ex) {
            LOG.error("Exception Occurred while fetching order details", ex);
        }
        return null;
    }

    private void validateSettlement(OrderSettlement orderSettlement, List<ExternalSettlement> externalSettlements)
            throws CardValidationException {
        BigDecimal settlementAmount = orderSettlement.getAmountPaid();
        BigDecimal cardSettlementsAmount = externalSettlements.stream().map(value -> value.getAmount())
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        if (!AppUtils.isEqual(settlementAmount, cardSettlementsAmount)) {
            throw new CardValidationException("The sum of card amount does not add up to Total Settlement Amount");
        }
    }

    private OrderPaymentDenominationDetail addOrderPaymentDenominations(OrderDetail order,
                                                                        OrderPaymentDenomination denomination, OrderSettlement orderSettlement) {
        OrderPaymentDenominationDetail detail = new OrderPaymentDenominationDetail();
        detail.setOrderId(order.getOrderId());
        detail.setDenominationId(denomination.getDenominationDetailId());
        detail.setOrderSettlement(orderSettlement);
        detail.setCount(denomination.getCount());
        detail.setTotalAmount(denomination.getTotalAmount());
        manager.persist(detail);
        return detail;
    }

    private void setData(OrderDetail order, OrderSettlement info, Settlement item) {
        info.setAmountPaid(item.getAmount());
        info.setPaymentModeId(item.getMode());
        BigDecimal vouchers = item.getExtraVouchers() == null ? BigDecimal.ZERO : item.getExtraVouchers();
        info.setExtraVouchers(vouchers);
        info.setOrderDetail(order);
    }

    private void addAddons(OrderItem order, OrderItemComposition composition) {
        Stopwatch watch = Stopwatch.createUnstarted();
        watch.start();
        order.getOrderItemAddons().addAll(addAddon(order, composition));
        order.getOrderItemAddons().addAll(addMandatoryAddon(order));
        System.out.println("&&&&&&&&&& , STEP 2.1, - , Add Order Item Addons----------,"
                + watch.stop().elapsed(TimeUnit.MILLISECONDS));
    }

    private List<OrderItemAddon> addMandatoryAddon(OrderItem order) {
        List<OrderItemAddon> list = new ArrayList<>();
        Collection<IngredientProductDetail> mandatoryAddons = recipeCache.getMandatoryAddons(order.getProductId(),
                order.getDimension(), order.getRecipeProfile());
        if (mandatoryAddons != null && mandatoryAddons.size() > 0) {
            for (IngredientProductDetail detail : mandatoryAddons) {
                list.add(addAddon(order, ProductSource.MENU, detail.getProduct().getClassification(), detail));
            }
        }
        return list;
    }

    private List<OrderItemAddon> addAddon(OrderItem order, OrderItemComposition composition) {
        List<OrderItemAddon> list = new ArrayList<>();

        if (composition != null) {
            if (composition.getVariants() != null && composition.getVariants().size() > 0) {
                for (IngredientVariantDetail detail : composition.getVariants()) {
                    list.add(addAddon(order, ProductSource.SCM, ProductClassification.VARIANT, detail));
                }
            }
            if (composition.getProducts() != null && composition.getProducts().size() > 0) {
                for (IngredientProductDetail detail : composition.getProducts()) {
                    list.add(addAddon(order, ProductSource.SCM, ProductClassification.PRODUCT_VARIANT, detail));
                }
            }
            if (composition.getAddons() != null && composition.getAddons().size() > 0) {
                for (IngredientProductDetail detail : composition.getAddons()) {
                    list.add(addAddon(order, ProductSource.MENU, detail.getProduct().getClassification(), detail));
                }
            }

            if (composition.getOptions() != null && composition.getOptions().size() > 0) {
                for (String detail : composition.getOptions()) {
                    list.add(addAddon(order, detail));
                }
            }

			/*if (composition.getOthers() != null && composition.getOthers().size() > 0) {
				for (IngredientProductDetail detail : composition.getOthers()) {
					list.add(addAddon(order, ProductSource.SCM, ProductClassification.OTHERS, detail));
				}
			}*/
        }
        return list;
    }

    private OrderItemAddon addAddon(OrderItem order, ProductSource source, ProductClassification type,
                                    IngredientProductDetail detail) {
        OrderItemAddon info = new OrderItemAddon(order, detail.getProduct().getProductId(),
                detail.getProduct().getName(), type == null ? null : type.name(), source.name(),
                detail.getDimension() == null ? null : detail.getDimension().getCode(),
                detail.getUom() == null ? null : detail.getUom().name(), detail.getQuantity(),
                AppConstants.getValue(detail.isDefaultSetting()));
        manager.persist(info);
        return info;
    }

    private OrderItemAddon addAddon(OrderItem order, String name) {
        OrderItemAddon info = new OrderItemAddon(order, -1, name, ProductClassification.FREE_OPTION.name(),
                ProductSource.OPTION.name(), "None", "PC", new BigDecimal(order.getQuantity()), "N");
        manager.persist(info);
        return info;
    }

    private OrderItemAddon addAddon(OrderItem order, ProductSource source, ProductClassification type,
                                    IngredientVariantDetail detail) {
        OrderItemAddon info = new OrderItemAddon(order, detail.getProductId(), detail.getAlias(), type.name(),
                source.name(), null, detail.getUom() == null ? null : detail.getUom().name(), detail.getQuantity(),
                AppConstants.getValue(detail.isDefaultSetting()));
        manager.persist(info);
        return info;
    }

    private OrderItemAddon addPreferenceToOrderItemAddon(OrderItem order, com.stpl.tech.kettle.domain.model.OrderItem item){
        OrderItemAddon info = new OrderItemAddon(order,item.getPreferenceDetail().getPreferenceId(),item.getPreferenceDetail().getPreferenceName(),
                item.getPreferenceDetail().getPreferenceType(),null,order.getDimension(),null,new BigDecimal(order.getQuantity()),"N");
        manager.persist(info);
        return info;
    }
    private void setTransactionDetail(OrderDetail detail, TransactionDetail transaction) {
        if (transaction.getDiscountDetail() != null) {
            if (transaction.getDiscountDetail().getDiscount() != null) {
                detail.setDiscountAmount(transaction.getDiscountDetail().getDiscount().getValue());
                detail.setDiscountPercent(transaction.getDiscountDetail().getDiscount().getPercentage());
            } else {
                detail.setDiscountAmount(BigDecimal.ZERO);
                detail.setDiscountPercent(BigDecimal.ZERO);
            }
            if (transaction.getDiscountDetail().getPromotionalOffer() != null) {
                detail.setPromotionalDiscount(transaction.getDiscountDetail().getPromotionalOffer());
            } else {
                detail.setPromotionalDiscount(BigDecimal.ZERO);
            }
            detail.setDiscountReason(transaction.getDiscountDetail().getDiscountReason());
            detail.setDiscountReasonId(transaction.getDiscountDetail().getDiscountCode());
        }
        if (transaction.getDiscountDetail() != null) {
            detail.setTotalDiscount(transaction.getDiscountDetail().getTotalDiscount()); // total
        } else {
            detail.setTotalDiscount(BigDecimal.ZERO); // total
        }

        detail.setRoundOffAmount(transaction.getRoundOffValue());

        detail.setTaxableAmount(transaction.getTaxableAmount());
        detail.setSettledAmount(transaction.getPaidAmount());
		if(Objects.nonNull(transaction.getCollectionAmount())){
			detail.setCollectionAmount(transaction.getCollectionAmount());
		} else{
			detail.setCollectionAmount(transaction.getPaidAmount());
		}
        detail.setTotalAmount(transaction.getTotalAmount());
        detail.setSaleAmount(transaction.getTotalAmount().subtract(
                transaction.getDiscountDetail() != null && transaction.getDiscountDetail().getPromotionalOffer() != null
                        ? transaction.getDiscountDetail().getPromotionalOffer()
                        : new BigDecimal(0.0D)));
        detail.setSavingAmount(transaction.getSavings());
        detail.setTaxAmount(transaction.getTax());
		if (Objects.nonNull(transaction.getServiceCharge()) && transaction.getServiceCharge().compareTo(BigDecimal.ZERO) > 0) {
			detail.setServiceCharge(transaction.getServiceCharge());
			detail.setServiceChargePercent(transaction.getServiceChargePercent());
			detail.setServiceTaxAmount(transaction.getServiceTaxAmount());
		}
    }

    private void setTaxDetail(OrderDetail detail, List<TaxDetail> taxes) {
        if (taxes == null || taxes.isEmpty()) {
            return;
        }
        for (TaxDetail tax : taxes) {
            OrderTaxDetail taxDetail = new OrderTaxDetail();
            setTaxInfo(taxDetail, tax);
            taxDetail.setOrderDetail(detail);
            manager.persist(taxDetail);
        }
        manager.flush();

    }

    private void setTaxInfo(TaxationDetailDao taxDetail, TaxDetail tax) {
        taxDetail.setTaxCode(tax.getCode());
        taxDetail.setTaxType(tax.getType());
        taxDetail.setTotalTax(tax.getValue());
        taxDetail.setTaxPercentage(tax.getPercentage());
        taxDetail.setTotalAmount(tax.getTotal());
        taxDetail.setTaxableAmount(tax.getTaxable());
    }

    private void setTaxDetail(int orderId, int stateId, OrderItem detail,
                              com.stpl.tech.kettle.domain.model.OrderItem item, Map<String, OrderItemInvoice> invoices) {
        if (item.getTaxes() == null || item.getTaxes().isEmpty()) {
            return;
        }
        for (TaxDetail tax : item.getTaxes()) {
            OrderItemTaxDetail taxDetail = new OrderItemTaxDetail();
            setTaxInfo(taxDetail, tax);
            taxDetail.setOrderItem(detail);
            manager.persist(taxDetail);
        }
        manager.flush();
        setTaxInvoices(orderId, stateId, detail, item, invoices);
    }

    private void setTaxInvoices(int orderId, int stateId, OrderItem detail,
                                com.stpl.tech.kettle.domain.model.OrderItem item, Map<String, OrderItemInvoice> invoices) {
        if (item.getTaxes() == null || item.getTaxes().isEmpty()
                || (item.getComplimentaryDetail() != null && item.getComplimentaryDetail().isIsComplimentary()
                && item.getComplimentaryDetail().getReasonCode() != AppConstants.COMPLEMENTARY_CODE_COMBO)) {
            return;
        }
        if (!invoices.containsKey(detail.getTaxCode())) {
            OrderItemInvoice invoice = new OrderItemInvoice();
            invoice.setStateId(stateId);
            invoice.setOrderId(orderId);
            invoice.setStateInvoiceId(getNextStateInvoiceId(stateId));
            invoice.setTaxableAmount(BigDecimal.ZERO);
            invoice.setTaxAmount(BigDecimal.ZERO);
            invoice.setTaxCategory(detail.getTaxCode());
            invoice.setTotalAmount(BigDecimal.ZERO);
            invoices.put(detail.getTaxCode(), invoice);
            manager.persist(invoice);
        }
        OrderItemInvoice data = invoices.get(detail.getTaxCode());
        data.setTotalAmount(data.getTotalAmount().add(item.getTotalAmount()));
        data.setTaxableAmount(data.getTaxableAmount().add(item.getAmount()));
        data.setTaxAmount(data.getTaxAmount().add(item.getTax()));
        for (TaxDetail tax : item.getTaxes()) {
            boolean found = false;
            List<OrderItemInvoiceTaxDetail> taxInvoices = data.getOrderItemInvoiceTaxes();
            for (OrderItemInvoiceTaxDetail taxInvoice : taxInvoices) {
                if (taxInvoice.getTaxCode().equals(tax.getCode()) && taxInvoice.getTaxType().equals(tax.getType())
                        && taxInvoice.getTaxPercentage().equals(tax.getPercentage())) {
                    found = true;
                    taxInvoice.setTotalAmount(taxInvoice.getTotalAmount().add(tax.getTotal()));
                    taxInvoice.setTaxableAmount(taxInvoice.getTaxableAmount().add(tax.getTaxable()));
                    taxInvoice.setTotalTax(taxInvoice.getTotalTax().add(tax.getValue()));
                    manager.flush();
                    break;
                }

            }
            if (!found) {
                OrderItemInvoiceTaxDetail newTaxInvoice = new OrderItemInvoiceTaxDetail();
                newTaxInvoice.setOrderItemInvoice(data);
                newTaxInvoice.setTaxableAmount(tax.getTaxable());
                newTaxInvoice.setTaxCode(tax.getCode());
                newTaxInvoice.setTaxPercentage(tax.getPercentage());
                newTaxInvoice.setTaxType(tax.getType());
                newTaxInvoice.setTotalAmount(tax.getTotal());
                newTaxInvoice.setTotalTax(tax.getValue());
                manager.persist(newTaxInvoice);
                data.getOrderItemInvoiceTaxes().add(newTaxInvoice);
            }
        }
        manager.flush();

    }

    private OrderDetail getOrderDetailObject(String generatedOrderId, int lastOrderId, int unitId) {
        Query query = manager.createQuery(
                "FROM OrderDetail E where E.generatedOrderId = :generatedOrderId and E.orderId > :lastOrderId and E.unitId = :unitId");
        query.setParameter("generatedOrderId", generatedOrderId);
        query.setParameter("lastOrderId", lastOrderId);
        query.setParameter("unitId", unitId);
        Object o = query.getSingleResult();
        return o == null ? null : (OrderDetail) o;
    }

    private OrderDetail getOrderDetailObject(String generatedOrderId) {
        Query query = manager.createQuery("FROM OrderDetail E where E.generatedOrderId = :generatedOrderId");
        query.setParameter("generatedOrderId", generatedOrderId);
        OrderDetail order = (OrderDetail) query.getSingleResult();
        return order;
    }

    public void generateOrderEmailEvent(OrderEmailEntryType type, int orderId, int retryCount, Customer customer,
                                        boolean isSystemGenerated, Date currentTimestamp) {
        String emailId = customer == null || customer.getEmailId() == null || customer.getEmailId().trim().length() == 0
                ? props.getUndeliveredEmail()
                : customer.getEmailId();
        boolean isEmailVerified = customer == null || customer.getEmailId() == null ? true : customer.isEmailVerified();

		if (type.equals(OrderEmailEntryType.ORDER) && Objects.nonNull(customer)
				&& (Objects.isNull(customer.getEmailId()) || Objects.isNull(customer.getContactNumber()))) {
			try {
				customeDao.updateCustomerEmail(customer, AppConstants.NO);
			} catch (DataUpdationException e) {
				LOG.error("Unable to update Email Verification Flag of Customer " + customer.getId(), e);
			}
			return;
		}
        generateOrderEmailEvent(type, orderId, retryCount, emailId, isSystemGenerated, isEmailVerified,
                currentTimestamp, customer.getContactNumber());
    }

    public void generateCancelledOrderEmailEvent(OrderEmailEntryType type, int orderId, int retryCount,
                                                 Customer customer, boolean isSystemGenerated, Date currentTimestamp, int unitId) {
        List<String> managerIds = masterCache.getEmailHeirarchy(unitId, true);
        String toEmailId = StringUtils.join(managerIds, ",");
        LOG.info("TO EMAIL IDS for Cancellation {}", toEmailId);
        boolean isEmailVerified = customer == null || customer.getEmailId() == null ? true : customer.isEmailVerified();
		if (type.equals(OrderEmailEntryType.ORDER_CANCELLATION) && Objects.nonNull(customer)
				&& (Objects.isNull(customer.getEmailId()) || Objects.isNull(customer.getContactNumber()))) {
			try {
				customeDao.updateCustomerEmail(customer, AppConstants.NO);
			} catch (DataUpdationException e) {
				LOG.error("Unable to update Email Verification Flag of Customer " + customer.getId(), e);
			}
			return;
		}
		generateOrderEmailEvent(type, orderId, retryCount, toEmailId, isSystemGenerated, isEmailVerified,
                currentTimestamp, customer.getContactNumber());
    }

    @Override
    public void generateOrderEmailEvent(OrderEmailEntryType type, int orderId, int retryCount, String emailId,
                                        boolean isSystemGenerated, boolean isEmailVerified, Date currentTimestamp, String contact) {
        OrderEmailNotification notification = new OrderEmailNotification(type.name(), orderId, retryCount,
                AppConstants.getValue(!isSystemGenerated), currentTimestamp);
        notification.setEmailAddress(emailId);
        if (contact != null) {
            notification.setContact(contact);
        }
        notification.setIsEmailDelivered("N");
        notification.setIsEmailVerified(AppConstants.getValue(isEmailVerified));
        manager.persist(notification);
        manager.flush();
    }

    public boolean updateStatus(int orderEmailId, EmailStatus status, String errorMessage) {
        try {
            Date currentTimestamp = AppUtils.getCurrentTimestamp();
            OrderEmailNotification data = manager.find(OrderEmailNotification.class, orderEmailId);
            data.setErrorMessage(errorMessage);
            data.setExecutionTime(currentTimestamp);
            if (EmailStatus.FAILED.equals(status) && data.getRetryCount() <= props.getRetryCount()) {
                generateOrderEmailEvent(OrderEmailEntryType.valueOf(data.getEntryType()), data.getOrderId(),
                        data.getRetryCount() + 1, data.getEmailAddress(),
                        !AppConstants.getValue(data.getUserRequested()),
                        AppConstants.getValue(data.getIsEmailVerified()), currentTimestamp, data.getContact());
            } else if (EmailStatus.INCORRECT_MAIL.equals(status)) {
                data.setRetryCount(props.getRetryCount() + 1);
                data.setIsEmailDelivered(AppConstants.NO);
            } else {
                data.setIsEmailDelivered(AppConstants.getValue(!EmailStatus.FAILED.equals(status)));
            }
            manager.persist(data);
            manager.flush();
            return true;
        } catch (Exception e) {
            LOG.error("Failed to update the status of Order Email Detail with productId " + orderEmailId, e);
        }
        return false;

    }

    public void addReprintRequest(int orderId, int generatedBy, int approvedBy, String reason) {
        OrderDetail detail = manager.find(OrderDetail.class, orderId);
        detail.setPrintCount(detail.getPrintCount() + 1);
        OrderRePrintDetail rePrint = new OrderRePrintDetail(detail, reason, generatedBy, approvedBy,
                AppUtils.getCurrentTimestamp());
        manager.persist(rePrint);
        manager.flush();
    }

    public OrderStatusEvent updateOrderStatus(Integer orderId, OrderStatus toStatus, int approvedBy, int generatedBy,
                                              int unitId, String reason, Boolean refund, Integer reasonId, String bookWastage) {
        Stopwatch watch = Stopwatch.createUnstarted();
        watch.start();
        int lastOrder = orderSearchDao.getLastDayCloseOrderId(unitId);
        System.out.println("######### , STEP 0, - , Get Last Order Status Value ----------,"
                + watch.stop().elapsed(TimeUnit.MILLISECONDS));
        watch.start();
        boolean lastOrderIdCheck = lastOrder > orderId.intValue();
        LOG.info("got the last order check for this order {} which is {} and last order for unit is {}", orderId,
                lastOrderIdCheck, lastOrder);
        if (!lastOrderIdCheck) {
            try {
                OrderDetail detail = manager.find(OrderDetail.class, orderId);
                //LOG.info("Order Detail is  :::: {}", detail.toString());

                //LOG.info("Inside the if check of lastOrderCheck");
                // getting current status of order placed
                OrderStatus fromStatus = OrderStatus.fromValue(detail.getOrderStatus());

                // directly send settled Cafe orders to cancelled state
                if (OrderStatus.SETTLED.equals(fromStatus) && OrderStatus.CANCELLED_REQUESTED.equals(toStatus)
                        && UnitCategory.CAFE.name().equals(detail.getOrderSource())) {
                    toStatus = OrderStatus.CANCELLED;
                }
                OrderStatusEvent ose = generateOrderStatusEvent(true, orderId, fromStatus, toStatus, approvedBy,
                        generatedBy, reason);
                System.out.println("######### , STEP 1, - , Generate Order Status Event ----------,"
                        + watch.stop().elapsed(TimeUnit.MILLISECONDS));
                if (ose != null && TransitionStatus.SUCCESS.name().equals(ose.getTransitionStatus())) {
                    if (toStatus.equals(OrderStatus.DELIVERED)) {
                        return ose;
                    }

                    if (toStatus.equals(OrderStatus.CANCELLED_REQUESTED) || toStatus.equals(OrderStatus.CANCELLED)) {
                        // on order cancellation
                        if (toStatus.equals(OrderStatus.CANCELLED_REQUESTED)) {
                            // handles all flows for cancel requested
                            detail = cancelOrder(detail, reason, approvedBy, generatedBy, refund, reasonId,
                                    bookWastage);
                        } else if (fromStatus.equals(OrderStatus.CANCELLED_REQUESTED)
                                && toStatus.equals(OrderStatus.CANCELLED)) {
                            // when status update sent via assembly or update cancel requested orders
                            detail.setCancelApprovedBy(generatedBy);
                        } else if (!OrderStatus.CANCELLED_REQUESTED.equals(fromStatus)
                                && OrderStatus.CANCELLED.equals(toStatus)) {
                            // when direct cancellation are requested
                            detail = cancelOrder(detail, reason, approvedBy, generatedBy, refund, reasonId,
                                    bookWastage);
                        }
                    }

                    detail.setOrderStatus(toStatus.value());
                    manager.merge(detail);
                    return ose;
                }

            } catch (Exception e) {
                LOG.error("Failed to update the status of Order with productId " + orderId, e);
            }
        }
        return null;
    }

    public OrderStatusEvent generateOrderStatusEvent(boolean checkExistingState, int orderId, OrderStatus fromStatus,
                                                     OrderStatus toStatus, int approvedBy, int generatedBy, String reason) {
        return generateOrderStatusEvent(checkExistingState, orderId, fromStatus, toStatus, approvedBy, generatedBy,
                null, reason);
    }

    public OrderStatusEvent generateFailureOrderStatusEvent(boolean checkExistingState, int orderId,
                                                            OrderStatus fromStatus, OrderStatus toStatus, int approvedBy, int generatedBy, String errorStatckTrace,
                                                            String reason) {
        return generateOrderStatusEvent(checkExistingState, orderId, fromStatus, toStatus, approvedBy, generatedBy,
                errorStatckTrace, reason);
    }

    public OrderStatusEvent generateOrderStatusEvent(boolean checkExistingState, int orderId, OrderStatus fromStatus,
                                                     OrderStatus toStatus, int approvedBy, int generatedBy, String errorStatckTrace, String reason) {
        Stopwatch watch = Stopwatch.createUnstarted();
        watch.start();
        boolean state = validateOrderStateTransition(fromStatus, toStatus);
        System.out.println("######### , STEP 1a, - , Validate Order Status Transition ----------,"
                + watch.stop().elapsed(TimeUnit.MILLISECONDS));
        watch.start();
        state = state & !existsOrderStatusEvent(checkExistingState, orderId, fromStatus, toStatus);
        System.out.println("######### , STEP 1b, - , Check Exists Order Status ----------,"
                + watch.stop().elapsed(TimeUnit.MILLISECONDS));
        Date currentTime = AppUtils.getCurrentTimestamp();
        watch.start();
        Date startTime = checkExistingState ? getLastOrderStatusEventTime(orderId, fromStatus, currentTime)
                : currentTime;
        System.out.println("######### , STEP 1c, - , Get Last Order Status Event ----------,"
                + watch.stop().elapsed(TimeUnit.MILLISECONDS));
        watch.start();
        OrderStatusEvent event = new OrderStatusEvent(orderId, fromStatus.name(), toStatus.name(), reason, generatedBy,
                approvedBy, startTime, currentTime,
                state ? TransitionStatus.SUCCESS.name() : TransitionStatus.FAILURE.name(), errorStatckTrace);
        manager.persist(event);
        System.out.println("######### , STEP 1d, - , Save Order Status Event ----------,"
                + watch.stop().elapsed(TimeUnit.MILLISECONDS));
        return event;
    }

    private Date getLastOrderStatusEventTime(int orderId, OrderStatus fromStatus, Date currentTime) {
        Query query = manager.createQuery(
                "select updateTime FROM  OrderStatusEvent where transitionStatus = :transitionStatus AND orderId = :orderId AND toStatus = :fromStatus");
        query.setParameter("fromStatus", fromStatus.name());
        query.setParameter("orderId", orderId);
        query.setParameter("transitionStatus", TransitionStatus.SUCCESS.name());
        try {
            Object o = query.getSingleResult();
            return o == null ? currentTime : (Date) o;
        } catch (NoResultException e) {
            return currentTime;
        } catch (NonUniqueResultException e) {
            return fixOrderStatusEvents(orderId, fromStatus, currentTime);
        }
    }

    @Override
    public Date getOrderStatusEventTime(int orderId, OrderStatus fromStatus) {
        Query query = manager.createQuery(
                "select updateTime FROM  OrderStatusEvent where transitionStatus = :transitionStatus AND orderId = :orderId AND toStatus = :fromStatus");
        query.setParameter("fromStatus", fromStatus.name());
        query.setParameter("orderId", orderId);
        query.setParameter("transitionStatus", TransitionStatus.SUCCESS.name());
        Object o = query.getSingleResult();
        return (Date) o;

    }

    @Override
    public OrderStatusEvent getLastOrderStatusEvent(int orderId) {
        Query query = manager.createQuery(
                "FROM  OrderStatusEvent where transitionStatus = :transitionStatus AND orderId = :orderId ORDER BY orderStatusId DESC");
        query.setParameter("orderId", orderId);
        query.setParameter("transitionStatus", TransitionStatus.SUCCESS.name());
        try {
            List<OrderStatusEvent> l = query.getResultList();
            return l != null && !l.isEmpty() ? l.get(0) : null;
        } catch (NoResultException e) {
            return null;
        }
    }

    private Date fixOrderStatusEvents(int orderId, OrderStatus fromStatus, Date currentTime) {

        Query query = manager.createQuery(
                "FROM OrderStatusEvent where transitionStatus = :transitionStatus AND orderId = :orderId AND toStatus = :fromStatus order by orderStatusId ");
        query.setParameter("fromStatus", fromStatus.name());
        query.setParameter("orderId", orderId);
        query.setParameter("transitionStatus", TransitionStatus.SUCCESS.name());
        @SuppressWarnings("unchecked")
        List<OrderStatusEvent> eventList = query.getResultList();

        for (int i = 1; i < eventList.size(); i++) {
            eventList.get(i).setTransitionStatus(TransitionStatus.FAILURE.name());
        }
        manager.flush();
        return eventList.get(0).getUpdateTime();
    }

    private boolean existsOrderStatusEvent(boolean checkExistingState, int orderId, OrderStatus fromStatus,
                                           OrderStatus toStatus) {
        if (checkExistingState) {
            Query query = manager.createQuery(
                    "FROM  OrderStatusEvent where transitionStatus = :transitionStatus AND orderId = :orderId AND fromStatus = :fromStatus AND toStatus = :toStatus");
            query.setParameter("fromStatus", fromStatus.name());
            query.setParameter("toStatus", toStatus.name());
            query.setParameter("orderId", orderId);
            query.setParameter("transitionStatus", TransitionStatus.SUCCESS.name());
            try {
                Object o = query.getSingleResult();
                return o != null;
            } catch (NoResultException e) {
                return false;
            } catch (NonUniqueResultException e) {
                return true;
            }
        } else {
            // should return false when no check is done
            return false;
        }
    }

    private boolean validateOrderStateTransition(OrderStatus fromStatus, OrderStatus toStatus) {
        TransitionData data = new TransitionData();
        data.setFromStateCode(fromStatus.name());
        data.setToStateCode(toStatus.name());
        StateTransitionCache.getInstance().setTransitionState(StateTransitionObject.ORDER, data);
        return TransitionStatus.SUCCESS.equals(data.getStatus());
    }

    public boolean getPaymentStatus(String cartId) {
        Query query = manager.createQuery(
                "from OrderPaymentDetail O " + " where O.cartId = :cartId" + " and O.paymentStatus = :successStatus");
        query.setParameter("successStatus", "SUCCESSFUL");
        query.setParameter("cartId", cartId);
        Object object = null;
        try {
            object = query.getSingleResult();
        } catch (NoResultException e) {
            return false;
        }
        return object == null ? false : true;
    }

    public boolean changeSettlementMode(int unitId, int orderId, int editedBy, List<Pair<Integer, Integer>> settlements)
            throws DataUpdationException {
        int lastOrderId = orderSearchDao.getLastDayCloseOrderId(unitId);
        if (orderId <= lastOrderId) {
            throw new DataUpdationException("Cannot change settlement type of older orders");
        }
        boolean changed = false;
        OrderDetail order = manager.find(OrderDetail.class, orderId);
        for (OrderSettlement settlement : order.getOrderSettlements()) {
            for (Pair<Integer, Integer> change : settlements) {
                if (settlement.getSettlementId().equals(change.getKey())) {
                    if (settlement.getPaymentModeId() != change.getValue()) {
                        settlement.setPreviousPaymentMode(settlement.getPaymentModeId());
                        settlement.setEdited(AppConstants.YES);
                        settlement.setEditTime(AppUtils.getCurrentTimestamp());
                        settlement.setEditedBy(editedBy);
                        settlement.setPaymentModeId(change.getValue());
                        changed = true;
                    }
                }
            }
        }
        return changed;
    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.kettle.data.dao.OrderManagementDao#setWastageSumoId(int,
     * int)
     */
    @Override
    public void setWastageSumoId(int orderId, int sumoId) {
        HouseCostEvent d = manager.find(HouseCostEvent.class, orderId);
        d.setWastageSumoId(sumoId);
        manager.flush();
    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.kettle.data.dao.OrderManagementDao#addCost(com.stpl.tech.
     * kettle.domain.model.Order, java.util.Collection)
     */
    @Override
    public int addCost(Order o, Collection<Consumable> values) {
        HouseCostEvent event = new HouseCostEvent();
        event.setKettleOrderId(o.getOrderId());
        if (TransactionUtils.isSpecialOrder(o)) {
            Integer reasonId = -1;
            for (com.stpl.tech.kettle.domain.model.OrderItem item : o.getOrders()) {
                if (item.getComplimentaryDetail().getReasonCode() != AppConstants.COMPLEMENTARY_CODE_COMBO) {
                    reasonId = item.getComplimentaryDetail().getReasonCode();
                    break;
                }
            }
            event.setComplimentaryReasonId(reasonId);
            event.setWastageType("COMPLETE_WASTAGE");
            if (o.getLinkedOrderId() != null) {
                try {
                    OrderDetail oldOrder = manager.find(OrderDetail.class, o.getLinkedOrderId());
                    event.setCustomerId(oldOrder.getCustomerId());
                    if (oldOrder.getCustomerId() <= 5) {
                        event.setCustomerName(oldOrder.getCustomerName());
                    } else {
                        CustomerInfo customer = manager.find(CustomerInfo.class, oldOrder.getCustomerId());
                        event.setCustomerName(customer.getFirstName());
                    }
                } catch (Exception e) {
                }
            }
        } else if (!TransactionUtils.isSpecialOrder(o) && o.getCancellationDetails() != null) {
            event.setCancelApprovedBy(o.getCancellationDetails().getApprovedBy());
            event.setCancelationReason(o.getCancellationDetails().getReason());
            event.setCancellationReasonId(o.getCancellationDetails().getReasonId());
            event.setCancelledBy(o.getCancellationDetails().getGeneratedBy());
            event.setWastageType(o.getCancellationDetails().getBookedWastage());
            event.setCustomerId(o.getCustomerId());
            event.setCustomerName(o.getCustomerName());
        } else {
            return -1;
        }
        event.setEmpId(o.getEmployeeId());
        event.setGenerationTime(AppUtils.getCurrentTimestamp());
        event.setOrderSource(o.getSource());
        event.setTotalAmount(o.getTransactionDetail().getTotalAmount());
        event.setUnitId(o.getUnitId());
        event = add(event);
        if (o.getOrderId() != null) {
            OrderDetail order = manager.find(OrderDetail.class, o.getOrderId());
            order.setWastageKettleId(event.getOrderId());
        }
        List<HouseCostItemData> items = new ArrayList<>();
        for (com.stpl.tech.kettle.domain.model.OrderItem i : o.getOrders()) {
            HouseCostItemData item = new HouseCostItemData();
            // TODO add cost as and when the order is punched, This needs to be
            // changed with price calculation module
            item.setDimension(i.getDimension());
            item.setLinkedOrderItemId(i.getItemId());
            item.setOrderDetail(event);
            item.setPrice(i.getPrice());
            item.setProductId(i.getProductId());
            item.setProductName(i.getProductName());
            item.setQuantity(new BigDecimal(i.getQuantity()));
            item.setRecipeId(i.getRecipeId());
            item.setTotalAmount(i.getTotalAmount());
            item = add(item);
            items.add(item);
        }
        event.setOrderItems(items);
        List<HouseCostConsumableData> consumables = new ArrayList<>();
        for (Consumable i : values) {
            HouseCostConsumableData item = new HouseCostConsumableData();
            // TODO add cost as and when the order is punched, This needs to be
            // changed with price calculation module
            item.setOrderDetail(event);
            item.setUom(i.getUom());
            item.setProductId(i.getProductId());
            item.setProductName(i.getName());
            item.setQuantity(i.getQuantity());
            item = add(item);
            consumables.add(item);
        }
        event.setConsumableItems(consumables);
        return event.getOrderId();
    }

    @Override
    public int remainingBillCount(int unitId, int startNo, int endNo) {
        Query query = manager.createQuery(
                "FROM OrderDetail O where O.unitId = :unitId and O.manualBillBookNo >=  :startNo and O.manualBillBookNo <=  :endNo");
        query.setParameter("unitId", unitId);
        query.setParameter("startNo", startNo);
        query.setParameter("endNo", endNo);
        return query.getResultList().size();
    }

    @Override
    public int validateBillBookNo(int unitId, int billBookNo) {
        Query query = manager
                .createQuery("FROM OrderDetail O where O.unitId = :unitId and O.manualBillBookNo = :billBookNo");
        query.setParameter("unitId", unitId);
        query.setParameter("billBookNo", billBookNo);
        return query.getResultList().size();
    }

    @Override
    public boolean outOfDeliveryOrder(int orderId) {
        OrderDetail detail = manager.find(OrderDetail.class, orderId);
        detail.setOutOfDelivery("Y");
        detail = manager.merge(detail);
        if (detail != null) {
            return true;
        }
        return false;
    }

    @Override
    public List<OrderStatusEvent> getOrderTransitionDetail(int orderId) {

        Query query = manager.createQuery("FROM OrderStatusEvent where  orderId = :orderId");
        query.setParameter("orderId", orderId);
        return query.getResultList();

    }

    @Override
    public void saveMonthlyConsumptionData(UnitBasicDetail ubd, int month, int year,
                                           Collection<OrderItemConsumable> values, String source) {

        for (OrderItemConsumable c : values) {
            OrderItemConsumableData cd = new OrderItemConsumableData();
            cd.setMenuProductDimension(c.getMenuProductDimension());
            cd.setMenuProductId(c.getMenuProductId());
            cd.setMenuProductName(c.getMenuProductName());
            cd.setMenuProductQuanity(c.getMenuProductQuanity());
            cd.setMonth(month);
            cd.setScmProductId(c.getScmProductId());
            cd.setScmProductName(c.getScmProductName());
            cd.setScmProductQuantity(c.getScmProductQuantity());
            cd.setScmProductUom(c.getScmProductUom());
            cd.setUnitId(ubd.getId());
            cd.setYear(year);
            cd.setSource(source);
            manager.persist(cd);
        }
        manager.flush();
    }

    @Override
    public void markNPSfeedbackCancelled(Integer orderId) {
        if (orderId != null) {
            if (feedbackManagementDao.cancelFeedBackforOrder(orderId, FeedbackEventType.NPS)) {

                List<String> l = new ArrayList<>();
                l.add(OrderStatus.CANCELLED.name());
                l.add(OrderStatus.CANCELLED_REQUESTED.name());
                OrderDetail o = manager.find(OrderDetail.class, orderId);
                Query query = manager.createQuery("FROM OrderDetail where orderId > :orderId "
                        + " and customerId = :customerId AND orderStatus NOT IN (:orderStatusList)");
                query.setParameter("orderId", orderId);
                query.setParameter("customerId", o.getCustomerId());
                query.setParameter("orderStatusList", l);
                query.setMaxResults(1);
                List<OrderDetail> orderList = query.getResultList();
                OrderDetail orderDetail = null;
                if (orderList != null && !orderList.isEmpty()) {
                    orderDetail = orderList.get(0);
                }
                if (orderDetail != null) {

                    Set<Integer> productIds = new HashSet<>();
                    for (OrderItem item : orderDetail.getOrderItems()) {
                        if (!AppUtils.isGiftCard(item.getTaxCode())) {
                            productIds.add(item.getProductId());
                        }
                    }
                    Customer customer;
                    try {
                        customer = customeDao.getCustomer(orderDetail.getCustomerId());
                        createNPSFeedback(productIds, customer, orderDetail, orderDetail.getBillingServerTime());
                    } catch (DataNotFoundException e) {
                        LOG.error("Data not found", e);
                    }

                }
            }
        }
    }

    @Override
    public void updateNewCustomerFlagForOrders(int unitId, Date businessDate) {
        Query query = manager.createNativeQuery("CALL MARK_CUSTOMER_AS_NEW_FOR_ORDER(:unitId, :businessDate)");
        query.setParameter("unitId", unitId);
        query.setParameter("businessDate", businessDate);
        query.executeUpdate();
    }

    @Override
    public void updateFeedbackUrl(int orderId, String feedbackUrl) {

        Query query = manager.createQuery("update OrderDetail set qrLink = :feedbackUrl where orderId = :orderId");
        query.setParameter("orderId", orderId);
        query.setParameter("feedbackUrl", feedbackUrl);
        query.executeUpdate();
    }



    @Override
    public Date getCustomerOfferLastAppliedTime(int customerId, String offerCode) {
        Query query = manager.createQuery("FROM OrderDetail where  customerId = :customerId and offerCode =:offerCode order by billGenerationTime DESC");
        query.setParameter("customerId", customerId);
        query.setParameter("offerCode", offerCode);
        List<OrderDetail> orderDetails = query.getResultList();
        if (orderDetails == null || orderDetails.size() == 0) {
            return null;
        }
        Date date = orderDetails.get(0).getBillGenerationTime();
        return date;
    }

    @Override
    public FeedbackDetail getInAppFeedback(Order order) throws DataNotFoundException {
        Set<Integer> productIds = getProductIds(order);
        Customer customer = customeDao.getCustomer(order.getCustomerId());
        FeedbackDetail feedbackDetail;
        if(orderEligibleForInAppFeedback(productIds,order)){
            if(props.getOrderFeedbackType().equals("internal")){
                feedbackDetail = createOrderFeedbackInApp(
                        productIds, customer, order.getOrderId(), order.getUnitId(),
                        order.getSource(), AppUtils.getCurrentTimestamp());
            }else{
                feedbackDetail = createNPSFeedbackInApp(productIds, customer, order.getOrderId(), order.getUnitId(),
                        order.getSource(), AppUtils.getCurrentTimestamp());
            }
            return feedbackDetail;
        }
        return null;
    }

    private boolean orderEligibleForInAppFeedback(Set<Integer> productIds, Order order) {
        if (order.getCustomerId() > 5 && AppUtils.isAppOrder(order.getChannelPartner()) && productIds.size() > 0) {
            return true;
        } else {
            return false;
        }
    }

    @Override
    public List<PartnerDataConsiderRequest> getListOfChannelPartnerOrderDetail(Date businessDate, List<Integer> brandIds, List<Integer> channelPartnerIds) {
        Query query = manager.createQuery("SELECT o.orderId,o.unitId,o.channelPartnerId,e.updateTime,o.billingServerTime,o.brandId,o.saleAmount FROM OrderDetail o, OrderStatusEvent e where o.orderId=e.orderId AND "
                + " o.billingServerTime >= :businessDate and o.orderStatus NOT IN (:orderStatus) " +
                "and o.channelPartnerId IN (:channelPartnerId) AND e.toStatus=:status and o.brandId IN(:brandId) and e.transitionStatus=:transitionstatus order by o.orderId ");
        query.setParameter("businessDate", businessDate);
        query.setParameter("orderStatus", Arrays.asList(OrderStatus.CANCELLED.value(), OrderStatus.CANCELLED_REQUESTED.value()));
        query.setParameter("channelPartnerId", channelPartnerIds);
        query.setParameter("brandId", brandIds);
        query.setParameter("status", OrderStatus.READY_TO_DISPATCH.value());
        query.setParameter("transitionstatus", TransitionStatus.SUCCESS.value());
        List<Object[]> response = query.getResultList();
        List<PartnerDataConsiderRequest> list = new ArrayList<>();
        if (response != null && response.size() > 0) {
            for (Object[] o : response) {
                list.add(new PartnerDataConsiderRequest((Integer) o[0], (Integer) o[1],
                        (Integer) o[2], (Date) o[3], (Date) o[4],
                        (Integer) o[5], (BigDecimal) o[6]));
            }
        }
        return list;

    }

    @Override
    public PartnerDataConsiderRequest getChannelPartnerOrderDetail(Integer orderId) {
        Query query = manager.createQuery("SELECT o.orderId,o.unitId,o.channelPartnerId,e.updateTime,o.billingServerTime,o.brandId,o.saleAmount FROM OrderDetail o, OrderStatusEvent e where o.orderId=e.orderId AND "
                + " o.orderId = :orderId and o.orderStatus NOT IN (:orderStatus) " +
                " AND e.toStatus=:status AND e.transitionStatus=:transitionstatus ");
        query.setParameter("orderId", orderId);
        query.setParameter("orderStatus", Arrays.asList(OrderStatus.CANCELLED.value(), OrderStatus.CANCELLED_REQUESTED.value()));
        query.setParameter("status", OrderStatus.READY_TO_DISPATCH.value());
        query.setParameter("transitionstatus", TransitionStatus.SUCCESS.value());
        List<Object[]> response = query.getResultList();
        List<PartnerDataConsiderRequest> list = new ArrayList<>();
        if (response != null && response.size() > 0) {
            for (Object[] o : response) {
                list.add(new PartnerDataConsiderRequest((Integer) o[0], (Integer) o[1],
                        (Integer) o[2], (Date) o[3], (Date) o[4],
                        (Integer) o[5], (BigDecimal) o[6]));
            }
        }
        return list.get(0);
    }

    @Override
    public Long checkDeliveryOrderForCustomer(Integer customerId) {
        Query query = manager.createQuery(" select count(*) FROM OrderDetail o where"
                + " o.customerId = :customerId and o.orderStatus NOT IN (:orderStatus) " +
                " AND o.orderSource=:orderSource ");
        query.setParameter("customerId", customerId);
        query.setParameter("orderStatus", Arrays.asList(OrderStatus.CANCELLED.value(), OrderStatus.CANCELLED_REQUESTED.value()));
        query.setParameter("orderSource", UnitCategory.COD.value());
//        query.setParameter("businessDate", date);
        Long response = 0L;
        try {
            response = (Long) query.getSingleResult();
        } catch (Exception e) {
            LOG.info("No record found");
        }

        return response;
    }


    @Override
    public Long checkDineInOrderForCustomer(Integer customerId, Date date) {
        Query query = manager.createQuery(" select count(*) FROM OrderDetail o where"
                + " o.customerId = :customerId and o.orderStatus NOT IN (:orderStatus) " +
                " AND o.orderSource<>:orderSource AND businessDate>=:businessDate");
        query.setParameter("customerId", customerId);
        query.setParameter("orderStatus", Arrays.asList(OrderStatus.CANCELLED.value(), OrderStatus.CANCELLED_REQUESTED.value()));
        query.setParameter("orderSource", UnitCategory.COD.value());
        query.setParameter("businessDate", date);
        Long response = 0L;
        try {
            response = (Long) query.getSingleResult();
        } catch (Exception e) {
            LOG.info("No record found");
        }

        return response;
    }


    public CustomerBrandMapping checkIsNewCustomerBrandWise(Integer customerId, Integer brandId) {
        Query query = manager.createQuery("FROM CustomerBrandMapping o where"
                + " o.customerId = :customerId and o.brandId =:brandId ");
        query.setParameter("customerId", customerId);
        query.setParameter("brandId", brandId);
        List<CustomerBrandMapping> customerBrandMapping = query.getResultList();
        if (customerBrandMapping != null && !customerBrandMapping.isEmpty()) {
            return customerBrandMapping.get(0);
        } else {
            return new CustomerBrandMapping(customerId, brandId, 0,0);
        }
    }

    @Override
    public List<MenuProductCostData> getCogsData(Integer unitId, Integer closureId) {
        Query query = manager.createQuery("FROM MenuProductCostData o where"
                + " o.unitId = :unitId and o.closureId =:closureId ");
        query.setParameter("unitId", unitId);
        query.setParameter("closureId", closureId);
        List<MenuProductCostData> list = query.getResultList();
        return list;

    }


    @Override
    public void deleteCogsDrillDown(Integer menuProductCostId) throws DataUpdationException {
        try {
            Query query = manager.createQuery("DELETE FROM MenuProductCogsDrilldown o where"
                    + " o.menuProductCostData.detailId = :menuProductCostId");
            query.setParameter("menuProductCostId", menuProductCostId);
            query.executeUpdate();
        } catch (Exception e) {
            throw new DataUpdationException("Error while deleting cogs drilldown Data " + e.getMessage());
        }
    }

    @Override
    public void deleteMenuCogsData(List<Integer> detailId) throws DataUpdationException {
        try {
            Query query = manager.createQuery("DELETE FROM MenuProductCostData o where"
                    + " o.detailId IN :detailId");
            query.setParameter("detailId", detailId);
            query.executeUpdate();
        } catch (Exception e) {
            throw new DataUpdationException("Error while deleting  menu cogs  Data " + e.getMessage());
        }
    }

    @Override
    public List<Integer> unsettledKettleOrdersDetailList(List<Integer> ids) {
        Query query = manager.createQuery("select E.orderId FROM OrderDetail E where E.orderId in (:ids) and E.orderStatus = :status");
        query.setParameter("ids", ids);
        query.setParameter("status", OrderStatus.SETTLED.name());
//        LOG.info("IN UNSETTLED KETTLE ORDER");
        try {
            List<Integer> orderIds = (List<Integer>) query.getResultList();
            return orderIds;
        } catch (Exception e) {
            LOG.info("Error while fetching order detail for order id");
        }
        return null;
    }

    @Override
    public OrderDetail getOrderDetailByGeneratedOrderId(String generatedOrderId) {
        Query query = manager.createQuery("FROM OrderDetail E where E.generatedOrderId =:generatedOrderId and E.orderType in (:orderType)");
        query.setParameter("generatedOrderId", generatedOrderId);
        List<String> orderType = new ArrayList();
        orderType.add("order");
        orderType.add("paidEmployeeMeal");
        query.setParameter("orderType",orderType);
        try {
            OrderDetail orderDetail= (OrderDetail) query.getSingleResult();
            LOG.info("***************************************{}",orderDetail);
            return orderDetail;
        } catch (Exception e) {
            LOG.info("Error while fetching order detail for order id");
        }
        return null;
    }

//    @Override
//    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
//    public OrderInvoiceDetail saveOrderInvoiceDetail(RequestInvoiceDetail requestInvoiceDetail){
//
//        LOG.info("{}",orderInvoiceDetail);
//        orderManagementDao.add(orderInvoiceDetail);
//        manager.persist(orderInvoiceDetail);
//        manager.merge(orderInvoiceDetail);
//        manager.flush();
//        return orderInvoiceDetail;
//    }
    @Override
    public int getNextStateInvoiceId(String stateCode, String financialYear) {

        Query query = manager.createQuery("FROM InvoiceSequenceId E where E.stateCode = :stateCode and E.financialYear = :financialYear");
        query.setParameter("financialYear", financialYear);
        query.setParameter("stateCode", stateCode);
        InvoiceSequenceId invoiceSequenceId=null;

        try {
            invoiceSequenceId = (InvoiceSequenceId) query.getSingleResult();
        } catch (NoResultException e) {
            invoiceSequenceId=addInvoiceSequenceId(stateCode,financialYear);
        }
        int currentValue = invoiceSequenceId.getNextValue();
        invoiceSequenceId.setNextValue(currentValue + 1);
        return currentValue;
    }

        private InvoiceSequenceId addInvoiceSequenceId(String stateCode, String financialYear) {
            InvoiceSequenceId invoiceSequenceId = new InvoiceSequenceId(stateCode, financialYear, 1);
            manager.persist(invoiceSequenceId);
            return invoiceSequenceId;
        }

   @Override
    public OrderInvoiceDetail getInvoiceDetail(String orderId){
        Query query = manager.createQuery("FROM OrderInvoiceDetail E where E.orderId =:orderId ");
        query.setParameter("orderId", orderId);
        try {
            OrderInvoiceDetail orderInvoiceDetail= (OrderInvoiceDetail) query.getSingleResult();
            LOG.info("***************************************{}",orderInvoiceDetail);
            return orderInvoiceDetail;
        } catch (Exception e) {
            LOG.info("Error while fetching order invoice detail for order id");
        }
        return null;
    }

    @Override
    public boolean alreadyAvailedOffer(Integer customerId, Integer campaignId) {
        Query query = manager.createQuery("FROM CustomerCampaignOfferDetail c WHERE c.customerId = :customerId " +
                "AND c.campaignId = :campaignId");
        query.setParameter("customerId",customerId);
        query.setParameter("campaignId",campaignId);
        List<CustomerCampaignOfferDetail> list = query.getResultList();
        return list.size()>0;
    }

    @Override
    public LoyaltyScore getCustomerLoyaltyScore(Integer customerId) {
        try {
            Query query = manager.createQuery("FROM LoyaltyScore LS WHERE LS.customerId = :customerId");
            query.setParameter("customerId",customerId);
            LoyaltyScore score = (LoyaltyScore) query.getSingleResult();
            return score;
        }catch (NoResultException | NonUniqueResultException e){
            LOG.error("Error while getting Loyalty Score for customer id : {}",customerId,e);
        }
        return null;
    }

    @Override
    public List<Object[]> getDayCloseEstimatesData(List<Integer> fountain9UnitIds, Date startDate, Date endDate) {
        try {
            Query query = manager.createNativeQuery("SELECT * FROM DAY_CLOSE_ESTIMATE_DATA WHERE BUSINESS_DATE >=:startDate AND BUSINESS_DATE <=:endDate AND UNIT_ID IN(:fountain9UnitIds)");
            query.setParameter("startDate",startDate).setParameter("endDate",endDate).setParameter("fountain9UnitIds",fountain9UnitIds);
            return (List<Object[]>)query.getResultList();
        }
        catch (Exception e){
            LOG.error("Error Occurred while getting day close estimate data :: ",e);
        }
        return new ArrayList<>();
    }

	@Override
	public Boolean updateOrderStatusByKettleAdmin(String generatedOrderId){
		try{
			Query updateOrderDetail = manager.createQuery("UPDATE OrderDetail od SET od.orderStatus = :settled WHERE od.generatedOrderId = :orderId");
			updateOrderDetail.setParameter("orderId",generatedOrderId);
			updateOrderDetail.setParameter("settled",OrderStatus.SETTLED.name());
			int updatedRow = updateOrderDetail.executeUpdate();
			return updatedRow > 0;
		}catch(Exception e){
			LOG.error("Error Occurred while getting Order details for status update  for generatedOrderId:: {}", generatedOrderId,e);
		}
		return false;
	}

	@Override
	public GameLeaderBoard getActiveLeaderboardEntry(int id, Integer campaignId) {
		try{
			Query query = manager.createQuery("FROM GameLeaderBoard g WHERE g.campaignId = :campaignId and g.customerId = :customerId");
			query.setParameter("campaignId",campaignId);
			query.setParameter("customerId",id);
			List<GameLeaderBoard> data = query.getResultList();
			if(data.size() >0){
				return data.get(0);
			}
		}catch(Exception e){
			LOG.error("Error while getting game leader board detail for campaign id : {}, customer id : {}",campaignId, id,e);
		}
		return null;
	}

	@Override
	public void addReferralScore(String refCode, Integer campaignId) {
		try {
			Query query = manager.createNativeQuery("UPDATE  GAME_LEADER_BOARD g SET g.REF_SCORE = ( g.REF_SCORE + :value) ," +
					" g.TOTAL_SCORE = ( g.TOTAL_SCORE + :value) WHERE g.REF_CODE = :refCode AND g.CAMPAIGN_ID = :campaignId");
			query.setParameter("value",2);
			query.setParameter("refCode",refCode);
			query.setParameter("campaignId",campaignId);
			query.executeUpdate();
		}catch (Exception e){
			LOG.error("Error while updating score via referall for ref code : {}",refCode);
		}
	}

	@Override
	public List<GameLeaderBoardDTO> getTop10Score(Integer campaignId) {
		try{
			Query query = manager.createQuery("FROM GameLeaderBoard g where g.campaignId = :campaignId order by g.totalScore desc");
			query.setParameter("campaignId",campaignId);
			query.setMaxResults(10);
			return convertToLeaderBoardDTO(query.getResultList());
		}catch (Exception e){
			LOG.error("Error while fetching top 10 score for campaignId : {}",campaignId,e);
		}
		return new ArrayList<>();
	}

	private List<GameLeaderBoardDTO> convertToLeaderBoardDTO(List<GameLeaderBoard> leaderBoardList){
		List<GameLeaderBoardDTO> dtos = new ArrayList<>();
		for(GameLeaderBoard board : leaderBoardList){
			dtos.add(GameLeaderBoardDTO.builder()
					.refCode(board.getRefCode())
					.refScore(board.getRefScore())
					.gameScore(board.getGameScore())
					.name(board.getUserName())
					.totalScore(board.getTotalScore()).build());
		}
		return dtos;
	}

	@Override
	public Integer getRankForContact(String contactNumber, Integer campaignId, Integer score) {
		try{
			Query query = manager.createNativeQuery("SELECT COUNT(*) FROM GAME_LEADER_BOARD g where g.TOTAL_SCORE >= :score AND " +
					"g.CAMPAIGN_ID = :campaignId");
			query.setParameter("score", score);
			query.setParameter("campaignId", campaignId);
			Object data = query.getSingleResult();
			return Integer.valueOf(String.valueOf(data));
		}catch(Exception e){
			LOG.error("Error while getting rank of customer for contact number : {}",contactNumber);
		}
		return null;
	}

	@Override
	public String getValidRefCode(List<String> refCodeList, Integer campaignId) {
		try {
			Query query = manager.createQuery("FROM GameLeaderBoard g where g.refCode in (:refCodeList) and g.campaignId = :campaignId");
			query.setParameter("refCodeList",refCodeList);
			query.setParameter("campaignId",campaignId);
			List<GameLeaderBoard> dataList = query.getResultList();
			if(dataList.isEmpty()){
				return refCodeList.get(0);
			}
			for(GameLeaderBoard leaderBoard : dataList){
				for(String refCode : refCodeList){
					if(!leaderBoard.getRefCode().equals(refCode)){
						return refCode;
					}
				}
			}
		}catch (Exception e){
			LOG.error("Error while fetching valid refCode for refCodeList : {}", JSONSerializer.toJSON(refCodeList),e);
		}
		return null;
	}

	@Override
	public List<OrderItem> getOrderItems(Set <Integer> orderItemsIds) {
		try {
			Query query = manager.createQuery("FROM OrderItem WHERE orderItemId IN (:orderItemsIds)");
			query.setParameter("orderItemsIds", orderItemsIds);
			return query.getResultList();
		} catch (Exception e) {
			LOG.error("Error while fetching order items");
		}
		return new ArrayList<>();
	}

	@Override
	public List<MonthlyAOVDetail> getPartnerBrandAndMonthWiseAOVData(PartnerAOVRequest partnerAOVRequest) {
		List<MonthlyAOVDetail> monthlyAOVDetailList = new ArrayList<>();
		try{
			Date calculationStartTime = AppUtils.getCurrentTimestamp();
			Query query= manager.createQuery("SELECT AVG(O.taxableAmount), O.brandId FROM OrderDetail O WHERE O.channelPartnerId= :channelPartnerId  AND O.orderStatus = :orderStatus AND O.billingServerTime BETWEEN :startDate AND :endDate GROUP BY O.brandId");
			query.setParameter("channelPartnerId", partnerAOVRequest.getPartnerId());
			query.setParameter("startDate", partnerAOVRequest.getStartDate());
			query.setParameter("endDate", partnerAOVRequest.getEndDate());
			query.setParameter("orderStatus","SETTLED");
			List<Object[]> list = query.getResultList();
			if(Objects.nonNull(list) && !list.isEmpty()){
				for(Object [] record : list){
					monthlyAOVDetailList.add(convertToMonthlyAOVDetailDto(record, partnerAOVRequest,calculationStartTime));
				}
			}
			return monthlyAOVDetailList;
		}catch(Exception e){
			LOG.error("Error while AOV calculation for startdate :::{} and endDate :: {}",partnerAOVRequest.getStartDate(),partnerAOVRequest.getEndDate(), e);
			return null;
		}
	}

//	@Override
//	public boolean addRidersDelayReason(DelayReason delayReason, OrderInfoCache ordersCache) throws DataNotFoundException, TemplateRenderingException {
//		try{
//			Query query = manager.createQuery("FROM PartnerOrderRiderStatesDetail P WHERE P.partnerOrderId = :orderId order by 1 desc");
//			query.setParameter("orderId",delayReason.getOrderId());
//			query.setMaxResults(1);
//			List<PartnerOrderRiderStatesDetail> partnerOrderRiderStatesDetails = query.getResultList();
//			if (!partnerOrderRiderStatesDetails.isEmpty()) {
//				PartnerOrderRiderStatesDetail partnerOrderRiderStatesDetail = partnerOrderRiderStatesDetails.get(0);
//				if (Objects.nonNull(partnerOrderRiderStatesDetail)){
//					partnerOrderRiderStatesDetail.setReasonsForDelay(delayReason.getReason());
//					manager.merge(partnerOrderRiderStatesDetail);
//					OrderInfo info = ordersCache.getOrderById(partnerOrderRiderStatesDetail.getKettleOrderId(), UnitCategory.DELIVERY);
//					if (Objects.nonNull(info.getPartnerOrderRiderStates()) && !info.getOrder().getStatus().equals(OrderStatus.SETTLED)) {
//						info.getPartnerOrderRiderStates().setDelayReason(delayReason.getReason());
//						ordersCache.addToCache(info);
//					}
//				}
//			} else {
//				LOG.error("Unable to update reason for delay for orderId:{}", delayReason.getOrderId());
//				OrderDetail orderDetail = getOrderByPartnerOrderId(delayReason.getOrderId());
//				OrderInfo info = ordersCache.getOrderById(orderDetail.getOrderId(), UnitCategory.DELIVERY);
//				PartnerOrderRiderStatesDetail partnerOrderRiderStatesDetail = new PartnerOrderRiderStatesDetail();
//				partnerOrderRiderStatesDetail.setBillingServerTime(info.getOrder().getBillingServerTime());
//				partnerOrderRiderStatesDetail.setKettleOrderId(info.getOrder().getOrderId());
//				partnerOrderRiderStatesDetail.setPartnerName(info.getChannelPartner().getName());
//				partnerOrderRiderStatesDetail.setPartnerOrderId(info.getOrder().getSourceId());
//				partnerOrderRiderStatesDetail.setReasonsForDelay(delayReason.getReason());
//				manager.persist(partnerOrderRiderStatesDetail);
//				if(!info.getOrder().getStatus().equals(OrderStatus.SETTLED)) {
//					info.setPartnerOrderRiderStates(DataConverter.convert(partnerOrderRiderStatesDetail));
//					info.getPartnerOrderRiderStates().setDelayReason(delayReason.getReason());
//					ordersCache.addToCache(info);
//				}
//			}
//		} catch (Exception e) {
//			throw new DataNotFoundException("Error While Updating the Order");
//		}
//		return true;
//	}

	@Override
	public boolean addRidersDelayReason(DelayReason delayReason, OrderInfoCache ordersCache) throws DataNotFoundException,TemplateRenderingException {
		try {
			OrderDelayReason orderDelayReason = null;
			Query query1 = manager.createQuery("FROM OrderDelayReason O WHERE O.partnerOrderId = :orderId order by 1 desc");
			query1.setParameter("orderId", delayReason.getOrderId());
			query1.setMaxResults(1);
			List<OrderDelayReason> orderDelayReasons = query1.getResultList();
			if (!orderDelayReasons.isEmpty()) {
				orderDelayReason = orderDelayReasons.get(0);
			}
			OrderDetail orderDetail = getOrderByPartnerOrderId(delayReason.getOrderId());
			OrderInfo info = ordersCache.getOrderById(orderDetail.getOrderId(), UnitCategory.DELIVERY);
			if (delayReason.getReasonType().equals("RIDER")) {
				Query query = manager.createQuery("FROM PartnerOrderRiderStatesDetail P WHERE P.partnerOrderId = :orderId order by 1 desc");
				query.setParameter("orderId", delayReason.getOrderId());
				query.setMaxResults(1);
				List<PartnerOrderRiderStatesDetail> partnerOrderRiderStatesDetails = query.getResultList();

				if (!partnerOrderRiderStatesDetails.isEmpty()) {
					PartnerOrderRiderStatesDetail partnerOrderRiderStatesDetail = partnerOrderRiderStatesDetails.get(0);
					if (Objects.nonNull(partnerOrderRiderStatesDetail)) {
						if (Objects.isNull(orderDelayReason)) {
							orderDelayReason = new OrderDelayReason();
							orderDelayReason.setOrderId(partnerOrderRiderStatesDetail.getKettleOrderId());
							orderDelayReason.setPartnerOrderId(partnerOrderRiderStatesDetail.getPartnerOrderId());
							orderDelayReason.setPartnerOrderName(partnerOrderRiderStatesDetail.getPartnerName());
							orderDelayReason.setBillingServerTime(partnerOrderRiderStatesDetail.getBillingServerTime());
						}
						orderDelayReason.setRiderName(partnerOrderRiderStatesDetail.getRiderName());
						orderDelayReason.setRiderContact(partnerOrderRiderStatesDetail.getRiderContact());
					}
				} else {
					if (Objects.isNull(orderDelayReason)) {
						orderDelayReason = new OrderDelayReason();
						orderDelayReason.setOrderId(info.getOrder().getOrderId());
						orderDelayReason.setPartnerOrderId(delayReason.getOrderId());
						orderDelayReason.setPartnerOrderName(info.getChannelPartner().getName());
						orderDelayReason.setBillingServerTime(info.getOrder().getBillingServerTime());
					}
				}
					orderDelayReason.setRiderDelayReason(delayReason.getReason());
					orderDelayReason.setOptionalRiderDelay(delayReason.getOptionalReason());
					orderDelayReason.setRiderDelayUpdatedBy(delayReason.getUpdatedBy());
					orderDelayReason.setRiderDelayUpdatedAt(AppUtils.getCurrentTimestamp());
					orderDelayReason.setRiderDelaySource(delayReason.getSource());
				   manager.merge(orderDelayReason);
				if(!info.getOrder().getStatus().equals(OrderStatus.SETTLED)){
                       info.setOrderDelayReason(OrderDelayReasonMapper.INSTANCE.toDomain(orderDelayReason));
					   ordersCache.addToCache(info);

				}
				return true;
			}
			if (delayReason.getReasonType().equals("CAFE")) {
				if (Objects.isNull(orderDelayReason)) {
					orderDelayReason = new OrderDelayReason();
					orderDelayReason.setBillingServerTime(info.getOrder().getBillingServerTime());
					orderDelayReason.setOrderId(info.getOrder().getOrderId());
					orderDelayReason.setPartnerOrderId(delayReason.getOrderId());
					orderDelayReason.setPartnerOrderName(info.getChannelPartner().getName());
				}
				orderDelayReason.setUnitId(info.getOrder().getUnitId());
				orderDelayReason.setUnitName(info.getOrder().getUnitName());
				orderDelayReason.setCafeDelayReason(delayReason.getReason());
				orderDelayReason.setOptionalCafeDelay(delayReason.getOptionalReason());
				orderDelayReason.setCafeDelayUpdatedBy(delayReason.getUpdatedBy());
				orderDelayReason.setCafeDelayUpdatedAt(AppUtils.getCurrentTimestamp());
				orderDelayReason.setCafeDelaySource(delayReason.getSource());
				manager.merge(orderDelayReason);
				if(!info.getOrder().getStatus().equals(OrderStatus.SETTLED)){
					info.setOrderDelayReason(OrderDelayReasonMapper.INSTANCE.toDomain(orderDelayReason));
					ordersCache.addToCache(info);
				}
				return true;
			}
		} catch (DataNotFoundException | TemplateRenderingException e) {
			LOG.error("Unable to save delay reason",e);
		}
		return false;
	}

	private MonthlyAOVDetail convertToMonthlyAOVDetailDto(Object[] record, PartnerAOVRequest partnerAOVRequest,Date calculationStartTime) {
		 return MonthlyAOVDetail.builder().aov(
				 BigDecimal.valueOf((Double) record[0])).brandId((Integer) record[1]).month(AppUtils.getMonth(partnerAOVRequest.getStartDate()))
				.year(AppUtils.getYear(partnerAOVRequest.getStartDate())).calculatedAt(AppUtils.getCurrentTimestamp()).calculationStartTime(calculationStartTime).build();
	}

	@Override
	public List<OrderDetail> getOrdersCommissionNotCalculated(String startDate, String endDate){

	try {
		Query query = manager.createNativeQuery("SELECT * FROM ORDER_DETAIL WHERE ORDER_ID NOT IN (SELECT KETTLE_ORDER_ID FROM ORDER_COMMISSION) AND  BILLING_SERVER_TIME >= :startDate AND  BILLING_SERVER_TIME <= :endDate   AND CHANNEL_PARTNER_ID IN (6,3)", OrderDetail.class);
		query.setParameter("startDate", startDate+" 05:00:00");
		query.setParameter("endDate", endDate+" 05:00:00");
		return query.getResultList();
		}catch (NoResultException ex){
		LOG.error("######## Cannot found missed order detail from commission ##########");
	   return new ArrayList<>();
	}

	}

	@Override
	public List<OrderPaymentDetailData> getOrderPaymentDetailByBatch(Integer customerId, Integer orderId, String paymentSource, String contactNumber, Integer startPosition) {
		try{
			Integer batchSize = 10;
			StringBuffer bufferedQuery = new StringBuffer("FROM OrderPaymentDetail P where ");
			if(customerId != null){
				bufferedQuery.append("P.customerId =:customerId");
			}
			if(orderId !=null){
				bufferedQuery.append("P.orderId =:orderId");
			}
			if(contactNumber != null){
				bufferedQuery.append("P.contactNumber =:contactNumber");
			}
			if(paymentSource !=null){
				if(customerId!=null && contactNumber!=null && orderId!=null){
					bufferedQuery.append("and P.paymentSource =:paymentSource");
				}else {
					bufferedQuery.append("P.paymentSource =:paymentSource");
				}
			}

			bufferedQuery.append(" order by 1 desc");

			Query query = manager.createQuery(bufferedQuery.toString());
			if(customerId != null) {
				query.setParameter("customerId", customerId);
			}
			if(orderId !=null) {
				query.setParameter("orderId", orderId);
			}
			if(contactNumber != null) {
				query.setParameter("contactNumber", contactNumber);
			}
			if(paymentSource !=null) {
				query.setParameter("paymentSource", paymentSource);
			}
			query.setFirstResult((startPosition-1)*batchSize);
			query.setMaxResults(batchSize);
			List<OrderPaymentDetail> details = query.getResultList();
			if(details.size() != 0) {
				List<OrderPaymentDetailData> paymentDetailData = new ArrayList<>();
				for (OrderPaymentDetail detail : details) {
					String generatedOrderId = null;
					if(Objects.nonNull(detail.getOrderId())){
						generatedOrderId = customeDao.getOrderDetail(detail.getOrderId()).getGeneratedOrderId();
					}
					OrderPaymentDetailData data = OrderPaymentDetailMapper.INSTANCE.toDomain(detail);
					data.setGeneratedOrderId(generatedOrderId);
					paymentDetailData.add(data);
				}
				return paymentDetailData;
			}
			else{
				LOG.error("No data found for customerId :{},orderId :{},contactNumber :{},paymentSource :{}"
						,customerId,orderId,contactNumber,paymentSource);
				return null;
			}

		} catch (Exception e) {
			LOG.error("Unable for find order payment detail for customerId :{},orderId :{},contactNumber :{},paymentSource :{} beacause :{}"
			,customerId,orderId,contactNumber,paymentSource,e);
		}
		return null;
	}

	@Override
	public List<OrderPaymentDetailData> getOrderPaymentDetailBySourceAndStatusByBatch(String paymentSource, String paymentStatus, Date startDate) {
		try{
			OrderDelayReason orderDelayReason = null;
			Query query = manager.createQuery("FROM OrderPaymentDetail O WHERE " +
					"O.paymentSource = :paymentSource and O.paymentStatus =:paymentStatus" +
					" and O.requestTime >= :startDate" +
					" and O.requestTime < :endDate" +
					" order by 1 desc");
			query.setParameter("paymentSource", paymentSource);
			query.setParameter("paymentStatus", paymentStatus);
			query.setParameter("startDate", AppUtils.addHoursToDate(AppUtils.getCurrentTimestamp(), -24));
			query.setParameter("endDate", AppUtils.getCurrentTimestamp());
			if (Objects.nonNull(startDate)) {
				query.setParameter("startDate", startDate);
				query.setParameter("endDate", AppUtils.addDays(startDate, 7));
			}
			List<OrderPaymentDetail> details = query.getResultList();
			if(!details.isEmpty()) {
				List<OrderPaymentDetailData> paymentDetailData = new ArrayList<>();
				for (OrderPaymentDetail detail : details) {
					String generatedOrderId = null;
					if(Objects.nonNull(detail.getOrderId())){
						generatedOrderId = customeDao.getOrderDetail(detail.getOrderId()).getGeneratedOrderId();
					}
					OrderPaymentDetailData data = OrderPaymentDetailMapper.INSTANCE.toDomain(detail);
					data.setGeneratedOrderId(generatedOrderId);
					paymentDetailData.add(data);
				}
				return paymentDetailData;
			}
			else{
				LOG.error("No data found for paymentSource :{} and paymentStatus :{}", paymentSource, paymentStatus);
				return null;
			}

		} catch (Exception e) {
			LOG.error("Unable for find order payment detail for paymentSource :{}  and paymentStatus :{} beacause :{}", paymentSource, paymentStatus, e);
		}
		return null;
	}

	@Override
	public OrderItemStatus getOrderItemStatusByOrderItemId(Integer orderItemId){
		try {
			StringBuilder bufferedQuery = new StringBuilder("FROM OrderItemStatus O WHERE O.orderItemId =:orderItemId");
			Query query = manager.createQuery(bufferedQuery.toString());
			query.setParameter("orderItemId",orderItemId);
			return (OrderItemStatus) query.getSingleResult();
		}catch (Exception e){
			LOG.error("No Order Item Status Found For Order Item Id ::::: {} ", orderItemId);
		}
		return null;
	}

	@Override
	public List<OrderStatusEvent> getLastOrderTransactionEvent(int orderId) {
		Query query = manager.createQuery("FROM OrderStatusEvent where orderId = :orderId ORDER BY orderStatusId DESC");
		query.setParameter("orderId", orderId);
		query.setMaxResults(1);
		return (List<OrderStatusEvent>) query.getResultList();
	}

	@Override
	public List<OrderRefundDetail> getOrderRefundByUnitId(Integer unitId, Date startDate){
		Query query = manager.createQuery("FROM OrderRefundDetail o where o.unitId = :unitId "+
				" and o.creationTime >= :startDate" +
				" and o.creationTime < :endDate" +
				" order by 1 desc");
		query.setParameter("unitId", unitId);
		query.setParameter("startDate", startDate);
		query.setParameter("endDate", AppUtils.addDays(startDate, 7));
		return (List<OrderRefundDetail>) query.getResultList();
	}
}
