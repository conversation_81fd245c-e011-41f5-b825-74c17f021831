/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.service.model;

import com.stpl.tech.kettle.domain.model.NextOffer;
import com.stpl.tech.util.PrintType;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class OrderResponse {

	private Integer orderId;

	private String generatedOrderId;

	private boolean billIncludedInReceipts;

	private List<String> receipts;

	private List<String> additionalReceipts;

	private List<String> giftCards;

	private PrintType printType;

	private Date billingServerTime;

	private NextOffer nextOffer;

	private NextOffer nextDeliveryOffer;

	private Boolean revalidationSuccess = true;

	private String revalidationFailedReason;

	public OrderResponse(Integer orderId,String generatedOrderId,boolean billIncludedInReceipts, List<String> receipts, List<String> additionalReceipts,
			List<String> giftCards, PrintType printType, Date billingServerTime) {
		super();
		this.orderId = orderId;
		this.generatedOrderId = generatedOrderId;
		this.receipts = receipts;
		this.additionalReceipts = additionalReceipts;
		this.billIncludedInReceipts = billIncludedInReceipts;
		this.giftCards = giftCards;
		this.printType = printType;
		this.billingServerTime = billingServerTime;
	}

	public OrderResponse(Boolean revalidationSuccess, String revalidationFailedReason) {
		this.revalidationSuccess = revalidationSuccess;
		this.revalidationFailedReason = revalidationFailedReason;
	}

	public OrderResponse() {
		super();
	}

	public String getGeneratedOrderId() {
		return generatedOrderId;
	}

	public void setGeneratedOrderId(String generatedOrderId) {
		this.generatedOrderId = generatedOrderId;
	}

	public List<String> getReceipts() {
		return receipts;
	}

	public void setReceipts(List<String> receipts) {
		this.receipts = receipts;
	}

	public boolean isBillIncludedInReceipts() {
		return billIncludedInReceipts;
	}

	public void setBillIncludedInReceipts(boolean billIncludedInReceipts) {
		this.billIncludedInReceipts = billIncludedInReceipts;
	}

	public List<String> getGiftCards() {
		if(giftCards == null){
			giftCards = new ArrayList<>();
		}
		return giftCards;
	}

	public void setGiftCards(List<String> giftCards) {
		this.giftCards = giftCards;
	}

	public PrintType getPrintType() {
		return printType;
	}

	public void setPrintType(PrintType printType) {
		this.printType = printType;
	}

	public Date getBillingServerTime() {
		return billingServerTime;
	}

	public void setBillingServerTime(Date billingServerTime) {
		this.billingServerTime = billingServerTime;
	}

	public NextOffer getNextOffer() {
		return nextOffer;
	}

	public void setNextOffer(NextOffer nextOffer) {
		this.nextOffer = nextOffer;
	}

	public List<String> getAdditionalReceipts() {
		return additionalReceipts;
	}

	public void setAdditionalReceipts(List<String> additionalReceipts) {
		this.additionalReceipts = additionalReceipts;
	}

	public NextOffer getNextDeliveryOffer() {
		return nextDeliveryOffer;
	}

	public void setNextDeliveryOffer(NextOffer nextDeliveryOffer) {
		this.nextDeliveryOffer = nextDeliveryOffer;
	}

	public Integer getOrderId() {
		return orderId;
	}

	public void setOrderId(Integer orderId) {
		this.orderId = orderId;
	}

	public Boolean getRevalidationSuccess() { return revalidationSuccess; }

	public void setRevalidationSuccess(Boolean revalidationSuccess) { this.revalidationSuccess = revalidationSuccess; }

	public String getRevalidationFailedReason() { return revalidationFailedReason; }

	public void setRevalidationFailedReason(String revalidationFailedReason) { this.revalidationFailedReason = revalidationFailedReason; }
}
