package com.stpl.tech.kettle.service.controller;


import com.stpl.tech.kettle.core.TransactionUtils;
import com.stpl.tech.kettle.core.service.PosMetadataService;
import com.stpl.tech.kettle.service.model.StockReferenceData;
import com.stpl.tech.kettle.stock.service.AutomatedStockEventReport;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.service.AbstractExceptionHandler;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.servlet.View;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static com.stpl.tech.kettle.service.core.ServiceConstants.API_VERSION;
import static com.stpl.tech.kettle.service.core.ServiceConstants.SEPARATOR;
import static com.stpl.tech.kettle.service.core.ServiceConstants.STOCK_EVENT_REPORT_ROOT_CONTEXT;

@Controller
@RequestMapping(API_VERSION + SEPARATOR + STOCK_EVENT_REPORT_ROOT_CONTEXT)
public class AutomatedStockEventReportController extends AbstractExceptionHandler {
    private static final Logger LOG = LoggerFactory.getLogger(AutomatedStockEventReportController.class);

    @Autowired
    private AutomatedStockEventReport automatedStockEventReport;

    @Autowired
    private MasterDataCache masterCache;

    @Autowired
    PosMetadataService posMetadataService;

    @RequestMapping(method = RequestMethod.POST, value = "stock-data")
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public View getEventStockReport(@RequestBody StockReferenceData requestedStockData) {
        LOG.info("Creating Stock Report");
        try {
            View view = automatedStockEventReport.executeForDownload(AppUtils.getDate(requestedStockData.getStartDate() + " 00:00:00.0", "yyyy-MM-dd HH:mm:ss.SSS"),
                    AppUtils.getDate(requestedStockData.getEndDate() + " 23:59:59.0", "yyyy-MM-dd HH:mm:ss.SSS"), requestedStockData.getUnitId(),
                    AppUtils.getDate(requestedStockData.getEndDate() + " 00:00:00.0", "yyyy-MM-dd HH:mm:ss.SSS"), true);
            LOG.info("Received the View Object");
            return view;
        } catch (Exception e) {
            LOG.info("cauth error:::::::", e);
            return null;
        }
    }

    @RequestMapping(method = RequestMethod.POST, value = "/get-stock-data")
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean getEventStockReport(@RequestParam String startDate,
                                       @RequestParam(required = false) String endDate , @RequestParam Integer partnerData) {
        LOG.info("Creating Stock Report");
        for (Date date : AppUtils.getDaysBetweenDates(AppUtils.getDate(startDate + " 00:00:00.0", "yyyy-MM-dd HH:mm:ss.SSS"),
                AppUtils.getDate(endDate + " 23:59:59.0", "yyyy-MM-dd HH:mm:ss.SSS"), false)) {
            Map<Integer, List<Date>> unitTimeMap = null;
            if(partnerData == 0) {
                unitTimeMap = posMetadataService.getUnitClosingTimeMap(date);
                if (unitTimeMap == null || unitTimeMap.isEmpty()) {
                    LOG.info("Skipping Stock Out Report Generation for date {}", date);
                    continue;
                }
            }
            List<Date> partnerHardCodedTimeChaayos = new ArrayList<>();
            partnerHardCodedTimeChaayos.add(AppUtils.getDate(AppUtils.getDateString(date) + " 08:00:00.0", "yyyy-MM-dd HH:mm:ss.SSS"));
            partnerHardCodedTimeChaayos.add(AppUtils.getDate(AppUtils.getDateString(date) + " 22:00:00.0", "yyyy-MM-dd HH:mm:ss.SSS"));

            List<Date> partnerHardCodedTimeGnT = new ArrayList<>();
            partnerHardCodedTimeGnT.add(AppUtils.getDate(AppUtils.getDateString(date) + " 11:00:00.0", "yyyy-MM-dd HH:mm:ss.SSS"));
            partnerHardCodedTimeGnT.add(AppUtils.getDate(AppUtils.getDateString(date) + " 23:59:59.0", "yyyy-MM-dd HH:mm:ss.SSS"));
            for (UnitBasicDetail unit : masterCache.getAllUnits()) {
                try {
                    LOG.info("Creating Report For Unit {} for Date {}", unit.getId(), AppUtils.getSQLFormattedDate(date));
                    if (TransactionUtils.isActiveUnit(unit.getStatus()) && unit.isLive()
                            && UnitCategory.CAFE.equals(unit.getCategory())) {
                        if(partnerData == 0) {
                            automatedStockEventReport.execute(date, unit.getId(), true, unitTimeMap.get(unit.getId()), false, AppConstants.CHAAYOS_BRAND_ID);
                            automatedStockEventReport.execute(date, unit.getId(), true, unitTimeMap.get(unit.getId()), false, AppConstants.GNT_BRAND_ID);
                        }
                        else {
                            automatedStockEventReport.execute(date, unit.getId(), true, partnerHardCodedTimeChaayos, true, AppConstants.CHAAYOS_BRAND_ID);
                            automatedStockEventReport.execute(date, unit.getId(), true, partnerHardCodedTimeGnT, true, AppConstants.GNT_BRAND_ID);
                        }
                    }
                } catch (Exception e) {
                    LOG.error("Exception Caught While Generating report for unit {}::", unit.getId(), e);
                }
            }
        }
        return true;
    }
}
