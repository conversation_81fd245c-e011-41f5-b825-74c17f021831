/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';

var formsApp = angular.module('formsApp', [
    'ngAnimate',
    'ngCookies',
    'ngResource',
    'ngSanitize',
    'ui.router',
    'ui.bootstrap',
    'ui.select2',
    'toastr',
    'angularjs-dropdown-multiselect'
]);

formsApp.config(['$stateProvider', '$urlRouterProvider',
    function ($stateProvider, $urlRouterProvider) {
        $stateProvider
            .state('login', {
                url: '/login',
                templateUrl: window.version + '/views/login.html',
                controller: 'loginCtrl'
            })
            .state('auto-login', {
                url: '/auto-login',
                templateUrl: window.version + '/views/autoLogin.html',
                controller: 'autoLoginCtrl'
            })
            .state('dashboard', {
                url: '/dashboard',
                templateUrl: window.version + '/views/dashboard.html',
                controller: 'dashboardCtrl',
            })
            .state('dashboard.home', {
                url: '/home',
                templateUrl: window.version + '/views/home.html',
                controller: 'homeCtrl',
            })
            .state('dashboard.forms', {
                url: '/forms',
                templateUrl: window.version + '/views/forms.html',
                params: {formType: null},
                controller: 'formsCtrl',
            })
            .state('dashboard.audit', {
                url: '/audit',
                templateUrl: window.version + '/views/audit.html',
                controller: 'auditCtrl',
            })
            .state('dashboard.searchAudit', {
                url: '/searchAudit',
                templateUrl: window.version + '/views/searchAudit.html',
                controller: 'searchAuditCtrl',
            }).state('dashboard.expense', {
            url: '/expense',
            templateUrl: window.version + '/views/expenseTracking.html',
            params: {expenseType: null, viewType: null},
            controller: 'expenseTrackingController',
        }).state('dashboard.warningDetail', {
            url: '/warningDetail',
            templateUrl: window.version + '/views/warningDetail.html',
            params: {viewType: null, audit: null, warningId: null, actionBy: null, searchParams: null},
            controller: 'warningDetailCtrl',
        }).state('dashboard.searchWarningDetail', {
            url: '/searchWarningDetail',
            templateUrl: window.version + '/views/searchWarningDetail.html',
            params: {searchParams: null},
            controller: 'searchWarningDetailCtrl',
        }).state('dashboard.createVoucher', {
            url: '/createVoucher',
            templateUrl: window.version + '/views/createVoucher.html',
            controller: 'createVoucherCtrl',
        }).state('dashboard.manageVoucher', {
            url: '/manageVoucher',
            templateUrl: window.version + '/views/manageVoucher.html',
            params: {actionBy: null},
            controller: 'manageVoucherCtrl',
        }).state('dashboard.requestClaim', {
            url: '/requestClaim',
            templateUrl: window.version + '/views/requestClaim.html',
            params: {actionBy: null},
            controller: 'requestClaimCtrl',
        }).state('dashboard.manageClaim', {
            url: '/manageClaim',
            templateUrl: window.version + '/views/manageClaim.html',
            params: {actionBy: null},
            controller: 'manageClaimCtrl',
        }).state('dashboard.manageWallet', {
            url: '/manageWallet',
            templateUrl: window.version + '/views/manageWallet.html',
            params: {actionBy: null},
            controller: 'manageWalletCtrl',
        }).state('dashboard.manageBatchCodes', {
            url: '/manageBatchCodes',
            templateUrl: window.version + '/views/batchCodeManagement.html',
            params: {searchParams: null},
            controller: 'batchCodeManagementCtrl',
        }).state('dashboard.createPnLAdjusment', {
            url: '/createPnLAdjusment',
            templateUrl: window.version + '/views/createPnLAdjusment.html',
            params: {actionBy: null},
            controller: 'createPnlAdjusmentCtrl',
        }).state('dashboard.processPnLAdjustment', {
            url: '/processPnLAdjustment',
            templateUrl: window.version + '/views/processPnLAdjustment.html',
            params: {actionBy: null},
            controller: 'processPnLAdjustmentCtrl',
        });

        $urlRouterProvider.otherwise('/dashboard');

    }])
    .service('authInterceptor', ['$rootScope', '$location', '$q', function ($rootScope, $location, $q) {
        var service = this;
        service.request = function (config) {
            config.headers.auth = $rootScope.auth;
            if (config.method == "POST" && config.data == undefined) {
                config["data"] = {};
            }
            return config;
        };
        service.responseError = function (response) {
            $rootScope.showFullScreenLoader = false;
            var url = $location.absUrl().split('?')[0];
            if ((url.indexOf('createVoucher') < 0 || url.indexOf('requestClaim') < 0) && response.status === 401) {
                $location.path('/login');
            }
            return $q.reject(response.data);
        };
    }])
    .config(['$httpProvider', function ($httpProvider) {
        $httpProvider.interceptors.push('authInterceptor');
    }])
    .run(['$rootScope', '$state', '$interval', '$stateParams', '$location', '$cookieStore', '$http', 'AppUtil',
        function ($rootScope, $state, $interval, $stateParams, $location, $cookieStore, $http, AppUtil) {
            $rootScope.rootLoading = false;
            $rootScope.$state = $state;
            $rootScope.$stateParams = $stateParams;
            $rootScope.$on('$locationChangeStart', function (event, next, current) {
                var restrictedPage = $.inArray($location.path(), ['/login', '/auto-login']) === -1;
                var body = document.getElementsByTagName("BODY")[0];
                var currentUrl = $location.url();
                var loggedIn = ($rootScope.auth != null) && !angular.isUndefined($rootScope.auth);
                if (restrictedPage && !loggedIn) {
                    $location.path('/login');
                } else {
                    if ($location.path().length) {
                        AppUtil.previousLocation.push($location.path());
                    } else {
                        $location.path(AppUtil.previousLocation[AppUtil.previousLocation.length - 1]);
                    }
                }
            });
        }])
    .filter('yesNo', [function () {
        return function (input) {
            return input == undefined || input == null || !input ? 'no' : 'yes';
        };
    }]).directive("aclMenu", ['$rootScope', function ($rootScope) {
    function link(scope, element, attributes) {
        var aclData = $rootScope.aclData;
        if (aclData != null) {
            if (aclData.menu != null && aclData.menu[attributes.aclMenu] != null) {
                element.show();
            } else {
                element.hide();
            }
        } else {
            $rootScope.logout(true);
        }
        /*scope.$watch(attributes.acl, function(value, oldValue) {
         heart(value);
         }, true);*/
    }

    return ({
        link: link,
        restrict: "A"
    });
}]).directive("aclSubMenu", ['$rootScope', function ($rootScope) {
    function link(scope, element, attributes) {
        var aclData = $rootScope.aclData;
        if (aclData != null) {
            if (aclData.subMenu != null && aclData.subMenu[attributes.aclSubMenu] != null) {
                element.show();
            } else {
                element.hide();
            }
        } else {
            $rootScope.logout(true);
        }
    }

    return ({
        link: link,
        restrict: "A"
    });
}]).directive("aclAction", ['$rootScope', function ($rootScope) {
    function link(scope, element, attributes) {
        var aclData = $rootScope.aclData;
        if (aclData != null) {
            if (aclData.action != null && aclData.action[attributes.aclAction] != null) {
                element.show();
            } else {
                element.hide();
            }
        } else {
            $rootScope.logout(true);
        }
    }

    return ({
        link: link,
        restrict: "A"
    });
}]).directive('fileModel', ['$parse', function ($parse) {
    return {
        restrict: 'A',
        link: function (scope, element, attrs) {
            var model = $parse(attrs.fileModel);
            var modelSetter = model.assign;

            element.bind('change', function () {
                scope.$apply(function () {
                    modelSetter(scope, element[0].files[0]);
                });
            });
        }
    };
}]).filter('strReplace', function () {
    return function (input, from, to) {
        input = input || '';
        from = from || '';
        to = to || '';
        return input.replace(new RegExp(from, 'g'), to);
    };
}).directive('stringToNumber', function () {
    return {
        require: 'ngModel',
        link: function (scope, element, attrs, ngModel) {
            ngModel.$parsers.push(function (value) {
                return '' + value;
            });
            ngModel.$formatters.push(function (value) {
                return parseFloat(value, 10);
            });
        }
    };
});
